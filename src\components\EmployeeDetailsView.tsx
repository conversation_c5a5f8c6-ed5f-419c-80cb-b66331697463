import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiRefreshCcw, FiUsers, FiAlertCircle } from 'react-icons/fi';
import { useAppContext } from '../context/AppContext';
import { useEmployeeViewStore } from '../store/employeeViewStore';
import YourDetails from './YourDetails';
import { EmployeeDetails as EmployeeDetailsType } from '../types';

interface EmployeeDetailsViewProps {
  employeeDetails: any; // API employee details
  canSwitchToAdmin: boolean;
  onSwitchToAdmin: () => void;
}

const EmployeeDetailsView: React.FC<EmployeeDetailsViewProps> = ({
  employeeDetails,
  canSwitchToAdmin,
  onSwitchToAdmin
}) => {
  const { isAdminViewingAsEmployee } = useEmployeeViewStore();
  const [isAdmin, setIsAdmin] = useState(false);
  
  // Create placeholder details from API data
  const placeholderDetails: EmployeeDetailsType = {
    fullName: `${employeeDetails?.firstName || ''} ${employeeDetails?.middleName || ''} ${employeeDetails?.lastName || ''}`.trim(),
    dateOfBirth: '',
    ssn: employeeDetails?.ssn || null,
    address: employeeDetails?.userAddress ? 
      `${employeeDetails.userAddress.address1}, ${employeeDetails.userAddress.city}, ${employeeDetails.userAddress.state} ${employeeDetails.userAddress.zipcode}` : '',
    businessPhone: employeeDetails?.phoneNumber || '',
    workEmail: employeeDetails?.email || '',
    username: '',
    password: '',
    businessEmailMasked: '',
    businessPhoneMasked: '',
    employmentType: employeeDetails?.WorkerType?.WorkerType || '',
    jobTitle: employeeDetails?.jobTitle || '',
    compensation: employeeDetails?.userWages && employeeDetails.userWages.length > 0 ? 
      `$${employeeDetails.userWages[0].WageRate}/${employeeDetails.userWages[0].WageBasis?.WageBasis || 'year'}` : '',
    startDate: employeeDetails?.dateOfJoin ? new Date(employeeDetails.dateOfJoin).toLocaleDateString() : '',
    workLocation: employeeDetails?.companyLocation ? employeeDetails.companyLocation.companyLocation : ''
  };
  
  useEffect(() => {
    // Check if user is admin viewing as employee
    const storedRole = localStorage.getItem('currentRole');
    setIsAdmin(storedRole === 'admin-as-employee');
  }, []);

  // Check if we have employee details
  const hasEmployeeData = !!employeeDetails && Object.keys(employeeDetails).length > 0;

  return (
    <div className="flex-1 p-8 bg-gray-50">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-medium">Employee Details</h1>
        {canSwitchToAdmin && (
          <button
            onClick={onSwitchToAdmin}
            className="flex items-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <FiRefreshCcw className="mr-2" />
            Switch to Admin
          </button>
        )}
      </div>
      
      {hasEmployeeData ? (
        // Use the YourDetails component with both the placeholder and API data
        <YourDetails 
          employeeDetails={placeholderDetails} 
          apiEmployeeDetails={employeeDetails} 
        />
      ) : (
        // Show a message if no employee data is available
        <div className="bg-white p-8 rounded-md shadow-sm text-center">
          <FiAlertCircle className="mx-auto text-yellow-500 text-5xl mb-4" />
          <h2 className="text-xl font-medium text-gray-700 mb-2">No Employee Data Available</h2>
          <p className="text-gray-500 mb-4">
            We couldn't retrieve the employee details at this time. This could be due to:
          </p>
          <ul className="text-gray-500 list-disc list-inside mb-6 text-left max-w-md mx-auto">
            <li>The employee ID may be invalid</li>
            <li>There might be an issue with the API connection</li>
            <li>The employee record may not exist in the system</li>
          </ul>
          <p className="text-gray-500">
            Please try refreshing the page or contact support if the issue persists.
          </p>
        </div>
      )}
    </div>
  );
};

export default EmployeeDetailsView;
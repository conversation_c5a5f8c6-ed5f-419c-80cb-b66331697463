import { SystemPrompt } from '@copilotkit/shared';
import { copilotService } from '../../services/copilotService';

if (!process.env.GROQ_API_KEY) {
  throw new Error('GROQ_API_KEY is not set in environment variables');
}


const hiringSystemPrompt = `You are a helpful Payroll AI Agent that assists with adding new employees. 

Important: You can determine today's date. Use new Date() to get the current date when needed.

Follow these steps precisely:

1. When a user wants to add an employee, gather information ONE field at a time in this order:

   First ask: "What is the employee's first name?"
   After getting first name, ask: "What is {firstName}'s last name?"
   After email, ask: "What is {firstName}'s email address?"
   After phone, ask: "What is {firstName}'s phone number? (10 digits)"
   After date, ask: "What is {firstName}'s start date? (YYYY-MM-DD)"
   After worker type, ask: "Is {firstName} a W2 employee or 1099 contractor?"
   If 1099, ask: "What is the rate type (hourly, daily, or project)?"
   If 1099, ask: "What is the rate amount?"
   After job title, ask: "What is {firstName}'s job title?"
   After job title, ask: "Will {firstName} be Remote or Office?"
   Finally, ask: "What state will {firstName} be working from? (2-letter code)"

2. Validation Rules:
   - Phone must be exactly 10 digits
   - Email must be valid format
   - Date must be YYYY-MM-DD and not in the future
   - Worker Type must be W2 or 1099
   - For 1099: Must include rate type and rate amount
   - Location must be Remote or Office
   - State must be 2-letter code

4. For date validation:
   - Use JavaScript's new Date() to get today's date
   - Format dates as YYYY-MM-DD
   - Compare dates properly accounting for timezone differences
   - Clearly communicate today's date in responses

4. If any validation fails:
   - Explain the error clearly
   - Ask for the correct information
   - Do not proceed until fixed

Example conversation:
User: "I need to add a new employee"
Assistant: "I'll help you add a new employee. What is the employee's first name?"
User: "John"
Assistant: "What is John's last name?"
[continue with each field]

Never ask for multiple pieces of information at once. Collect one field at a time.`;

const systemPrompt = {
  content: hiringSystemPrompt,
  role: 'system'
};

export async function handler(req: Request) {
  try {
    const body = await req.json();
    const { messages } = body;
    const lastMessage = messages[messages.length - 1].content;

    // Get full context for better responses
    const context = await copilotService.getFullContext();

    // Check if this is an employee addition request
    if (lastMessage.toLowerCase().includes('add') && lastMessage.toLowerCase().includes('employee')) {
      // Extract employee data from the conversation
      const employeeData = extractEmployeeData(lastMessage);
      
      // If we have complete employee data, try to add the employee
      if (isCompleteEmployeeData(employeeData)) {
        try {
          // Execute the ADD_EMPLOYEE action
          const result = await executeAction('ADD_EMPLOYEE', employeeData);
          
          return new Response(JSON.stringify({ 
            response: `✅ Successfully added ${employeeData.firstName} ${employeeData.lastName} as a ${employeeData.jobTitle}.`
          }));
        } catch (error) {
          console.error('Error adding employee:', error);
          return new Response(JSON.stringify({ 
            response: `⚠️ Error: Failed to add employee. ${error instanceof Error ? error.message : 'Please try again.'}`
          }));
        }
      }
    }

    // Default response using Groq
    const completion = await groqClient.chat.completions.create({
      model: "llama3-70b-8192",
      messages: [
        systemPrompt,
        ...messages,
        { role: 'system', content: `Current context:
Route: ${context.route}
Today's Date: ${new Date().toISOString().split('T')[0]}
Company: ${context.company?.company || 'None selected'}
Employees: ${context.employeeData?.employees.length || 0}
Contractors: ${context.employeeData?.contractors.length || 0}

Please handle any errors gracefully and provide clear error messages to the user.`
        }
      ]
    });

    return new Response(JSON.stringify({ 
      response: completion.choices[0].message.content 
    }));

  } catch (error) {
    console.error('Copilot error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return new Response(JSON.stringify({ 
      response: `⚠️ Error: ${errorMessage}\n\nPlease try again with valid information.`
    }));
  }
}

async function executeAction(action: string, actionData: any) {
  console.log(`🔄 Executing ${action}:`, actionData);
  
  try {
    let result;
    switch (action) {
      case 'ADD_EMPLOYEE':
        console.log('📡 Adding new employee:', actionData);
        
        // Get company ID from localStorage or context
        let companyId;
        try {
          const selectedCompanyStr = localStorage.getItem('selectedCompany');
          if (selectedCompanyStr) {
            const selectedCompany = JSON.parse(selectedCompanyStr);
            companyId = selectedCompany.companyID;
          }
        } catch (error) {
          console.warn('⚠️ Could not get company ID from localStorage:', error);
          // Try to get from context if available
          companyId = actionData.companyId;
        }
        
        if (!companyId) {
          throw new Error('Company ID is required');
        }
        
        // Format the data according to the exact API format provided
        const userData = {
          companyId: companyId,
          userReferenceId: "",
          firstName: actionData.firstName,
          middleName: actionData.middleName || "",
          lastName: actionData.lastName,
          email: actionData.email,
          phoneNumber: actionData.phoneNumber?.replace(/\D/g, '') || '',
          dateOfJoin: actionData.dateOfJoin,
          workerType: actionData.workerType,
          jobTitle: actionData.jobTitle,
          companyLocationCategory: actionData.companyLocationCategory || "Remote",
          stateCode: actionData.stateCode?.toUpperCase() || actionData.code?.toUpperCase(),
          companyLocationId: ""
        };
        
        // Get the auth token
        const token = localStorage.getItem('auth_token');
        if (!token) {
          throw new Error('Authentication token not found');
        }
        
        // Make the API call using the exact format from the curl example
        const response = await fetch('https://sandbox.rollfi.xyz/adminPortal#addUser', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            method: 'addUser',
            user: userData
          })
        });
        
        // Check if the request was successful
        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ API error response:', errorText);
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }
        
        result = await response.json();
        console.log('✅ API success response:', JSON.stringify(result, null, 2));
        break;
      
      // Other cases remain unchanged
      case 'GET_USERS':
        console.log('📡 Fetching all users');
        result = await copilotService.getUsers();
        break;
      case 'GET_USER':
        console.log(`📡 Fetching user by ID: ${actionData.id}`);
        result = await copilotService.getUserById(actionData.id);
        break;
      case 'CREATE_USER':
        console.log('📡 Creating new user:', actionData);
        result = await copilotService.createUser(actionData);
        break;
      case 'UPDATE_USER':
        console.log(`📡 Updating user ${actionData.id}:`, actionData);
        result = await copilotService.updateUser(actionData.id, actionData);
        break;
      case 'DELETE_USER':
        console.log(`📡 Deleting user: ${actionData.id}`);
        result = await copilotService.deleteUser(actionData.id);
        break;
      case 'GET_EMPLOYEES':
        console.log('📡 Fetching all employees');
        result = await copilotService.getEmployees();
        break;
      case 'UPDATE_EMPLOYEE':
        console.log(`📡 Updating employee ${actionData.id}:`, actionData);
        result = await copilotService.updateEmployee(actionData.id, actionData);
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }
    console.log(`✅ Action ${action} completed successfully:`, result);
    return result;
  } catch (error) {
    console.error(`❌ Error executing ${action}:`, error);
    throw error;
  }
}

async function getRelevantContext(messages: any[], action?: string) {
  try {
    const context: any = {
      timestamp: new Date().toISOString(),
      today: new Date().toISOString().split('T')[0], // Add today's date in YYYY-MM-DD format
      action: action || 'chat',
    };

    // If the message is about employee/user count, always fetch the data
    if (messages.some(m => 
      m.content.toLowerCase().includes('how many') || 
      m.content.toLowerCase().includes('count') ||
      m.content.toLowerCase().includes('number of')
    )) {
      const users = await copilotService.getUsersByCompany();
      context.userCount = users.length;
    }

    return context;
  } catch (error) {
    console.error('Error getting context:', error);
    return {
      timestamp: new Date().toISOString(),
      today: new Date().toISOString().split('T')[0], // Include even in error case
      action: action || 'chat',
      error: 'Failed to load some context data'
    };
  }
}

function validateEmployeeData(data: any): string[] {
  const missingFields = [];
  
  // Validate each field one at a time
  if (!data.firstName) {
    return ["Please provide the employee's first name"];
  }
  if (!data.lastName) {
    return ["Please provide the employee's last name"];
  }
  if (!data.email) {
    return ["Please provide the employee's email address"];
  }
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    return ["Please provide a valid email address"];
  }
  if (!data.phoneNumber || !/^\d{10}$/.test(data.phoneNumber.replace(/\D/g, ''))) {
    return ["Please provide a valid 10-digit phone number"];
  }
  if (!data.dateOfJoin || !/^\d{4}-\d{2}-\d{2}$/.test(data.dateOfJoin)) {
    return ["Please provide a valid start date in YYYY-MM-DD format"];
  }
  if (!data.workerType || !['W2', '1099'].includes(data.workerType.toUpperCase())) {
    return ["Please specify if the employee is W2 or 1099"];
  }
  if (!data.jobTitle) {
    return ["Please provide the employee's job title"];
  }
  if (!data.companyLocationCategory || !['Office', 'Remote'].includes(data.companyLocationCategory)) {
    return ["Please specify if the employee is Remote or Office"];
  }
  if (!data.code || !/^[A-Z]{2}$/.test(data.code.toUpperCase())) {
    return ["Please provide a valid 2-letter state code"];
  }

  return missingFields;
}

async function updateUserField(userId: string, field: string, value: any) {
  try {
    // First fetch existing user data
    const currentUser = await copilotService.getUserById(userId);
    
    // Create update payload with only the changed field
    const updatePayload = {
      userId,
      ...currentUser,
      [field]: value
    };

    // Validate the specific field
    const validationError = validateSingleField(field, value);
    if (validationError) {
      throw new Error(validationError);
    }

    const result = await copilotService.updateUser(userId, updatePayload);
    return result;
  } catch (error) {
    console.error(`Error updating ${field}:`, error);
    throw error;
  }
}

function validateSingleField(field: string, value: any): string | null {
  switch (field) {
    case 'email':
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return 'Invalid email format';
      }
      break;
    case 'phoneNumber':
      if (!/^\d{10}$/.test(value.replace(/\D/g, ''))) {
        return 'Phone number must be 10 digits';
      }
      break;
    case 'companyLocationCategory':
      if (!['Remote', 'Office'].includes(value)) {
        return 'Location must be Remote or Office';
      }
      break;
    case 'workerType':
      if (!['W2', '1099'].includes(value)) {
        return 'Worker type must be W2 or 1099';
      }
      break;
    case 'code':
      if (!/^[A-Z]{2}$/.test(value.toUpperCase())) {
        return 'State code must be 2 letters';
      }
      break;
  }
  return null;
}

// Helper function to check if we have complete employee data
function isCompleteEmployeeData(data: any): boolean {
  const requiredFields = [
    'firstName', 
    'lastName', 
    'email', 
    'phoneNumber', 
    'dateOfJoin', 
    'workerType', 
    'jobTitle', 
    'companyLocationCategory', 
    'code'
  ];
  
  // Check if all required fields are present
  const hasAllRequiredFields = requiredFields.every(field => 
    data[field] !== undefined && data[field] !== null && data[field] !== ''
  );
  
  // For W2 employees, salary is also required
  if (hasAllRequiredFields && data.workerType === 'W2') {
    return data.salary !== undefined && data.salary !== null && data.salary > 0;
  }
  
  return hasAllRequiredFields;
}

// Improve the extractEmployeeData function to better parse conversation data
function extractEmployeeData(message: string): any {
  const data: any = {};
  
  // Extract first name
  const firstNameMatch = message.match(/first name[:\s]+([A-Za-z]+)/i) || 
                         message.match(/name[:\s]+([A-Za-z]+)/i);
  if (firstNameMatch) data.firstName = firstNameMatch[1];
  
  // Extract last name
  const lastNameMatch = message.match(/last name[:\s]+([A-Za-z]+)/i) || 
                        message.match(/([A-Za-z]+)'s last name/i);
  if (lastNameMatch) data.lastName = lastNameMatch[1];
  
  // Extract email
  const emailMatch = message.match(/email[:\s]+([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i);
  if (emailMatch) data.email = emailMatch[1];
  
  // Extract phone number
  const phoneMatch = message.match(/phone[:\s]+(\d{10})/i) || 
                     message.match(/phone[:\s]+(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})/i);
  if (phoneMatch) data.phoneNumber = phoneMatch[1].replace(/\D/g, '');
  
  // Extract date of join
  const dateMatch = message.match(/date[:\s]+(\d{4}-\d{2}-\d{2})/i) || 
                    message.match(/start date[:\s]+(\d{4}-\d{2}-\d{2})/i);
  if (dateMatch) data.dateOfJoin = dateMatch[1];
  
  // Extract worker type
  const workerTypeMatch = message.match(/worker type[:\s]+(W2|1099)/i) || 
                          message.match(/(W2|1099) (employee|contractor)/i);
  if (workerTypeMatch) data.workerType = workerTypeMatch[1];
  
  // Extract job title
  const jobTitleMatch = message.match(/job title[:\s]+([A-Za-z\s]+)/i) || 
                        message.match(/position[:\s]+([A-Za-z\s]+)/i);
  if (jobTitleMatch) data.jobTitle = jobTitleMatch[1].trim();
  
  // Extract location
  const locationMatch = message.match(/location[:\s]+(Remote|Office|On-site)/i);
  if (locationMatch) data.companyLocationCategory = locationMatch[1];
  
  // Extract state code
  const stateMatch = message.match(/state[:\s]+([A-Z]{2})/i) || 
                     message.match(/state code[:\s]+([A-Z]{2})/i);
  if (stateMatch) data.code = stateMatch[1].toUpperCase();
  
  // Extract salary (for W2 employees)
  const salaryMatch = message.match(/salary[:\s]+\$?(\d+[,\d]*)/i) || 
                      message.match(/salary[:\s]+(\d+[,\d]*)/i);
  if (salaryMatch) data.salary = Number(salaryMatch[1].replace(/,/g, ''));
  
  return data;
}



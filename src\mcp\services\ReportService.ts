import { mcpServer } from '../MCPServer';

/**
 * Report Service
 * 
 * Handles all API operations related to reports
 */
export class ReportService {
  private static instance: ReportService;
  private endpoint = '/reports';

  private constructor() {}

  /**
   * Get the singleton instance of the Report Service
   */
  public static getInstance(): ReportService {
    if (!ReportService.instance) {
      ReportService.instance = new ReportService();
    }
    return ReportService.instance;
  }

  /**
   * Get payroll reports
   * 
   * @param companyId Company ID
   * @param startDate Start date (YYYY-MM-DD)
   * @param endDate End date (YYYY-MM-DD)
   * @returns Payroll reports
   */
  public async getPayrollReports(companyId: string, startDate: string, endDate: string) {
    return mcpServer.post(this.endpoint, 'getPayrollReports', {
      companyId,
      startDate,
      endDate
    });
  }

  /**
   * Get tax reports
   * 
   * @param companyId Company ID
   * @param year Year (YYYY)
   * @param quarter Quarter (1-4)
   * @returns Tax reports
   */
  public async getTaxReports(companyId: string, year: string, quarter: number) {
    return mcpServer.post(this.endpoint, 'getTaxReports', {
      companyId,
      year,
      quarter
    });
  }

  /**
   * Get employee reports
   * 
   * @param companyId Company ID
   * @param userId User ID (optional)
   * @returns Employee reports
   */
  public async getEmployeeReports(companyId: string, userId?: string) {
    const payload: any = {
      companyId
    };

    if (userId) {
      payload.userId = userId;
    }

    return mcpServer.post(this.endpoint, 'getEmployeeReports', payload);
  }

  /**
   * Get company reports
   * 
   * @param companyId Company ID
   * @returns Company reports
   */
  public async getCompanyReports(companyId: string) {
    return mcpServer.post(this.endpoint, 'getCompanyReports', {
      companyId
    });
  }

  /**
   * Get users by company name
   * 
   * @param companyName Company name
   * @returns List of users
   */
  public async getUsersByCompanyName(companyName: string) {
    return mcpServer.post(this.endpoint, 'getUsersByCompanyName', {
      companyName
    });
  }
}

// Export a singleton instance
export const reportService = ReportService.getInstance();
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabaseClient';

const AuthCallback = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('Authenticating...');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) throw sessionError;
        
        if (!session) {
          setMessage('No active session found. Redirecting to login...');
          setTimeout(() => navigate('/login'), 3000);
          return;
        }
        
        // Store user email in localStorage
        if (session.user.email) {
          localStorage.setItem('userEmail', session.user.email);
        }
        
        // Check if we have pending user data from signup
        const pendingUserDataStr = localStorage.getItem('pendingUserData');
        
        if (pendingUserDataStr) {
          // This is a new user completing registration
          try {
            const pendingUserData = JSON.parse(pendingUserDataStr);
            
            // Check if user already has a profile to prevent duplicates
            const { data: existingProfile, error: checkError } = await supabase
              .from('website2_users')
              .select('id')
              .eq('user_id', session.user.id)
              .maybeSingle();
            
            if (checkError && checkError.code !== 'PGRST116') {
              throw checkError;
            }
            
            // Only insert if no profile exists
            if (!existingProfile) {
              // Now that the user is authenticated, we can insert their data
              const { error: insertError } = await supabase
                .from('website2_users')
                .insert([
                  {
                    user_id: session.user.id,
                    first_name: pendingUserData.first_name || '',
                    middle_name: pendingUserData.middle_name || null,
                    last_name: pendingUserData.last_name || '',
                    phone: pendingUserData.phone || '',
                    bussiness_email: pendingUserData.bussiness_email
                  }
                ]);
              
              if (insertError) {
                console.error('Insert error:', insertError);
                throw insertError;
              }
              
              console.log('User profile created successfully');
            }
            
            // Clear the pending user data
            localStorage.removeItem('pendingUserData');
            
            // Redirect to admin page
            setMessage('Registration complete! Redirecting...');
            setTimeout(() => navigate('/admin'), 2000);
          } catch (err: any) {
            console.error('Error creating user profile:', err);
            setError(`Failed to create user profile: ${err.message}`);
            setTimeout(() => navigate('/signup'), 3000);
          }
        } else {
          // This is an existing user logging in
          try {
            // Check if the user has a profile in website2_users
            const { data: userProfile, error: profileError } = await supabase
              .from('website2_users')
              .select('*')
              .eq('user_id', session.user.id)
              .single();
            
            if (profileError) {
              // If no profile exists, redirect to signup
              if (profileError.code === 'PGRST116') {
                // Store email in localStorage for signup page
                if (session.user.email) {
                  localStorage.setItem('pendingUserData', JSON.stringify({
                    bussiness_email: session.user.email
                  }));
                }
                
                setMessage('You need to complete your profile before continuing...');
                setTimeout(() => navigate('/signup'), 2000);
                return;
              }
              throw profileError;
            }
            
            // User has a profile, redirect to admin page
            setMessage('Login successful! Redirecting...');
            setTimeout(() => navigate('/admin'), 2000);
          } catch (err: any) {
            console.error('Error checking user profile:', err);
            setError(`Authentication error: ${err.message}`);
            setTimeout(() => navigate('/login'), 3000);
          }
        }
      } catch (err: any) {
        console.error('Auth callback error:', err);
        setError(`Authentication failed: ${err.message}`);
        setTimeout(() => navigate('/login'), 3000);
      } finally {
        setLoading(false);
      }
    };

    handleAuthCallback();
  }, [navigate]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4">
      <div className="w-full max-w-md bg-white text-center">
        {error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <p>Authentication failed: {error}</p>
            <p className="mt-2">Redirecting...</p>
          </div>
        ) : (
          <>
            <h2 className="text-2xl font-bold mb-4">{message}</h2>
            {loading && (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600"></div>
              </div>
            )}
            <p className="mt-4 text-gray-600">Please wait while we process your request...</p>
          </>
        )}
      </div>
    </div>
  );
};

export default AuthCallback;
import api from './apiConfig';
import { employerService } from './employerService';
import { adminService, AdminCompanyResponse } from './adminService';
import { getAccessToken, getBasicAuthHeader } from '../utils/auth';
import axios from 'axios';

interface ContextData {
  route: string;
  company: any;
  userData: any;
  employeeData: {
    employees: any[];
    contractors: any[];
  };
  dashboardStats: {
    payroll: {
      totalPayroll: number;
      nextPayrollDate: string;
      pendingPayments: number;
      completedPayments: number;
    };
    bankBalance: number;
  };
  companyList: AdminCompanyResponse[];
}

export class CopilotService {
  async getFullContext(): Promise<ContextData> {
    try {
      const storedCompany = localStorage.getItem('selectedCompany');
      const currentRoute = window.location.pathname;
      let employees: any[] = [];
      let contractors: any[] = [];

      // Fetch users if company is selected
      if (storedCompany) {
        const company = JSON.parse(storedCompany);
        const companyId = company.companyID || company.companyId || company.id;
        if (companyId) {
          try {
            const basicAuth = getBasicAuthHeader();
            const payload = {
              method: 'getUsers',
              companyId: companyId
            };
            const { data } = await axios.post(
              `${import.meta.env.VITE_API_URL}/reports`,
              payload,
              {
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': basicAuth,
                },
              }
            );
            // API returns data.users
            if (data && Array.isArray(data.users)) {
              console.log("Users fetched for context:", data.users);
              employees = data.users.filter(
                (u: any) => u.WorkerType && (u.WorkerType.WorkerType === 'W2')
              );
              contractors = data.users.filter(
                (u: any) => u.WorkerType && (u.WorkerType.WorkerType !== 'W2')
              );
              console.log("Employees:", employees.length, "Contractors:", contractors.length);
            }
          } catch (err) {
            console.error('Error fetching users for Copilot context:', err);
          }
        }
      }

      const contextData: ContextData = {
        route: currentRoute,
        company: storedCompany ? JSON.parse(storedCompany) : null,
        userData: null,
        employeeData: {
          employees,
          contractors
        },
        dashboardStats: {
          payroll: { totalPayroll: 0, nextPayrollDate: '', pendingPayments: 0, completedPayments: 0 },
          bankBalance: 0
        },
        companyList: []
      };

      // Based on route, fetch relevant data
      if (currentRoute.includes('/admin')) {
        try {
          const companies = await adminService.getAllCompanies();
          if (companies && companies.data) {
            contextData.companyList = companies.data as any[];
          }
        } catch (error) {
          console.error('Error fetching companies:', error);
        }
      }

      // If no user is logged in, add a note
      if (!contextData.userData) {
        contextData.userData = { note: 'No user logged in' };
      }

      // Debug log before returning context
      console.log('[CopilotService] Returning context: employees:', employees.length, 'contractors:', contractors.length, 'users:', employees.concat(contractors));
      return contextData;
    } catch (error) {
      console.error('Error getting context:', error);
      return {
        route: window.location.pathname,
        company: null,
        userData: { note: 'No user logged in' },
        employeeData: {
          employees: [],
          contractors: []
        },
        dashboardStats: {
          payroll: { totalPayroll: 0, nextPayrollDate: '', pendingPayments: 0, completedPayments: 0 },
          bankBalance: 0
        },
        companyList: []
      };
    }
  }

  async getUsersByCompany() {
    const context = await this.getFullContext();
    if (!context.employeeData) {
      throw new Error('No employee data available');
    }

    return {
      total: context.employeeData.employees.length + context.employeeData.contractors.length,
      employees: context.employeeData.employees.length,
      contractors: context.employeeData.contractors.length,
      company: context.company?.company
    };
  }

  async getDashboardStats() {
    const context = await this.getFullContext();
    return context.dashboardStats;
  }

  async getCompanyList() {
    const context = await this.getFullContext();
    return context.companyList;
  }
}

export const copilotService = new CopilotService();


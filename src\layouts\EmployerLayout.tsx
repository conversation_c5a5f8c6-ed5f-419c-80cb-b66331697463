import React from 'react';
import { Outlet } from 'react-router-dom';
import EmployerSidebar from '../components/EmployerSidebar';
import { useSmartNavigation } from '../hooks/useSmartNavigation';

const EmployerLayout: React.FC = () => {
  useSmartNavigation();
  
  return (
    <div className="flex min-h-screen bg-gray-50">
      <EmployerSidebar />
      <main className="flex-1 page-transition">
        <Outlet />
      </main>
    </div>
  );
};

export default EmployerLayout;

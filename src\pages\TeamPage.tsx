import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  useCopilotReadable, 
  useCopilotAction, 
  useCopilotAdditionalInstructions 
} from '@copilotkit/react-core';
import AddTeamMemberModal from '../components/AddTeamMemberModal';
import api from '../services/apiConfig';
import { PromptManager } from '../services/promptManager';
import { employerService } from '../services/employerService';

interface TeamMember {
  userID: string;
  user: string;
  email: string;
  phoneNumber: string;
  jobTitle: string;
  dateOfJoin: string;
  workerType: {
    workerType: string;
  };
  status?: string;
}

// Add utility function for date validation
const isValidStartDate = (dateString: string): boolean => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time portion for accurate date comparison
  
  const inputDate = new Date(dateString);
  inputDate.setHours(0, 0, 0, 0);
  
  return inputDate <= today;
};

const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
};

const TeamPage: React.FC = () => {
  const navigate = useNavigate();
  const [showAddTeamMemberModal, setShowAddTeamMemberModal] = useState(false);
  const [activeTab, setActiveTab] = useState('Employees');
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const tabs = ['Employees', 'Contractors', 'Inactive employees', 'Inactive contractors'];

  // Copilot Instructions
  useCopilotAdditionalInstructions(`
    You are a helpful AI assistant for the Team Management page. You can:
    - View and search team members
    - Switch between different team member categories
    - Add new team members
    - View active and inactive team members
    - Help with team member management tasks
  `);

  // Detailed step-by-step instructions for team member management
  useCopilotAdditionalInstructions({
    instructions: `
    When collecting team member details for adding a new member:
    1. Today's date is: ${new Date().toISOString().split('T')[0]}
    2. Start date must not be after today's date
    3. Ask for and collect one field at a time in this specific order:
       - First name
       - Last name
       - Email address
       - Phone number
       - Job title
       - Worker type (W2 or 1099)
       - Rate type and amount (ONLY for 1099)
       - Start date
       - Location (Remote or Office)
       - State code

    2. Validate each field before proceeding to the next one:
       - First name: Required, no numbers, max 50 characters
       - Last name: Required, no numbers, max 50 characters
       - Email: Must be valid email format
       - Phone number: Must be 10 digits, proper format
       - Job title: Required, max 100 characters
       - Worker type: Must be either "W2" or "1099"
       - For 1099 only:
          * Must specify rate type (hourly/daily/project)
          * Rate amount must be > 0
       - Start date: Must be valid date, not in future
       - Location: Must be either "Remote" or "Office"
       - State code: Must be valid 2-letter US state code

    3. For 1099 contractors only:
       - First ask for rate type (hourly/daily/project)
       - Then ask for rate amount
       - Validate amount is greater than 0
       - Format currency values properly
       - Show confirmation of entered amount

    4. After collecting each field:
       - Confirm the entered value
       - Allow correction if needed
       - Show validation errors clearly
       - Only proceed when valid`
  });

  // Copilot Readable States
  useCopilotReadable({
    name: "teamMembers",
    description: "List of all team members with their details",
    value: teamMembers
  });

  useCopilotReadable({
    name: "teamStats",
    description: "Statistics about team composition",
    value: {
      totalMembers: teamMembers.length,
      activeEmployees: teamMembers.filter(m => m.workerType.workerType === 'W2' && m.status !== 'inactive').length,
      activeContractors: teamMembers.filter(m => m.workerType.workerType !== 'W2' && m.status !== 'inactive').length,
      inactiveMembers: teamMembers.filter(m => m.status === 'inactive').length
    }
  });

  useCopilotReadable({
    name: "currentTab",
    description: "Currently active tab in the team page",
    value: activeTab
  });

  useCopilotReadable({
    name: "currentDate",
    description: "Today's date in YYYY-MM-DD format",
    value: new Date().toISOString().split('T')[0]
  });

  // Copilot Actions
  useCopilotAction({
    name: "switchTab",
    description: "Switch to a different tab in the team page",
    parameters: [
      {
        name: "tabName",
        type: "string",
        description: "Name of the tab to switch to (Employees, Contractors, Inactive employees, Inactive contractors)"
      }
    ],
    run: async (params) => {
      if (tabs.includes(params.tabName)) {
        setActiveTab(params.tabName);
        return `Switched to ${params.tabName} tab`;
      }
      return "Invalid tab name";
    }
  });

  useCopilotAction({
    name: "searchTeamMembers",
    description: "Search for team members by name, email, or job title",
    parameters: [
      { 
        name: "searchTerm", 
        type: "string", 
        description: "Text to search for in name, email, or job title" 
      }
    ],
    handler: async (params) => {
      const results = teamMembers.filter(member => 
        member.user.toLowerCase().includes(params.searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(params.searchTerm.toLowerCase()) ||
        member.jobTitle.toLowerCase().includes(params.searchTerm.toLowerCase())
      );
      
      return results.length > 0 
        ? `Found ${results.length} matching team members:\n${results.map(m => 
            `- ${m.user} (${m.jobTitle}) - ${m.workerType.workerType}`
          ).join('\n')}`
        : "No team members found matching your search criteria";
    }
  });

  // Enhanced addNewTeamMember action with step-by-step collection
  useCopilotAction({
    name: "addNewTeamMember",
    description: "Add a new team member with step-by-step information collection",
    parameters: [
      { name: "firstName", type: "string", description: "First name (required, letters only, max 50 chars)" },
      { name: "middleName", type: "string", description: "Middle name (optional)" },
      { name: "lastName", type: "string", description: "Last name (required, letters only)" },
      { name: "email", type: "string", description: "Valid email address" },
      { name: "phoneNumber", type: "string", description: "10-digit phone number" },
      { name: "dateOfJoin", type: "string", description: "Join date (YYYY-MM-DD)" },
      { name: "workerType", type: "string", description: "Worker type (W2 or 1099)" },
      { name: "jobTitle", type: "string", description: "Job title (required)" },
      { name: "code", type: "string", description: "State code (2 letters)" }
    ],
    handler: async (params) => {
      try {
        // Input validation
        if (!params.firstName?.trim() || !params.lastName?.trim()) {
          return "First name and last name are required";
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(params.email)) {
          return "Invalid email format";
        }

        // Format phone number
        const formattedPhone = params.phoneNumber.replace(/\D/g, '');
        if (formattedPhone.length !== 10) {
          return "Phone number must be 10 digits";
        }

        // Validate date
        const formattedDate = new Date(params.dateOfJoin).toISOString().split('T')[0];
        if (!isValidStartDate(params.dateOfJoin)) {
          return "Start date cannot be in the future";
        }

        // Get selected company from localStorage
        const selectedCompanyStr = localStorage.getItem('selectedCompany');
        if (!selectedCompanyStr) {
          throw new Error('No company selected');
        }
        const selectedCompany = JSON.parse(selectedCompanyStr);

        // Create the new user object with validated data
        const newUser = {
          companyId: selectedCompany.companyID,
          userReferenceId: "",
          firstName: params.firstName.trim(),
          middleName: params.middleName?.trim() || "",
          lastName: params.lastName.trim(),
          email: params.email.trim().toLowerCase(),
          phoneNumber: formattedPhone,
          dateOfJoin: formattedDate,
          workerType: params.workerType || "W2",
          jobTitle: params.jobTitle.trim(),
          companyLocationCategory: "Remote",
          stateCode: params.code.toUpperCase(),
          code: params.code.toUpperCase(),
          companyLocationId: ""
        };

        // Log for debugging
        console.log('State Code:', params.code);
        console.log('Formatted State Code:', params.code.toUpperCase());

        const requestPayload = {
          method: "addUser",
          user: {
            ...newUser,
            stateCode: params.code.toUpperCase(),
          }
        };

        // Log the full payload
        console.log('Full Request Payload:', JSON.stringify(requestPayload, null, 2));

        const response = await api.post(
          'https://sandbox.rollfi.xyz/adminPortal',
          requestPayload
        );

        // Log the response
        console.log('Full Response:', JSON.stringify(response.data, null, 2));

        // Refresh team members list after successful addition
        await fetchTeamMembers();

        return "Successfully added new team member.";
      } catch (error) {
        console.error('Error in addNewTeamMember:', error);
        throw error;
      }
    }
  });

  // Enhanced updateTeamMember action
  useCopilotAction({
    name: "updateTeamMember",
    description: "Update existing team member details field by field",
    parameters: [
      { name: "identifier", type: "string", description: "Email or name to identify the team member" },
      { name: "field", type: "string", description: "Field to update (firstName, lastName, email, etc.)" },
      { name: "value", type: "string", description: "New value for the field" }
    ],
    handler: async (params) => {
      const member = teamMembers.find(m => 
        m.email.toLowerCase() === params.identifier.toLowerCase() ||
        m.user.toLowerCase().includes(params.identifier.toLowerCase())
      );

      if (!member) {
        return "Team member not found. Please verify the email or name.";
      }

      // Validation and update logic would go here
      return `Ready to update ${params.field} for ${member.user}. Please confirm the new value.`;
    }
  });

  useCopilotAction({
    name: "getTeamMemberDetails",
    description: "Get detailed information about a specific team member",
    parameters: [
      { 
        name: "identifier", 
        type: "string", 
        description: "Name or email of the team member" 
      }
    ],
    handler: async (params) => {
      const member = teamMembers.find(m => 
        m.user.toLowerCase().includes(params.identifier.toLowerCase()) ||
        m.email.toLowerCase().includes(params.identifier.toLowerCase())
      );

      if (!member) {
        return "Team member not found";
      }

      return `
        Team Member Details:
        Name: ${member.user}
        Email: ${member.email}
        Job Title: ${member.jobTitle}
        Type: ${member.workerType.workerType}
        Start Date: ${new Date(member.dateOfJoin).toLocaleDateString()}
        Status: ${member.status === 'inactive' ? 'Inactive' : 'Active'}
      `;
    }
  });

  useEffect(() => {
    const fetchTeamMembers = async () => {
      setLoading(true);
      try {
        const selectedCompanyStr = localStorage.getItem('selectedCompany');
        if (!selectedCompanyStr) {
          throw new Error('No company selected');
        }

        const selectedCompany = JSON.parse(selectedCompanyStr);
        
        const response = await api.post('/reports', {
          method: 'getUsersByCompanyName',
          companyName: selectedCompany.company
        });

        const data = response.data;
        
        if (data && typeof data === 'object') {
          let usersData: TeamMember[] = [];
          
          if (Array.isArray(data)) {
            usersData = data;
          } else if (Array.isArray(data.users)) {
            usersData = data.users;
          } else if (data.data && Array.isArray(data.data.users)) {
            usersData = data.data.users;
          } else if (data.data && Array.isArray(data.data)) {
            usersData = data.data;
          }

          setTeamMembers(usersData);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (err: any) {
        const errorMessage = err.response?.data?.error?.message || err.message || 'Failed to fetch team members';
        console.error('Error fetching team members:', err);
        setError(errorMessage);
        setTeamMembers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  const getFilteredTeamMembers = () => {
    return teamMembers.filter(member => {
      const isW2 = member.workerType.workerType === 'W2';
      const isActive = member.status !== 'inactive';

      switch (activeTab) {
        case 'Employees':
          return isW2 && isActive;
        case 'Contractors':
          return !isW2 && isActive;
        case 'Inactive employees':
          return isW2 && !isActive;
        case 'Inactive contractors':
          return !isW2 && !isActive;
        default:
          return false;
      }
    });
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-12 text-red-500">
          {error}
        </div>
      );
    }

    const filteredMembers = getFilteredTeamMembers();
    
    if (filteredMembers.length === 0) {
      return (
        <div className="text-center py-12 text-gray-500">
          No {activeTab.toLowerCase()} found
        </div>
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job Title</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredMembers.map((member, index) => (
              <tr key={member.userID || index}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{member.user}</div>
                  <div className="text-sm text-gray-500">{member.email}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {member.jobTitle}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(member.dateOfJoin).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    member.workerType.workerType === 'W2' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                  }`}>
                    {member.workerType.workerType === 'W2' ? 'Employee' : 'Contractor'}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold">Team Members</h1>
          <button
            onClick={() => setShowAddTeamMemberModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Add Team Member
          </button>
        </div>

        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`
                  whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        <div className="mt-6">
          {renderContent()}
        </div>

        {showAddTeamMemberModal && (
          <AddTeamMemberModal
            isOpen={showAddTeamMemberModal}
            onClose={() => setShowAddTeamMemberModal(false)}
          />
        )}
      </div>
    </div>
  );
};
export default TeamPage;

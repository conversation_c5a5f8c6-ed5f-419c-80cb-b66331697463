import React from 'react';
import { useSmartNavigation } from '../hooks/useSmartNavigation';
import { useCopilotAdditionalInstructions, useCopilotAction } from '@copilotkit/react-core';
import { useAuth } from '../context/AuthProvider';
import { copilotService } from '../services/copilotService';
import axios from 'axios';
import { getBasicAuthHeader } from '../utils/auth';

export const CopilotActionsProvider: React.FC = () => {
  const { userProfile } = useAuth();
  // Always register all actions and instructions, regardless of role
    useCopilotAdditionalInstructions({
      instructions: `
      When collecting employee details for adding a new employee:
      1. Ask for and collect one field at a time in this specific order:
         - First name
         - Last name
         - Email address
         - Phone number
         - Start date
         - Worker type (W2 or 1099)
         - Job title
         - Location (Remote or On-site)
         - State code
         - Salary (only for W2 employees)
    
      2. Validate each field before proceeding to the next one:
         - First name should not be empty
         - Last name should not be empty
         - Email should be in a valid format (e.g., <EMAIL>)
         - Phone number should be 10 digits
         - Start date should be in YYYY-MM-DD format
         - Worker type must be either W2 or 1099
         - Job title should not be empty
         - Location must be either Remote or On-site
         - State code must be 2 letters
         - Salary must be a positive number (only for W2 employees)
    
      3. After collecting all information, show a summary and ask for confirmation before submitting.
    
      4. Use a conversational tone and provide helpful guidance:
         - "What is the employee's first name?"
         - "What is [firstName]'s last name?"
         - "What is [firstName]'s email address?"
         - "What is [firstName]'s phone number? (10 digits)"
         - "What is [firstName]'s start date? (YYYY-MM-DD)"
         - "Is [firstName] a W2 employee or 1099 contractor? (Enter W2 or 1099)"
         - "What is [firstName]'s job title?"
         - "Will [firstName] be Remote or On-site? (Enter Remote or On-site)"
         - "What state will [firstName] be working from? (2-letter code)"
         - "What is [firstName]'s annual salary?" (only for W2 employees)
    
      5. If validation fails, provide clear error messages and ask for the information again:
         - "Invalid email format. Please try again."
         - "Phone number must be 10 digits. Please try again."
         - "Date must be in YYYY-MM-DD format. Please try again."
         - "Please enter either W2 or 1099."
         - "Please enter either Remote or On-site."
         - "State code must be 2 letters. Please try again."
         - "Salary must be a positive number. Please try again."

    You can also answer questions about the number of employees, contractors, or users, and display their details if asked.
      `
    });
    useCopilotAction({
      name: "addUser",
      description: "Add a new user to the system",
      parameters: [
        { name: "firstName", type: "string", description: "User's first name (max 50 characters, letters only)" },
        { name: "middleName", type: "string", description: "User's middle name (optional)" },
        { name: "lastName", type: "string", description: "User's last name" },
        { name: "email", type: "string", description: "User's email address" },
        { name: "phoneNumber", type: "string", description: "User's phone number" },
        { name: "dateOfJoin", type: "string", description: "User's join date (YYYY-MM-DD)" },
        { name: "workerType", type: "string", description: "User's worker type (W2 or 1099)" },
        { name: "jobTitle", type: "string", description: "User's job title" },
        { name: "code", type: "string", description: "Two-letter state code (Required)", required: true }
      ],
      handler: async (params) => {
        // Validate user input
        const errors = validateUserInput(params);
        if (errors.length > 0) {
          return `Validation errors: ${errors.join(', ')}`;
        }
        try {
          await addUserToSystem(params);
          return "User added successfully!";
      } catch (error: any) {
          return `Error adding user: ${error.message}`;
        }
      }
    });

  // Count Users (employees, contractors, all)
  useCopilotAction({
    name: "countUsers",
    description: "Count the number of employees, contractors, or all users.",
    parameters: [
      { name: "type", type: "string", description: "Type of user to count: employees, contractors, or all" }
    ],
    handler: async (params) => {
      const context = await copilotService.getFullContext();
      console.log('[CopilotAction] countUsers: employees:', context.employeeData.employees.length, 'contractors:', context.employeeData.contractors.length, 'employeesArray:', context.employeeData.employees, 'contractorsArray:', context.employeeData.contractors);
      if (!context.employeeData || context.employeeData.employees === undefined || context.employeeData.contractors === undefined) {
        return "Employee data is still loading. Please try again in a moment.";
      }
      const employees = context.employeeData.employees || [];
      const contractors = context.employeeData.contractors || [];
      if (params.type === 'employees') return `There are ${employees.length} employees.`;
      if (params.type === 'contractors') return `There are ${contractors.length} contractors.`;
      return `There are ${employees.length + contractors.length} users in total.`;
    }
  });

  // List Users (employees or contractors)
  useCopilotAction({
    name: "listUsers",
    description: "List all employees or contractors.",
    parameters: [
      { name: "type", type: "string", description: "Type of user to list: employees or contractors" }
    ],
    handler: async (params) => {
      const context = await copilotService.getFullContext();
      if (!context.employeeData || context.employeeData.employees === undefined || context.employeeData.contractors === undefined) {
        return `User data is still loading. Please try again in a moment.`;
      }
      const users = params.type === 'contractors' ? context.employeeData.contractors : context.employeeData.employees;
      if (!users.length) return `No ${params.type} found.`;
      return users.map(u => `${u.firstName} ${u.lastName} (${u.email}) - ${u.jobTitle}`).join('\n');
    }
  });

  // Get User Details
  useCopilotAction({
    name: "getUserDetails",
    description: "Get details for a specific user by name or email.",
    parameters: [
      { name: "identifier", type: "string", description: "Name or email of the user" }
    ],
    handler: async (params) => {
      const context = await copilotService.getFullContext();
      if (!context.employeeData || context.employeeData.employees === undefined || context.employeeData.contractors === undefined) {
        return `User data is still loading. Please try again in a moment.`;
      }
      const allUsers = [...(context.employeeData.employees || []), ...(context.employeeData.contractors || [])];
      const user = allUsers.find(u =>
        (u.email && u.email.toLowerCase() === params.identifier.toLowerCase()) ||
        (`${u.firstName} ${u.lastName}`.toLowerCase().includes(params.identifier.toLowerCase()))
      );
      if (!user) return 'User not found.';
      return `Name: ${user.firstName} ${user.lastName}\nEmail: ${user.email}\nJob Title: ${user.jobTitle}\nType: ${user.WorkerType?.WorkerType}\nStatus: ${user.status?.userStatus}`;
    }
  });

  // Update User (field by field)
  useCopilotAction({
    name: "updateUser",
    description: "Update a specific field for a user.",
    parameters: [
      { name: "identifier", type: "string", description: "Name or email of the user to update" },
      { name: "field", type: "string", description: "Field to update (e.g., jobTitle, email, phoneNumber)" },
      { name: "value", type: "string", description: "New value for the field" }
    ],
    handler: async (params) => {
      // Find the user by identifier
      const context = await copilotService.getFullContext();
      const allUsers = [...(context.employeeData.employees || []), ...(context.employeeData.contractors || [])];
      const user = allUsers.find(u =>
        (u.email && u.email.toLowerCase() === params.identifier.toLowerCase()) ||
        (`${u.firstName} ${u.lastName}`.toLowerCase().includes(params.identifier.toLowerCase()))
      );
      if (!user) return 'User not found.';
      // Prepare update payload
      const updatePayload = {
        method: 'updateUser',
        user: {
          userId: user.userId,
          [params.field]: params.value
        }
      };
      try {
        const basicAuth = getBasicAuthHeader();
        const response = await axios.put(
          `${import.meta.env.VITE_API_URL}/adminPortal#updateUser`,
          updatePayload,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': basicAuth
            }
          }
        );
        if (response.data && response.data.success) {
          return `Successfully updated ${params.field} for ${user.firstName} ${user.lastName}.`;
        } else {
          return `Update attempted, but no success confirmation from backend.`;
        }
      } catch (error: any) {
        return `Failed to update user: ${error.message}`;
      }
    }
  });

  // Deactivate User
  useCopilotAction({
    name: "deactivateUser",
    description: "Deactivate a user by name or email.",
    parameters: [
      { name: "identifier", type: "string", description: "Name or email of the user to deactivate" },
      { name: "exitDate", type: "string", description: "Exit date (YYYY-MM-DD)" },
      { name: "personalEmail", type: "string", description: "Personal email of the user" },
      { name: "finalPayCheckType", type: "string", description: "Final paycheck type (e.g., 'They have already been paid')" },
      { name: "additionalNotes", type: "string", description: "Additional notes for deactivation (optional)" }
    ],
    handler: async (params) => {
      // Find the user by identifier
      const context = await copilotService.getFullContext();
      const allUsers = [...(context.employeeData.employees || []), ...(context.employeeData.contractors || [])];
      const user = allUsers.find(u =>
        (u.email && u.email.toLowerCase() === params.identifier.toLowerCase()) ||
        (`${u.firstName} ${u.lastName}`.toLowerCase().includes(params.identifier.toLowerCase()))
      );
      if (!user) return 'User not found.';
      // Prepare deactivation payload
      const deactivatePayload = {
        method: 'deactivateUser',
        user: {
          userId: user.userId,
          exitDate: params.exitDate,
          personalEmail: params.personalEmail,
          finalPayCheckType: params.finalPayCheckType,
          additionalNotes: params.additionalNotes || "Deactivated via Copilot"
        }
      };
      try {
        const basicAuth = getBasicAuthHeader();
        const response = await axios.post(
          `${import.meta.env.VITE_API_URL}/adminPortal#deactivateUser`,
          deactivatePayload,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': basicAuth
            }
          }
        );
        if (response.data && response.data.success) {
          return `Successfully deactivated user ${user.firstName} ${user.lastName}.`;
        } else {
          return `Deactivation attempted, but no success confirmation from backend.`;
        }
      } catch (error: any) {
        return `Failed to deactivate user: ${error.message}`;
      }
    }
  });

  // Terminate User
  useCopilotAction({
    name: "terminateUser",
    description: "Terminate a user by name or email.",
    parameters: [
      { name: "identifier", type: "string", description: "Name or email of the user to terminate" },
      { name: "exitDate", type: "string", description: "Exit date (YYYY-MM-DD)" },
      { name: "personalEmail", type: "string", description: "Personal email of the user" },
      { name: "finalPayCheckType", type: "string", description: "Final paycheck type (e.g., 'They have already been paid')" },
      { name: "terminationChoice", type: "string", description: "Termination choice (e.g., 'No - This user did not choose to leave')" },
      { name: "dismissalType", type: "string", description: "Dismissal type (e.g., 'Layoff')" },
      { name: "severance", type: "boolean", description: "Is severance provided? (true/false)" },
      { name: "severancePaymentType", type: "string", description: "Severance payment type (e.g., 'Yes, it will be a one time severance payment')" },
      { name: "severancePaymentFrequency", type: "string", description: "Severance payment frequency (e.g., 'Weekly, every Friday')" },
      { name: "firstSeverancePayDate", type: "string", description: "First severance pay date (YYYY-MM-DD)" },
      { name: "lastSeverancePayDate", type: "string", description: "Last severance pay date (YYYY-MM-DD)" },
      { name: "severanceAmount", type: "number", description: "Severance amount" },
      { name: "additionalNotes", type: "string", description: "Additional notes for termination (optional)" }
    ],
    handler: async (params) => {
      // Find the user by identifier
      const context = await copilotService.getFullContext();
      const allUsers = [...(context.employeeData.employees || []), ...(context.employeeData.contractors || [])];
      const user = allUsers.find(u =>
        (u.email && u.email.toLowerCase() === params.identifier.toLowerCase()) ||
        (`${u.firstName} ${u.lastName}`.toLowerCase().includes(params.identifier.toLowerCase()))
      );
      if (!user) return 'User not found.';
      // Prepare termination payload
      const terminatePayload = {
        method: 'terminateUser',
        user: {
          userId: user.userId,
          companyId: user.companyId,
          exitDate: params.exitDate,
          personalEmail: params.personalEmail,
          finalPayCheckType: params.finalPayCheckType,
          terminationChoice: params.terminationChoice,
          dismissalType: params.dismissalType,
          severance: params.severance,
          severancePaymentType: params.severancePaymentType,
          severancePaymentFrequency: params.severancePaymentFrequency,
          firstSeverancePayDate: params.firstSeverancePayDate,
          lastSeverancePayDate: params.lastSeverancePayDate,
          severanceAmount: params.severanceAmount,
          additionalNotes: params.additionalNotes || "Terminated via Copilot"
        }
      };
      try {
        const basicAuth = getBasicAuthHeader();
        const response = await axios.put(
          `${import.meta.env.VITE_API_URL}/adminPortal#terminateUser`,
          terminatePayload,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': basicAuth
            }
          }
        );
        if (response.data && response.data.success) {
          return `Successfully terminated user ${user.firstName} ${user.lastName}.`;
        } else {
          return `Termination attempted, but no success confirmation from backend.`;
        }
      } catch (error: any) {
        return `Failed to terminate user: ${error.message}`;
      }
    }
  });

  // Update User Wage
  useCopilotAction({
    name: "updateUserWage",
    description: "Update a user's wage by user wage ID.",
    parameters: [
      { name: "userWageId", type: "string", description: "User wage ID to update" },
      { name: "wageRate", type: "number", description: "Wage rate" },
      { name: "workerType", type: "string", description: "Worker type (e.g., 'W2')" },
      { name: "wageBasis", type: "string", description: "Wage basis (e.g., 'Per Week')" },
      { name: "userType", type: "string", description: "User type (e.g., 'Salary/Eligible for overtime')" },
      { name: "employmentStatus", type: "string", description: "Employment status (e.g., 'Full Time (30+ Hours per week)')" },
      { name: "userRefTaxExempt", type: "string", description: "Tax exempt status (e.g., 'Yes, as an owner/corporate officer')" },
      { name: "startDate", type: "string", description: "Start date (YYYY-MM-DD)" }
    ],
    handler: async (params) => {
      // Prepare update wage payload
      const updateWagePayload = {
        method: 'updateUserWage',
        userWage: {
          userWageId: params.userWageId,
          wageRate: params.wageRate,
          workerType: params.workerType,
          wageBasis: params.wageBasis,
          userType: params.userType,
          employmentStatus: params.employmentStatus,
          userRefTaxExempt: params.userRefTaxExempt,
          startDate: params.startDate
        }
      };
      try {
        const basicAuth = getBasicAuthHeader();
        const response = await axios.put(
          `${import.meta.env.VITE_API_URL}/adminPortal#updateUserWage`,
          updateWagePayload,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': basicAuth
            }
          }
        );
        if (response.data && response.data.success) {
          return `Successfully updated wage for user wage ID ${params.userWageId}.`;
        } else {
          return `Wage update attempted, but no success confirmation from backend.`;
        }
      } catch (error: any) {
        return `Failed to update user wage: ${error.message}`;
      }
    }
  });

  // Register employee-allowed actions here (view/update own details, etc.)

  return null;
}

// Helper validation function
function validateUserInput(userData: { firstName: string; middleName: string; lastName: string; email: string; phoneNumber: string; dateOfJoin: string; workerType: string; jobTitle: string; code: string; }) {
  const errors = [];
  // Validate first name
  if (!userData.firstName || userData.firstName.length > 50 || /\d/.test(userData.firstName)) {
    errors.push("Invalid first name");
  }
  // Validate last name
  if (!userData.lastName || /\d/.test(userData.lastName)) {
    errors.push("Invalid last name");
  }
  // Validate email format
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
    errors.push("Invalid email");
  }
  // Validate phone number format
  if (!/^\d{10}$/.test(userData.phoneNumber)) {
    errors.push("Invalid phone number");
  }
  // Validate date of join
  if (!userData.dateOfJoin || isNaN(new Date(userData.dateOfJoin).getTime())) {
    errors.push("Invalid date of join");
  }
  // Validate worker type
  if (!['W2', '1099'].includes(userData.workerType)) {
    errors.push("Invalid worker type");
  }
  // Validate state code
  if (!/^[A-Z]{2}$/.test(userData.code.toUpperCase())) {
    errors.push("Invalid state code");
  }
  return errors;
};

// Placeholder function for adding a user to the system
async function addUserToSystem(params: { firstName: string; middleName: string; lastName: string; email: string; phoneNumber: string; dateOfJoin: string; workerType: string; jobTitle: string; code: string; }) {
  // Simulate adding user to the system
  console.log("Adding user to the system:", params);
  // Throw an error if needed for testing
  // throw new Error('Simulated error');
}

export default CopilotActionsProvider;
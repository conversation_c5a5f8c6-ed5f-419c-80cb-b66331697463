@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import '@copilotkit/react-ui/styles.css';

/* Add these styles to customize the Copilot popup */
.copilot-popup {
  --copilot-popup-background: white;
  --copilot-popup-text: #374151;
  --copilot-popup-button-background: #0D9488;
  --copilot-popup-button-text: white;
  --copilot-popup-button-hover: #0F766E;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
}

.copilot-popup-button {
  transition: all 0.2s ease-in-out;
}

.copilot-popup-button:hover {
  transform: scale(1.05);
}

/* Page transition class - make it more pronounced */
.page-transition {
  animation: fadeInRight 0.5s ease-out forwards;
  will-change: opacity, transform;
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Add this to your existing CSS */
.page-content {
  animation: fadeInRight 0.4s ease-out;
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Copilot Sidebar */
@media (max-width: 768px) {
  .copilot-popup {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    left: auto !important;
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
    z-index: 9999 !important;
    box-shadow: none !important;
    animation: slideInSidebar 0.3s cubic-bezier(0.4,0,0.2,1);
  }
}

@keyframes slideInSidebar {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

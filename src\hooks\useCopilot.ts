import { useCopilotContext } from '@copilotkit/react-core';
import { useState, useCallback } from 'react';
import { copilotService } from '../services/copilotService';
import { PromptManager } from '../services/promptManager';

export const useCopilot = () => {
  const { submitMessage } = useCopilotContext();
  const [isLoading, setIsLoading] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');

  const askCopilot = useCallback(async (
    question: string,
    action?: string,
    actionData?: any
  ) => {
    setIsLoading(true);
    setStreamingContent('');

    try {
      // Get current context
      const userData = await copilotService.getCurrentUser();
      const companyData = await copilotService.getCurrentCompany();
      const availableActions = await copilotService.getAvailableActions();

      // Generate dynamic prompt
      const dynamicPrompt = PromptManager.generatePrompt({
        currentPage: window.location.pathname,
        userRole: userData?.role,
        availableActions,
        userData,
        companyData
      });

      // Handle user count questions directly
      if (question.toLowerCase().includes('how many')) {
        const users = await copilotService.getUsersByCompany();
        const q = question.toLowerCase();
        if (q.includes('employee')) {
          return {
            response: `Based on the current data, your company has ${users.employees} employees.`
          };
        } else if (q.includes('contractor')) {
          return {
            response: `Based on the current data, your company has ${users.contractors} contractors.`
          };
        } else if (q.includes('user') || q.includes('people')) {
          return {
            response: `Based on the current data, your company has ${users.total} users (employees + contractors).`
          };
        }
      }

      // Submit message with dynamic prompt
      const response = await submitMessage({
        message: question,
        context: {
          currentPage: window.location.pathname,
          timestamp: new Date().toISOString(),
          action,
          actionData,
          systemPrompt: dynamicPrompt
        },
        onStream: (content: string) => {
          setStreamingContent(prev => prev + content);
        },
      });

      return response;
    } catch (error) {
      console.error('Error in askCopilot:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [submitMessage]);

  return { askCopilot, isLoading, streamingContent };
};

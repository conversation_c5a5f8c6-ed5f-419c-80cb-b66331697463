

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."asset_status" AS ENUM (
    'Pending',
    'Approved',
    'Rejected'
);


ALTER TYPE "public"."asset_status" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_asset_assignments"() RETURNS TABLE("Company_Name" "text", "Employee_Name" "text", "Asset_Type" "text")
    LANGUAGE "sql"
    AS $$
  select 
    c."Company_Name",
    e."First_Name" || ' ' || e."Last_Name" as "Employee_Name",
    a."Asset_Type"
  from 
    public."Asset_Assigned" aa
  join 
    public.employee e on aa."Employee" = e.id
  join 
    public.companies c on e."Company_Name" = c.id
  join 
    public."Assets" a on aa."Asset" = a.id;
$$;


ALTER FUNCTION "public"."get_asset_assignments"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."Asset_Assigned" (
    "id" bigint NOT NULL,
    "Asset" "uuid" DEFAULT "gen_random_uuid"(),
    "Employee" "uuid" DEFAULT "gen_random_uuid"(),
    "Creation_Date" timestamp with time zone,
    "Modified_Date" timestamp with time zone,
    "Slug" "text",
    "Creator" "text"
);


ALTER TABLE "public"."Asset_Assigned" OWNER TO "postgres";


ALTER TABLE "public"."Asset_Assigned" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."Asset_Assigned_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."Assets" (
    "Asset_Type" "text",
    "Leasing_Cost" numeric,
    "Model_Name" "text",
    "Serial_No." "text",
    "Status" "public"."asset_status",
    "Creation_Date" timestamp with time zone,
    "Modified_Date" timestamp with time zone,
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL
);


ALTER TABLE "public"."Assets" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."Real_Estates" (
    "Cost_Per_Month" bigint,
    "Country" "text",
    "Location" "text",
    "Creation_Date" "text",
    "Modified_Date" "text",
    "Slug" "text",
    "Creator" "text"
);


ALTER TABLE "public"."Real_Estates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."assets" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "person_id" "uuid",
    "company_id" "uuid",
    "type" "text" NOT NULL,
    "serial_number" "text" NOT NULL,
    "model_number" "text" NOT NULL,
    "leasing_cost" numeric DEFAULT 0 NOT NULL,
    "assigned_date" "date" NOT NULL,
    "status" "text" DEFAULT 'active'::"text" NOT NULL
);


ALTER TABLE "public"."assets" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."companies" (
    "Accounts_Email_id" "text",
    "Assets_Amount" "text",
    "Company_Logo" "text",
    "Company_Name" "text",
    "Company_Status" "text",
    "Company_Website" "text",
    "Contact_Email" "text",
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL
);


ALTER TABLE "public"."companies" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."employee" (
    "Actual_Total_Fee" "text",
    "Employee_Email" "text",
    "Employee_Status" "text",
    "Employee_Type" "text",
    "First_Name" "text",
    "Id" bigint,
    "Last_Name" "text",
    "Total_Fee" "text",
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "Company_Name" "uuid"
);


ALTER TABLE "public"."employee" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."people" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "company_id" "uuid",
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "email" "text" NOT NULL,
    "country" "text" NOT NULL,
    "person_type" "text" NOT NULL,
    "eor_peo" "text" NOT NULL,
    "status" "text" DEFAULT 'active'::"text" NOT NULL,
    "salary" numeric DEFAULT 0 NOT NULL,
    "start_date" "date" NOT NULL
);


ALTER TABLE "public"."people" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."real_estate" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "person_id" "uuid",
    "company_id" "uuid",
    "location" "text" NOT NULL,
    "address" "text" NOT NULL,
    "monthly_cost" numeric DEFAULT 0 NOT NULL,
    "start_date" "date" NOT NULL,
    "end_date" "date",
    "status" "text" DEFAULT 'active'::"text" NOT NULL
);


ALTER TABLE "public"."real_estate" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."temp_website2_users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" "text",
    "first_name" "text",
    "middle_name" "text",
    "last_name" "text",
    "phone" "text",
    "company_id" "uuid",
    "company_name" "text",
    "business_user_id" "uuid",
    "ownership_percentage" numeric,
    "date_of_birth" date,
    "ssn" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "job_title" "text",
    "date_of_join" date,
    "kyc_status" "text",
    "user_status" "text",
    "worker_type" "text",
    "wage_rate" numeric,
    "wage_basis" "text",
    "payment_method" "text",
    "address1" "text",
    "address2" "text",
    "city" "text",
    "state" "text",
    "zipcode" "text",
    "country" "text",
    "user_id" "uuid",
    "is_business_user" boolean DEFAULT false,
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."temp_website2_users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_company_access" (
    "user_id" bigint NOT NULL,
    "company_id" "uuid" NOT NULL
);


ALTER TABLE "public"."user_company_access" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users_profiles" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "Company_Access" "text"[],
    "First_Name" "text",
    "is_active" "text" DEFAULT ''::"text" NOT NULL,
    "Last_Name" "text",
    "Role" "text" NOT NULL,
    "email" "text" NOT NULL
);


ALTER TABLE "public"."users_profiles" OWNER TO "postgres";


ALTER TABLE "public"."users_profiles" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."users_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



ALTER TABLE ONLY "public"."Asset_Assigned"
    ADD CONSTRAINT "Asset_Assigned_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."Assets"
    ADD CONSTRAINT "Assets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_serial_number_key" UNIQUE ("serial_number");



ALTER TABLE ONLY "public"."companies"
    ADD CONSTRAINT "companies_Company Name_key" UNIQUE ("Company_Name");



ALTER TABLE ONLY "public"."companies"
    ADD CONSTRAINT "companies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."employee"
    ADD CONSTRAINT "employee_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."people"
    ADD CONSTRAINT "people_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."people"
    ADD CONSTRAINT "people_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."real_estate"
    ADD CONSTRAINT "real_estate_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."temp_website2_users"
    ADD CONSTRAINT "temp_website2_users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."temp_website2_users"
    ADD CONSTRAINT "temp_website2_users_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."temp_website2_users"
    ADD CONSTRAINT "temp_website2_users_user_id_key" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."user_company_access"
    ADD CONSTRAINT "user_company_access_pkey" PRIMARY KEY ("user_id", "company_id");



ALTER TABLE ONLY "public"."users_profiles"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."Asset_Assigned"
    ADD CONSTRAINT "Asset_Assigned_Asest_fkey" FOREIGN KEY ("Asset") REFERENCES "public"."Assets"("id");



ALTER TABLE ONLY "public"."Asset_Assigned"
    ADD CONSTRAINT "Asset_Assigned_Employee_fkey" FOREIGN KEY ("Employee") REFERENCES "public"."employee"("id");



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_person_id_fkey" FOREIGN KEY ("person_id") REFERENCES "public"."people"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."employee"
    ADD CONSTRAINT "employee_Company Name_fkey" FOREIGN KEY ("Company_Name") REFERENCES "public"."companies"("id");



ALTER TABLE ONLY "public"."employee"
    ADD CONSTRAINT "employee_Company Name_fkey1" FOREIGN KEY ("Company_Name") REFERENCES "public"."companies"("id");



ALTER TABLE ONLY "public"."people"
    ADD CONSTRAINT "people_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."real_estate"
    ADD CONSTRAINT "real_estate_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."real_estate"
    ADD CONSTRAINT "real_estate_person_id_fkey" FOREIGN KEY ("person_id") REFERENCES "public"."people"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_company_access"
    ADD CONSTRAINT "user_company_access_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_company_access"
    ADD CONSTRAINT "user_company_access_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users_profiles"("id") ON DELETE CASCADE;



CREATE POLICY "Allow authenticated users to delete their own profile" ON "public"."users_profiles" FOR DELETE TO "authenticated" USING (("email" = ( SELECT ("auth"."jwt"() ->> 'email'::"text"))));



CREATE POLICY "Allow authenticated users to insert a new profile" ON "public"."users_profiles" FOR INSERT WITH CHECK ((("email" = ( SELECT ("auth"."jwt"() ->> 'email'::"text"))) AND (NOT (EXISTS ( SELECT 1
   FROM "public"."users_profiles" "users_profiles_1"
  WHERE ("users_profiles_1"."email" = ( SELECT ("auth"."jwt"() ->> 'email'::"text"))))))));



CREATE POLICY "Allow authenticated users to select their own profile" ON "public"."users_profiles" FOR SELECT TO "authenticated" USING (("email" = ( SELECT ("auth"."jwt"() ->> 'email'::"text"))));



CREATE POLICY "Allow authenticated users to update their own profile" ON "public"."users_profiles" FOR UPDATE TO "authenticated" USING (("email" = ( SELECT ("auth"."jwt"() ->> 'email'::"text")))) WITH CHECK (("email" = ( SELECT ("auth"."jwt"() ->> 'email'::"text"))));



ALTER TABLE "public"."Asset_Assigned" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."Assets" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."Real_Estates" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."temp_website2_users" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "SaaS admin can delete data from Asset_Assigned" ON "public"."Asset_Assigned" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can delete data from Assets" ON "public"."Assets" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can delete data from Real_Estates" ON "public"."Real_Estates" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can delete data from companies" ON "public"."companies" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can delete data from employee" ON "public"."employee" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can delete data from temp_website2_users" ON "public"."temp_website2_users" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can delete data from user_company_access" ON "public"."user_company_access" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can delete data from users_profiles" ON "public"."users_profiles" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can insert data into Asset_Assigned" ON "public"."Asset_Assigned" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "SaaS admin can insert data into Assets" ON "public"."Assets" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "SaaS admin can insert data into Real_Estates" ON "public"."Real_Estates" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "SaaS admin can insert data into companies" ON "public"."companies" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "SaaS admin can insert data into employee" ON "public"."employee" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "SaaS admin can insert data into temp_website2_users" ON "public"."temp_website2_users" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "SaaS admin can insert data into user_company_access" ON "public"."user_company_access" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "SaaS admin can insert data into users_profiles" ON "public"."users_profiles" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "SaaS admin can select data from Asset_Assigned" ON "public"."Asset_Assigned" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can select data from Assets" ON "public"."Assets" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can select data from Real_Estates" ON "public"."Real_Estates" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can select data from companies" ON "public"."companies" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can select data from employee" ON "public"."employee" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can select data from temp_website2_users" ON "public"."temp_website2_users" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can select data from user_company_access" ON "public"."user_company_access" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can select data from users_profiles" ON "public"."users_profiles" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "SaaS admin can update data in Asset_Assigned" ON "public"."Asset_Assigned" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "SaaS admin can update data in Assets" ON "public"."Assets" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "SaaS admin can update data in Real_Estates" ON "public"."Real_Estates" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "SaaS admin can update data in companies" ON "public"."companies" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "SaaS admin can update data in employee" ON "public"."employee" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "SaaS admin can update data in temp_website2_users" ON "public"."temp_website2_users" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "SaaS admin can update data in user_company_access" ON "public"."user_company_access" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "SaaS admin can update data in users_profiles" ON "public"."users_profiles" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Users can view assets in their companies" ON "public"."assets" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Users can view companies they have access to" ON "public"."companies" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Users can view people in their companies" ON "public"."people" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Users can view real estate in their companies" ON "public"."real_estate" FOR SELECT TO "authenticated" USING (true);



ALTER TABLE "public"."assets" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."companies" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."employee" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."people" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."real_estate" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_company_access" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."users_profiles" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";

GRANT ALL ON FUNCTION "public"."get_asset_assignments"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_asset_assignments"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_asset_assignments"() TO "service_role";

GRANT ALL ON TABLE "public"."Asset_Assigned" TO "anon";
GRANT ALL ON TABLE "public"."Asset_Assigned" TO "authenticated";
GRANT ALL ON TABLE "public"."Asset_Assigned" TO "service_role";



GRANT ALL ON SEQUENCE "public"."Asset_Assigned_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."Asset_Assigned_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."Asset_Assigned_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."Assets" TO "anon";
GRANT ALL ON TABLE "public"."Assets" TO "authenticated";
GRANT ALL ON TABLE "public"."Assets" TO "service_role";



GRANT ALL ON TABLE "public"."Real_Estates" TO "anon";
GRANT ALL ON TABLE "public"."Real_Estates" TO "authenticated";
GRANT ALL ON TABLE "public"."Real_Estates" TO "service_role";



GRANT ALL ON TABLE "public"."assets" TO "anon";
GRANT ALL ON TABLE "public"."assets" TO "authenticated";
GRANT ALL ON TABLE "public"."assets" TO "service_role";



GRANT ALL ON TABLE "public"."companies" TO "anon";
GRANT ALL ON TABLE "public"."companies" TO "authenticated";
GRANT ALL ON TABLE "public"."companies" TO "service_role";



GRANT ALL ON TABLE "public"."employee" TO "anon";
GRANT ALL ON TABLE "public"."employee" TO "authenticated";
GRANT ALL ON TABLE "public"."employee" TO "service_role";



GRANT ALL ON TABLE "public"."people" TO "anon";
GRANT ALL ON TABLE "public"."people" TO "authenticated";
GRANT ALL ON TABLE "public"."people" TO "service_role";



GRANT ALL ON TABLE "public"."real_estate" TO "anon";
GRANT ALL ON TABLE "public"."real_estate" TO "authenticated";
GRANT ALL ON TABLE "public"."real_estate" TO "service_role";



GRANT ALL ON TABLE "public"."temp_website2_users" TO "anon";
GRANT ALL ON TABLE "public"."temp_website2_users" TO "authenticated";
GRANT ALL ON TABLE "public"."temp_website2_users" TO "service_role";



GRANT ALL ON TABLE "public"."user_company_access" TO "anon";
GRANT ALL ON TABLE "public"."user_company_access" TO "authenticated";
GRANT ALL ON TABLE "public"."user_company_access" TO "service_role";



GRANT ALL ON TABLE "public"."users_profiles" TO "anon";
GRANT ALL ON TABLE "public"."users_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."users_profiles" TO "service_role";



GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";
RESET ALL;

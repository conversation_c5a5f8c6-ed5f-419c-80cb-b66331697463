  import { api } from './apiConfig';
  import { ApiResponse } from '../types/api';
  import { utf8ToBase64 } from '../utils/base64';

  export interface AdminCompanyResponse {
    companyID: string;
    company: string;
  }

  export interface AdminUserResponse {
    id: string;
    email: string;
    role: string;
    status: string;
    lastLogin: string;
  }

  // Interfaces for createBusiness payload
  interface RegistrationInfo {
    company: string;
    businessWebsite: string;
    nacisSubCategoryCode: number;
    isEsign: boolean;
    isTermsAccepted: boolean;
  }

  interface KybInformation {
    ein: string;
    entityType: string;
    dateOfIncorporation: string; // "YYYY-MM-DD"
    irsAssisgnedFederalFilingForm: string;
  }

  interface CompanyLocationData {
    companyLocation: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zipcode: string;
    phoneNumber: string;
    isWorkLocation: boolean;
    isMailingAddress: boolean;
    isFilingAddress: boolean;
  }

  interface BusinessUserData {
    firstName: string;
    middleName?: string;
    lastName: string;
    phoneNumber: string;
    email: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zipcode: string;
    ssn: string;
    dateOfBirth: string; // "YYYY-MM-DD"
    payrollAdmin: boolean;
    bookKeeper: boolean;
    beneficialOwner: boolean;
    ownershipPercentage: number;
  }

  export interface CreateBusinessPayload {
    registration: RegistrationInfo;
    kybInformation: KybInformation;
    companyLocation: CompanyLocationData;
    businessUser: BusinessUserData;
  }

  export const adminService = {
    // Company Management
    getAllCompanies: async () => {
      try {
        const response = await api.get<{ Company: AdminCompanyResponse[] }>('/reports/getCompanies');
        
        // More robust data validation
        if (!response.data) {
          throw new Error('No data received from server');
        }

        // Handle different possible response formats
        let companies: AdminCompanyResponse[] = [];
        
        if (Array.isArray(response.data)) {
          companies = response.data;
        } else if (Array.isArray(response.data.Company)) {
          companies = response.data.Company;
        } else if (response.data.data && Array.isArray(response.data.data)) {
          companies = response.data.data;
        } else {
          throw new Error('Companies data is not in expected format');
        }

        return { data: companies };
      } catch (error: any) {
        console.error('getAllCompanies error:', error);
        return { 
          data: [],
          error: error.message || 'Failed to fetch companies'
        };
      }
    },
    
    getCompanyDetails: (companyId: string) => 
      api.get<ApiResponse<AdminCompanyResponse>>(`/reports/companies/${companyId}`),
    
    updateCompanyStatus: (companyId: string, status: string) => 
      api.put(`/reports/companies/${companyId}/status`, { status }),

    async createBusiness(companyData: CreateBusinessPayload): Promise<any> {
      console.log('🔍 adminService.createBusiness called with data:', JSON.stringify(companyData, null, 2));
      try {
        // Get the token from environment variables
        const clientId = import.meta.env.VITE_CLIENT_ID;
        const clientSecret = import.meta.env.VITE_CLIENT_SECRET;
        
        if (!clientId || !clientSecret) {
          throw new Error('Client credentials not found in environment variables');
        }
        
        // Create Basic Auth token
        const credentials = `${clientId}:${clientSecret}`;
        const token = utf8ToBase64(credentials); // Base64 encode the credentials

        // Ensure EIN is exactly 9 digits
        let kybInformation = { ...companyData.kybInformation };
        if (kybInformation.ein) {
          // Remove any non-digit characters
          kybInformation.ein = kybInformation.ein.replace(/\D/g, '');
          
          // Ensure it's exactly 9 digits
          if (kybInformation.ein.length !== 9) {
            throw new Error('EIN must be 9 digits');
          }
        }
        
        // Ensure SSN is exactly 9 digits
        let businessUser = { ...companyData.businessUser };
        if (businessUser.ssn) {
          // Remove any non-digit characters
          businessUser.ssn = businessUser.ssn.replace(/\D/g, '');
          
          // Ensure it's exactly 9 digits
          if (businessUser.ssn.length !== 9) {
            throw new Error('SSN must be 9 digits');
          }
        }
        
        const payload = {
          method: "createBusiness",
          registration: companyData.registration,
          kybInformation: kybInformation,
          companyLocation: companyData.companyLocation,
          businessUser: businessUser
        };

        console.log('📤 Sending API request to create business with payload:', JSON.stringify(payload, null, 2));
        console.log('🔍 EIN format being sent to API:', kybInformation.ein, '(should be 9 digits without hyphens)');

        // Use the API URL from environment variables if available
        const apiUrl = import.meta.env.VITE_API_URL || 'https://sandbox.rollfi.xyz';
        const response = await fetch(`${apiUrl}/companyOnboarding#createBusiness`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${token}`
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ API error response text:', errorText);
          let apiErrorMessage = `API error: ${response.status} ${response.statusText}`;
          try {
            const errorJson = JSON.parse(errorText);
            if (errorJson && errorJson.message) {
              apiErrorMessage += ` - ${errorJson.message}`;
            } else if (errorJson && errorJson.error && errorJson.error.message) {
              apiErrorMessage += ` - ${errorJson.error.message}`;
            } else {
              apiErrorMessage += ` - ${errorText}`;
            }
          } catch (e) {
            apiErrorMessage += ` - ${errorText}`;
          }
          throw new Error(apiErrorMessage);
        }

        const result = await response.json();
        console.log('✅ API response from createBusiness:', JSON.stringify(result, null, 2));
        
        // Check if the response contains an error object
        if (result && result.error) {
          let errorMessage = result.error.message || 'An error occurred during company creation';
          
          // Simplify all EIN format error messages
          if (errorMessage.includes('EIN')) {
            errorMessage = 'EIN must be 9 digits';
          }
          // Simplify all SSN format error messages
          else if (errorMessage.includes('SSN')) {
            errorMessage = 'SSN must be 9 digits';
          }
          
          throw new Error(errorMessage);
        }
        
        return result;
      } catch (error) {
        console.error('❌ Error in adminService.createBusiness:', error);
        throw error; // Re-throw to be caught by the caller
      }
    },

    // User Management
    getAllUsers: () => 
      api.get<ApiResponse<AdminUserResponse[]>>('/admin/users'),
    
    createAdminUser: (userData: { email: string; role: string; }) => 
      api.post('/admin/users', userData),
    
    updateUserStatus: (userId: string, status: string) => 
      api.put(`/admin/users/${userId}/status`, { status }),

    // Analytics & Reporting
    getSystemStats: () => 
      api.get('/admin/stats'),
    
    getAuditLogs: (filters: any) => 
      api.get('/admin/audit-logs', { params: filters }),

    // Settings & Configuration
    getSystemSettings: () => 
      api.get('/admin/settings'),
    
    updateSystemSettings: (settings: any) => 
      api.put('/admin/settings', settings),
  };






















import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { adminService, CreateBusinessPayload, AdminCompanyResponse } from '../../../services/adminService';
import RegistrationForm from './components/RegistrationForm';
import KybForm from './components/KybForm';
import CompanyLocationForm from './components/CompanyLocationForm';
import BusinessUserForm from './components/BusinessUserForm';
import ReviewStep from './components/ReviewStep';
import styles from './AddCompanyPage.module.css';

const STEPS = {
  REGISTRATION: 1,
  KYB: 2,
  LOCATION: 3,
  USER: 4,
  REVIEW: 5,
};

const AddCompanyPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(STEPS.REGISTRATION);
  const [formData, setFormData] = useState<Partial<CreateBusinessPayload>>({
    registration: {
      company: '',
      businessWebsite: '',
      nacisSubCategoryCode: 0,
      isEsign: true,
      isTermsAccepted: true,
    },
    kybInformation: {
      ein: '',
      entityType: '',
      dateOfIncorporation: '',
      irsAssisgnedFederalFilingForm: '',
    },
    companyLocation: {
      companyLocation: 'M', // Default as per API suggestion
      address1: '',
      address2: '',
      city: '',
      state: '',
      zipcode: '',
      phoneNumber: '',
      isWorkLocation: true,
      isMailingAddress: true,
      isFilingAddress: true,
    },
    businessUser: {
      firstName: '',
      lastName: '',
      phoneNumber: '',
      email: '',
      address1: '',
      city: '',
      state: '',
      zipcode: '',
      ssn: '',
      dateOfBirth: '',
      payrollAdmin: true,
      bookKeeper: true,
      beneficialOwner: true,
      ownershipPercentage: 25,
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleNext = (data: any) => {
    setFormData((prev) => ({ ...prev, ...data }));
    setCurrentStep((prev) => prev + 1);
    setError(null);
  };

  const handleBack = () => {
    setCurrentStep((prev) => prev - 1);
    setError(null);
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Ensure all parts of formData are present as expected by CreateBusinessPayload
      const payload: CreateBusinessPayload = {
        registration: formData.registration!,
        kybInformation: formData.kybInformation!,
        companyLocation: formData.companyLocation!,
        businessUser: formData.businessUser!,
      };
      const response = await adminService.createBusiness(payload);
      console.log('Company created successfully:', response);
      // TODO: Add success notification/toast
      // Navigate to a relevant page, e.g., company list or dashboard
      navigate('/admin'); // Navigate to the admin dashboard
    } catch (err: any) {
      console.error('Failed to create company:', err);
      setError(err.message || 'An unexpected error occurred.');
      // TODO: Add error notification/toast
    }
    setIsLoading(false);
  };

  const renderStep = () => {
    switch (currentStep) {
      case STEPS.REGISTRATION:
        return <RegistrationForm data={formData.registration!} onNext={handleNext} />;
      case STEPS.KYB:
        return <KybForm data={formData.kybInformation!} onNext={handleNext} onBack={handleBack} />;
      case STEPS.LOCATION:
        return <CompanyLocationForm data={formData.companyLocation!} onNext={handleNext} onBack={handleBack} />;
      case STEPS.USER:
        return <BusinessUserForm data={formData.businessUser!} onNext={handleNext} onBack={handleBack} />;
      case STEPS.REVIEW:
        return <ReviewStep data={formData as CreateBusinessPayload} onSubmit={handleSubmit} onBack={handleBack} isLoading={isLoading} />;
      default:
        return <div>Unknown Step</div>;
    }
  };

  return (
    <div className={styles.addCompanyPageContainer}>
      <h1 className={styles.pageTitle}>Add New Company</h1>
      {/* Optional: Add a stepper component here to show progress */}
      {/* <AddCompanyStepper currentStep={currentStep} totalSteps={Object.keys(STEPS).length} /> */}
      
      {error && <div className={styles.errorMessage}>{error}</div>}
      
      <div className={styles.formContainer}>
        {renderStep()}
      </div>
    </div>
  );
};

export default AddCompanyPage;
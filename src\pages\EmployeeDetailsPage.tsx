import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiRefreshCcw, FiUsers, FiAlertCircle } from 'react-icons/fi';
import { useAppContext } from '../context/AppContext';
import { useEmployeeViewStore } from '../store/employeeViewStore';
import { supabase } from '../lib/supabaseClient';
import EmployeeDetailsView from '../components/EmployeeDetailsView';
import api from '../services/apiConfig';
import { utf8ToBase64 } from '../utils/base64';
import { getUserDetails as fetchEmployeeDetailsFromService } from '../services/employerService';

interface UserAddress {
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
}

interface UserWage {
  WageRate: number;
  WageBasis: {
    WageBasis: string;
  };
  paymentMethod: {
    PaymentMethod: string;
  };
  employeeRefTaxExempt?: string;
  employeeType?: string;
  differentialPay?: string;
  employmentStatus?: string;
}

interface EmployeeDetails {
  userId: string;
  user: string;
  companyId: string;
  status: {
    userStatus: string;
  };
  WorkerType: {
    WorkerType: string;
  };
  firstName: string;
  lastName: string;
  middleName?: string;
  phoneNumber: string;
  kycStatus: string;
  jobTitle: string;
  dateOfJoin: string;
  email: string;
  userWages: UserWage[];
  userAddress?: UserAddress;
  companyLocationCategory?: {
    companyLocationCategory: string;
  };
  companyLocation?: {
    companyLocation: string;
    address: string;
    companyLocationId: string;
  };
  stateId?: string;
  ssn?: string;
}

const EmployeeDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentRole, switchToAdminRole } = useAppContext();
  const { selectedEmployeeId, isAdminViewingAsEmployee } = useEmployeeViewStore();
  const [employeeDetails, setEmployeeDetails] = useState<EmployeeDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [canSwitchToAdmin, setCanSwitchToAdmin] = useState(false);
  
  useEffect(() => {
    // Check if user can switch to admin and get user roles from database
    const checkUserRoles = async () => {
      try {
        // Get the current user's session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.user?.email) {
          console.log('Current user email:', session.user.email);
          
          // Get the user's roles from Supabase
          const { data: userProfile, error: profileError } = await supabase
            .from('temp_website2_users')
            .select('is_business_user, user_id, company_id')
            .eq('email', session.user.email)
            .single();
          
          if (profileError) {
            console.error('Error fetching user profile:', profileError);
            // Fallback to localStorage
            const storedRoles = localStorage.getItem('userRoles');
            if (storedRoles) {
              const roles = JSON.parse(storedRoles);
              console.log('User roles from localStorage:', roles);
              setCanSwitchToAdmin(roles.isPayrollAdmin || roles.isBusinessUser);
            }
          } else if (userProfile) {
            console.log('User profile from Supabase:', userProfile);
            
            // Determine user roles based on the 'is_business_user' field
            const isBusinessUser = userProfile.is_business_user === true;
            // For now, we'll consider business users as admins too
            const isPayrollAdmin = isBusinessUser; 
            const isEmployee = !isBusinessUser;
            
            console.log('Determined user roles:', { isBusinessUser, isPayrollAdmin, isEmployee });
            
            // Can switch if they have admin or business user roles
            setCanSwitchToAdmin(isPayrollAdmin || isBusinessUser);
            
            // Store the company ID in localStorage for later use
            if (userProfile.company_id) {
              localStorage.setItem('selectedCompanyId', userProfile.company_id);
              console.log('Stored company_id in localStorage:', userProfile.company_id);
            }
            
            // Store user ID in localStorage
            if (userProfile.user_id) {
              localStorage.setItem('currentUserId', userProfile.user_id);
              console.log('Stored user_id in localStorage:', userProfile.user_id);
            }
            
            // Store roles in localStorage for future use
            const roles = {
              isBusinessUser,
              isPayrollAdmin,
              isEmployee
            };
            localStorage.setItem('userRoles', JSON.stringify(roles));
            console.log('Stored user roles in localStorage:', roles);
          }
        } else {
          // Fallback to localStorage
          const storedRoles = localStorage.getItem('userRoles');
          if (storedRoles) {
            const roles = JSON.parse(storedRoles);
            console.log('User roles from localStorage:', roles);
            setCanSwitchToAdmin(roles.isPayrollAdmin || roles.isBusinessUser);
          }
        }
      } catch (err: any) {
        console.error('Error checking user roles:', err);
        
        // Fallback to localStorage
        const storedRoles = localStorage.getItem('userRoles');
        if (storedRoles) {
          const roles = JSON.parse(storedRoles);
          console.log('User roles from localStorage (fallback):', roles);
          setCanSwitchToAdmin(roles.isPayrollAdmin || roles.isBusinessUser);
        }
      }
    };
    
    checkUserRoles();
    
    // Check if we're in admin-as-employee mode
    const storedRole = localStorage.getItem('currentRole');
    const isAdminView = storedRole === 'admin-as-employee';
    
    // Get the employee ID to display
    const employeeId = selectedEmployeeId || localStorage.getItem('viewingEmployeeId');
    
    // Fetch employee details from the API
    const getEmployeeDetails = async () => {
      setLoading(true);
      try {
        console.log('=== EMPLOYEE DETAILS FETCH PROCESS STARTED ===');
        console.log('Current route: /employee/details');
        
        // Log stored values
        console.log('Stored values from localStorage:');
        console.log('- selectedEmployeeId:', selectedEmployeeId);
        console.log('- viewingEmployeeId:', localStorage.getItem('viewingEmployeeId'));
        console.log('- currentRole:', localStorage.getItem('currentRole'));
        console.log('- selectedCompanyId:', localStorage.getItem('selectedCompanyId'));
        
        // Get the company ID from localStorage or from Supabase
        let companyId = localStorage.getItem('selectedCompanyId');
        console.log('Initial companyId from localStorage:', companyId);
        
        // If not in localStorage, try to get it from Supabase
        if (!companyId) {
          console.log('No companyId in localStorage, fetching from Supabase...');
          const { data: { session } } = await supabase.auth.getSession();
          
          if (session?.user?.email) {
            console.log('User session found with email:', session.user.email);
            
            const { data: userProfile, error: profileError } = await supabase
              .from('temp_website2_users')
              .select('company_id, role, user_id')
              .eq('email', session.user.email)
              .single();
            
            console.log('Supabase query result:');
            if (profileError) {
              console.error('Supabase query error:', profileError);
            } else {
              console.log('User profile from Supabase:', userProfile);
            }
            
            if (!profileError && userProfile && userProfile.company_id) {
              companyId = userProfile.company_id;
              localStorage.setItem('selectedCompanyId', companyId);
              console.log('CompanyId set from Supabase:', companyId);
              
              // Also store user_id if available
              if (userProfile.user_id) {
                console.log('Storing user_id in localStorage:', userProfile.user_id);
                localStorage.setItem('currentUserId', userProfile.user_id);
              }
            }
          } else {
            console.log('No user session found');
          }
        }
        
        // Determine which user ID to use for the API call
        let userIdToFetch = employeeId;
        const storedRole = localStorage.getItem('currentRole');
        
        console.log('Current role:', storedRole);
        console.log('Initial userIdToFetch (from employeeId):', userIdToFetch);
        
        // If not in admin-as-employee mode and no employeeId, use current user's ID
        if (storedRole !== 'admin-as-employee' && !userIdToFetch) {
          console.log('Not in admin-as-employee mode, trying to get current user ID');
          userIdToFetch = localStorage.getItem('currentUserId');
          console.log('Current user ID from localStorage:', userIdToFetch);
          
          // If still no user ID, try to get it from Supabase
          if (!userIdToFetch) {
            console.log('No currentUserId in localStorage, fetching from Supabase...');
            const { data: { session } } = await supabase.auth.getSession();
            
            if (session?.user?.email) {
              console.log('User session found with email:', session.user.email);
              
              const { data: userProfile, error: profileError } = await supabase
                .from('temp_website2_users')
                .select('user_id')
                .eq('email', session.user.email)
                .single();
              
              console.log('Supabase query for user_id result:');
              if (profileError) {
                console.error('Supabase query error:', profileError);
              } else {
                console.log('User profile from Supabase:', userProfile);
              }
              
              if (!profileError && userProfile && userProfile.user_id) {
                userIdToFetch = userProfile.user_id;
                localStorage.setItem('currentUserId', userIdToFetch);
                console.log('User ID set from Supabase:', userIdToFetch);
              }
            } else {
              console.log('No user session found');
            }
          }
        }
        
        if (!userIdToFetch || !companyId) {
          const errorMsg = `Missing required data: userId=${userIdToFetch}, companyId=${companyId}`;
          console.error(errorMsg);
          setError(errorMsg);
          setLoading(false);
          return;
        }
        
        // Log the Rollfi API call details
        console.log('=== MAKING ROLLFI API CALL ===');
        console.log('Final parameters for Rollfi API:');
        console.log('- userId:', userIdToFetch);
        console.log('- companyId:', companyId);

        // Get employee details from Rollfi using the service
        console.log('Calling fetchEmployeeDetailsFromService...');
        const employeeData = await fetchEmployeeDetailsFromService(userIdToFetch, companyId);
        
        console.log('=== SERVICE CALL RESPONSE ===');
        console.log('Response data from service:', JSON.stringify(employeeData, null, 2));

        // Process the response data
        if (employeeData) {
          // Log the extracted employee data
          console.log('Employee details successfully extracted:');
          console.log('- Name:', `${employeeData.firstName} ${employeeData.middleName || ''} ${employeeData.lastName}`.trim());
          console.log('- Email:', employeeData.email);
          console.log('- Phone:', employeeData.phoneNumber);
          console.log('- Job Title:', employeeData.jobTitle);
          console.log('- Worker Type:', employeeData.WorkerType?.WorkerType || 'N/A');
          console.log('- Status:', employeeData.status?.userStatus || 'N/A');
          console.log('- Date of Join:', employeeData.dateOfJoin);
          
          // Log wage information if available
          if (employeeData.userWages && employeeData.userWages.length > 0) {
            const wage = employeeData.userWages[0];
            console.log('- Wage Rate:', wage.WageRate);
            console.log('- Wage Basis:', wage.WageBasis?.WageBasis || 'N/A');
            console.log('- Payment Method:', wage.paymentMethod?.PaymentMethod || 'N/A');
            console.log('- Employee Type:', wage.employeeType || 'N/A');
            console.log('- Employment Status:', wage.employmentStatus || 'N/A');
          }
          
          // Log address information if available
          if (employeeData.userAddress) {
            console.log('- Address:', 
              `${employeeData.userAddress.address1}, 
              ${employeeData.userAddress.address2 ? employeeData.userAddress.address2 + ', ' : ''}
              ${employeeData.userAddress.city}, ${employeeData.userAddress.state} ${employeeData.userAddress.zipcode}, 
              ${employeeData.userAddress.country}`
            );
          }
          
          // Log work location if available
          if (employeeData.companyLocation) {
            console.log('- Work Location:', employeeData.companyLocation.companyLocation);
            console.log('- Work Address:', employeeData.companyLocation.address);
          }
          
          // Set the employee details in state
          console.log('Setting employee details in state...');
          setEmployeeDetails(employeeData);
          console.log('Employee details set successfully!');
        } else {
          // Log error if response doesn't contain expected data
          const errorMsg = 'Failed to fetch employee details from Rollfi API';
          console.error('=== ERROR PROCESSING ROLLFI API RESPONSE ===');
          console.error('Error message:', errorMsg);
          // No data to log
          setError('Employee data not found or in unexpected format.');
        }
      } catch (err: any) {
        // Log detailed error information
        console.error('=== ERROR FETCHING EMPLOYEE DETAILS ===');
        console.error('Error type:', err.constructor.name);
        console.error('Error message:', err.message);
        console.error('Error stack:', err.stack);
        
        if (err.name === 'TypeError' && err.message.includes('Failed to fetch')) {
          console.error('Network error: This could be due to CORS, network connectivity, or the API being down');
        }
        
        if (err.response) {
          console.error('Response status:', err.response.status);
          console.error('Response headers:', err.response.headers);
          console.error('Response data:', err.response.data);
        }
        
        // Log the current state and context for debugging
        console.log('=== DEBUGGING CONTEXT ===');
        console.log('Current parameters:');
        console.log('- employeeId:', employeeId);
        console.log('- selectedEmployeeId:', selectedEmployeeId);
        console.log('- isAdminViewingAsEmployee:', isAdminViewingAsEmployee);
        
        console.log('LocalStorage values:');
        console.log('- localStorage.currentRole:', localStorage.getItem('currentRole'));
        console.log('- localStorage.viewingEmployeeId:', localStorage.getItem('viewingEmployeeId'));
        console.log('- localStorage.selectedCompanyId:', localStorage.getItem('selectedCompanyId'));
        console.log('- localStorage.currentUserId:', localStorage.getItem('currentUserId'));
        console.log('- localStorage.userRoles:', localStorage.getItem('userRoles'));
        
        // Set a user-friendly error message
        const errorMessage = err.message || 'Failed to fetch employee details';
        console.error('Setting error state with message:', errorMessage);
        setError(errorMessage);
      } finally {
        console.log('=== EMPLOYEE DETAILS FETCH PROCESS COMPLETED ===');
        console.log('Setting loading state to false');
        setLoading(false);
      }
    };
    
    // Always call getEmployeeDetails - it has fallback logic to use the current user's ID
    // if employeeId is not provided
    getEmployeeDetails();
  }, [selectedEmployeeId]);
  
  const handleSwitchToAdmin = () => {
    if (canSwitchToAdmin) {
      switchToAdminRole();
    }
  };
  
  if (loading) {
    return (
      <div className="flex-1 p-8 bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-md shadow-sm text-center w-full max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500 mx-auto mb-4"></div>
          <div className="text-gray-700 text-lg font-medium mb-2">Loading Employee Details</div>
          <div className="text-gray-500">Please wait while we fetch the employee information...</div>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex-1 p-8 bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-md shadow-sm text-center w-full max-w-md">
          <FiAlertCircle className="mx-auto text-red-500 text-5xl mb-4" />
          <div className="text-gray-700 text-lg font-medium mb-2">Error Loading Data</div>
          <div className="text-gray-500 mb-4">We encountered a problem while fetching employee details:</div>
          <div className="text-red-500 p-3 bg-red-50 rounded-md mb-4">{error}</div>
          <button 
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }
  
  // Even if employeeDetails is null, we'll pass it to EmployeeDetailsView
  // which will handle the empty state appropriately
  
  // Use our new EmployeeDetailsView component
  return (
    <EmployeeDetailsView
      employeeDetails={employeeDetails}
      canSwitchToAdmin={canSwitchToAdmin}
      onSwitchToAdmin={handleSwitchToAdmin}
    />
  );
  
  /* Original UI removed
  <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-xl font-medium mb-4">
              {employeeDetails.firstName} {employeeDetails.middleName || ''} {employeeDetails.lastName}
            </h2>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-500">Email</p>
                <p>{employeeDetails.email}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Phone</p>
                <p>{employeeDetails.phoneNumber}</p>
              </div>
              {employeeDetails.userAddress && (
                <div>
                  <p className="text-sm text-gray-500">Address</p>
                  <p>
                    {employeeDetails.userAddress.address1}
                    {employeeDetails.userAddress.address2 && `, ${employeeDetails.userAddress.address2}`}
                    <br />
                    {employeeDetails.userAddress.city}, {employeeDetails.userAddress.state} {employeeDetails.userAddress.zipcode}
                    <br />
                    {employeeDetails.userAddress.country}
                  </p>
                </div>
              )}
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-3">Employment Information</h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-500">Job Title</p>
                <p>{employeeDetails.jobTitle}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Worker Type</p>
                <p>{employeeDetails.WorkerType?.WorkerType || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Start Date</p>
                <p>{new Date(employeeDetails.dateOfJoin).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <p>{employeeDetails.status?.userStatus || 'N/A'}</p>
              </div>
              {employeeDetails.companyLocation && (
                <div>
                  <p className="text-sm text-gray-500">Location</p>
                  <p>{employeeDetails.companyLocation.companyLocation}</p>
                  <p className="text-xs text-gray-500">{employeeDetails.companyLocation.address}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {employeeDetails.userWages && employeeDetails.userWages.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-4">Wage Information</h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-500">Wage Rate</p>
                <p>${employeeDetails.userWages[0].WageRate}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Wage Basis</p>
                <p>{employeeDetails.userWages[0].WageBasis?.WageBasis || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Payment Method</p>
                <p>{employeeDetails.userWages[0].paymentMethod?.PaymentMethod || 'N/A'}</p>
              </div>
              {employeeDetails.userWages[0].employeeType && (
                <div>
                  <p className="text-sm text-gray-500">Employee Type</p>
                  <p>{employeeDetails.userWages[0].employeeType}</p>
                </div>
              )}
              {employeeDetails.userWages[0].employmentStatus && (
                <div>
                  <p className="text-sm text-gray-500">Employment Status</p>
                  <p>{employeeDetails.userWages[0].employmentStatus}</p>
                </div>
              )}
            </div>
          </div>
        )}
        
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium mb-4">Additional Information</h3>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">KYC Status</p>
              <p>{employeeDetails.kycStatus}</p>
            </div>
            {employeeDetails.companyLocationCategory && (
              <div>
                <p className="text-sm text-gray-500">Location Category</p>
                <p>{employeeDetails.companyLocationCategory.companyLocationCategory}</p>
              </div>
            )}
            {employeeDetails.userWages && employeeDetails.userWages.length > 0 && employeeDetails.userWages[0].employeeRefTaxExempt && (
              <div>
                <p className="text-sm text-gray-500">Tax Exempt Status</p>
                <p>{employeeDetails.userWages[0].employeeRefTaxExempt}</p>
              </div>
            )}
            {employeeDetails.ssn && (
              <div>
                <p className="text-sm text-gray-500">SSN</p>
                <p>***-**-{employeeDetails.ssn.slice(-4)}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  */
};

export default EmployeeDetailsPage;

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "npm:@supabase/supabase-js@2";
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
serve(async (req)=>{
  const SUPABASE_URL = Deno.env.get("SUPABASE_URL");
  const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY");
  const CLIENT_ID = Deno.env.get("ROLLFI_CLIENT_ID");
  const CLIENT_SECRET = Deno.env.get("ROLLFI_CLIENT_SECRET");
  if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !CLIENT_ID || !CLIENT_SECRET) {
    return new Response(JSON.stringify({
      message: "Missing environment variables."
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  const credentials = `${CLIENT_ID}:${CLIENT_SECRET}`;
  const basicAuthHeader = "Basic " + globalThis.btoa(credentials);
  const headers = {
    "Content-Type": "application/json",
    Authorization: basicAuthHeader
  };
  async function fetchUsersByCompanyID(companyId) {
    const res = await fetch("https://sandbox.rollfi.xyz/reports#getUsers", {
      method: "POST",
      headers,
      body: JSON.stringify({
        method: "getUsers",
        companyId
      })
    });
    if (!res.ok) {
      const err = await res.json().catch(()=>({}));
      throw new Error(`Failed to fetch users for company ${companyId}: ${JSON.stringify(err)}`);
    }
    const data = await res.json();
    return data.users ?? [];
  }
  try {
    // Fetch companies
    const companiesRes = await fetch("https://sandbox.rollfi.xyz/reports#getCompanies", {
      method: "POST",
      headers,
      body: JSON.stringify({
        method: "getCompanies"
      })
    });
    if (!companiesRes.ok) {
      const errorData = await companiesRes.json().catch(()=>({}));
      return new Response(JSON.stringify({
        message: "Failed to fetch companies.",
        error: errorData
      }), {
        status: companiesRes.status,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }
    const companiesData = await companiesRes.json();
    if (!Array.isArray(companiesData.Company)) {
      return new Response(JSON.stringify({
        message: "Unexpected response: Company field missing or not an array.",
        data: companiesData
      }), {
        status: 400,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }
    // Prepare companies for DB insert
    const companiesToInsert = companiesData.Company.map((c)=>({
        company_id: c.companyID,
        company_name: c.company
      }));
    // Insert companies into DB (consider using upsert if duplicates possible)
    const { data: insertedCompanies, error: companyInsertError } = await supabase.from("website2_users").insert(companiesToInsert);
    if (companyInsertError) {
      return new Response(JSON.stringify({
        message: "Failed to insert companies.",
        error: companyInsertError.message
      }), {
        status: 500,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }
    // Fetch users for all companies in parallel
    const userFetchPromises = companiesToInsert.map(async (company)=>{
      try {
        const users = await fetchUsersByCompanyID(company.company_id);
        if (!users.length) {
          console.log(`No users found for company ${company.company_id}`);
          return [];
        }
        return users.map((u)=>({
            user_id: u.userId,
            email: u.email ?? null,
            first_name: u.firstName ?? null,
            middle_name: u.middleName ?? null,
            last_name: u.lastName ?? null,
            phone: u.phoneNumber ?? null,
            company_id: u.companyId ?? company.company_id,
            user_status: u.status?.userStatus ?? null,
            worker_type: u.WorkerType?.WorkerType ?? null,
            kyc_status: u.kycStatus ?? null,
            job_title: u.jobTitle ?? null,
            date_of_join: u.dateOfJoin ?? null,
            wage_rate: u.userWages?.[0]?.WageRate ?? null,
            wage_basis: u.userWages?.[0]?.WageBasis?.WageBasis ?? null,
            payment_method: u.userWages?.[0]?.paymentMethod?.PaymentMethod ?? null,
            address1: u.userAddress?.address1 ?? null,
            address2: u.userAddress?.address2 ?? null,
            city: u.userAddress?.city ?? null,
            state: u.userAddress?.state ?? null,
            zipcode: u.userAddress?.zipcode ?? null,
            country: u.userAddress?.country ?? null
          }));
      } catch (e) {
        console.error(`Error fetching users for company ${company.company_id}:`, e);
        return [];
      }
    });
    const allUsersArrays = await Promise.all(userFetchPromises);
    const allUsers = allUsersArrays.flat();
    if (allUsers.length > 0) {
      const { error: userInsertError } = await supabase.from("website2_users").insert(allUsers);
      if (userInsertError) {
        console.error("Error inserting users:", userInsertError);
      }
    }
    return new Response(JSON.stringify({
      message: "Companies and users inserted successfully.",
      companiesInserted: insertedCompanies.length,
      usersInserted: allUsers.length
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json"
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      message: "An error occurred.",
      error: error.message
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
});

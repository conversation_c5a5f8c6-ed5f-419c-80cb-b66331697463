import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, Outlet } from 'react-router-dom';
import { FiRefreshCcw, FiUsers, FiChevronDown, FiCheck } from 'react-icons/fi';
import { supabase } from '../lib/supabaseClient'; // Ensure supabase is imported
import { useAppContext } from '../context/AppContext';
import { adminService } from '../services/adminService';
import { useEmployeeViewStore } from '../store/employeeViewStore';
import api from '../services/apiConfig';
import { utf8ToBase64 } from '../utils/base64';
import { useAuth } from '../context/AuthProvider';

interface Employee {
  id: string;
  name: string;
  details: {
    tasks: Array<{
      id: string;
      title: string;
      status: string;
      dueDate: string;
    }>;
    payroll: {
      nextPayDate: string;
      nextPayAmount: string;
      lastPayDate: string;
      lastPayAmount: string;
    };
  };
}

interface User {
  userID: string;
  user: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  email: string;
  dateOfJoin?: string;
  jobTitle?: string;
}

const BankAccountForm: React.FC = () => {
  console.log('[BankAccountForm] Rendered');
  const navigate = useNavigate();
  const [step, setStep] = useState(1);
  const [method, setMethod] = useState<'plaid' | 'manual'>('plaid');
  const [form, setForm] = useState({
    accountNumber: '',
    routingNumber: '',
    bankName: '',
    accountType: '',
    accountName: '',
    payPercentage: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      const userId = localStorage.getItem('currentUserId');
      const companyId = localStorage.getItem('selectedCompanyId');
      if (!userId || !companyId) throw new Error('Missing user or company ID');
      const username = import.meta.env.VITE_CLIENT_ID;
      const password = import.meta.env.VITE_CLIENT_SECRET;
      const basicAuth = btoa(`${username}:${password}`);
      const payload = {
        method: 'addUserBankAccount',
        userPayAccountEntity: {
          companyId,
          userId,
          accountNumber: form.accountNumber,
          routingNumber: form.routingNumber,
          bankName: form.bankName,
          accountType: form.accountType,
          accountName: form.accountName,
          payPercentage: parseFloat(form.payPercentage)
        }
      };
      console.log('[BankAccountForm] Submitting payload:', payload);
      const response = await fetch('https://sandbox.rollfi.xyz/userPortal#addUserBankAccount', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${basicAuth}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      const data = await response.json();
      console.log('[BankAccountForm] API response:', data);
      if (data.error) throw new Error(data.error.message || 'Failed to add bank account');
      setSuccess('Bank account added successfully!');
      setForm({ accountNumber: '', routingNumber: '', bankName: '', accountType: '', accountName: '', payPercentage: '' });
    } catch (err: any) {
      console.error('[BankAccountForm] API error:', err);
      setError(err.message || 'Failed to add bank account');
    } finally {
      setLoading(false);
    }
  };

  // Step 1: Choose connection method
  if (step === 1) {
    return (
      <div className="bg-white p-6 rounded-lg shadow mb-8" style={{ maxWidth: 480, margin: '0 auto' }}>
        <h2 className="text-xl font-semibold mb-2">Connect your bank account</h2>
        <p className="text-gray-500 mb-4">Choose how you would like to connect your account.</p>
        <div className="mb-6">
          <div
            className={`flex items-center p-4 mb-2 border rounded cursor-pointer ${method === 'plaid' ? 'bg-gray-100 border-black' : 'bg-white border-gray-300'}`}
            onClick={() => setMethod('plaid')}
          >
            <span className="mr-3">🏦</span>
            <span className="font-medium">Plaid connection</span>
            <span className="ml-auto">
              <input type="radio" checked={method === 'plaid'} readOnly />
            </span>
          </div>
          <div
            className={`flex items-center p-4 border rounded cursor-pointer ${method === 'manual' ? 'bg-gray-100 border-black' : 'bg-white border-gray-300'}`}
            onClick={() => setMethod('manual')}
          >
            <span className="mr-3">🔗</span>
            <span className="font-medium">Manual account linking</span>
            <span className="ml-auto">
              <input type="radio" checked={method === 'manual'} readOnly />
            </span>
          </div>
        </div>
        <div className="flex justify-between">
          <button type="button" className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400" onClick={() => navigate('/employee/dashboard')}>Back</button>
          <button type="button" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700" onClick={() => setStep(2)}>Continue</button>
        </div>
      </div>
    );
  }

  // Step 2: Plaid or Manual
  if (step === 2 && method === 'plaid') {
    return (
      <div className="bg-white p-6 rounded-lg shadow mb-8" style={{ maxWidth: 480, margin: '0 auto' }}>
        <h2 className="text-xl font-semibold mb-2">Plaid connection</h2>
        <p className="mb-6">Plaid integration coming soon.</p>
        <div className="flex justify-between">
          <button type="button" className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400" onClick={() => setStep(1)}>Back</button>
          <button type="button" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700" onClick={() => navigate('/employee/dashboard')}>Cancel</button>
        </div>
      </div>
    );
  }

  if (step === 2 && method === 'manual') {
    return (
      <div className="bg-white p-6 rounded-lg shadow mb-8" style={{ maxWidth: 480, margin: '0 auto' }}>
        <h2 className="text-xl font-semibold mb-2">Manual account linking</h2>
        {success && <div className="mb-2 text-green-600">{success}</div>}
        {error && <div className="mb-2 text-red-600">{error}</div>}
        <form onSubmit={handleSubmit} className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Account Number</label>
            <input type="text" name="accountNumber" value={form.accountNumber} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Routing Number</label>
            <input type="text" name="routingNumber" value={form.routingNumber} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
          </div>
          <div> 
            <label className="block text-sm font-medium mb-1">Bank Name</label>
            <input type="text" name="bankName" value={form.bankName} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Account Type</label>
            <select name="accountType" value={form.accountType} onChange={handleChange} className="w-full border px-3 py-2 rounded" required>
              <option value="">Select</option>
              <option value="savings">Savings</option>
              <option value="checking">Checking</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Account Name</label>
            <input type="text" name="accountName" value={form.accountName} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Pay Percentage</label>
            <input type="number" name="payPercentage" value={form.payPercentage} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" max="100" step="0.01" required />
          </div>
          <div className="col-span-2 flex justify-between">
            <button type="button" className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400" onClick={() => setStep(1)}>Back</button>
            <button type="submit" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700" disabled={loading}>{loading ? 'Saving...' : 'Add Bank Account'}</button>
          </div>
        </form>
      </div>
    );
  }

  return null;
};

const FederalW4Form: React.FC = () => {
  const [form, setForm] = useState({
    w4FilingStatus: '',
    haveMultipleJob: false,
    dependents: '',
    dependentsAbove18: '',
    otherIncome: '',
    otherDeduction: '',
    extraWithholding: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox' && e.target instanceof HTMLInputElement) {
      setForm({ ...form, [name]: e.target.checked });
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      const userId = localStorage.getItem('currentUserId');
      if (!userId) throw new Error('Missing user ID');
      const username = import.meta.env.VITE_CLIENT_ID;
      const password = import.meta.env.VITE_CLIENT_SECRET;
      const basicAuth = btoa(`${username}:${password}`);
      const payload = {
        method: 'addW4Information',
        w4Information: {
          userId,
          w4FilingStatus: form.w4FilingStatus,
          haveMultipleJob: form.haveMultipleJob,
          dependents: parseInt(form.dependents) || 0,
          dependentsAbove18: parseInt(form.dependentsAbove18) || 0,
          otherIncome: parseFloat(form.otherIncome) || 0,
          otherDeduction: parseFloat(form.otherDeduction) || 0,
          extraWithholding: parseFloat(form.extraWithholding) || 0
        }
      };
      const response = await fetch('https://sandbox.rollfi.xyz/userOnboarding#addW4Information', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${basicAuth}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      const data = await response.json();
      if (data.error) throw new Error(data.error.message || 'Failed to add W-4 info');
      setSuccess('Federal W-4 information added successfully!');
      setForm({ w4FilingStatus: '', haveMultipleJob: false, dependents: '', dependentsAbove18: '', otherIncome: '', otherDeduction: '', extraWithholding: '' });
    } catch (err: any) {
      setError(err.message || 'Failed to add W-4 info');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Add Federal W-4 Information</h2>
        <button className="text-teal-600 border border-teal-600 px-3 py-1 rounded hover:bg-teal-50" disabled>Edit</button>
      </div>
      {success && <div className="mb-2 text-green-600">{success}</div>}
      {error && <div className="mb-2 text-red-600">{error}</div>}
      <form onSubmit={handleSubmit} className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Filing Status</label>
          <select name="w4FilingStatus" value={form.w4FilingStatus} onChange={handleChange} className="w-full border px-3 py-2 rounded" required>
            <option value="">Select</option>
            <option value="Single">Single</option>
            <option value="Married">Married</option>
            <option value="Head of household">Head of household</option>
          </select>
        </div>
        <div className="flex items-center mt-6">
          <input type="checkbox" name="haveMultipleJob" checked={form.haveMultipleJob} onChange={handleChange} className="mr-2" />
          <label className="text-sm font-medium">Have Multiple Jobs?</label>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Dependents</label>
          <input type="number" name="dependents" value={form.dependents} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Dependents Above 18</label>
          <input type="number" name="dependentsAbove18" value={form.dependentsAbove18} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Other Income</label>
          <input type="number" name="otherIncome" value={form.otherIncome} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" step="0.01" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Other Deduction</label>
          <input type="number" name="otherDeduction" value={form.otherDeduction} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" step="0.01" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Extra Withholding</label>
          <input type="number" name="extraWithholding" value={form.extraWithholding} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" step="0.01" required />
        </div>
        <div className="col-span-2 flex justify-end">
          <button type="submit" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700" disabled={loading}>{loading ? 'Saving...' : 'Add W-4 Info'}</button>
        </div>
      </form>
    </div>
  );
};

const StateW4Form: React.FC = () => {
  const [form, setForm] = useState({
    filingStatus: '',
    withholdingAllowance: '',
    additionalWithholding: '',
    nycWithholdingAllowance: '',
    nycAdditionalWithholding: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      const employeeId = localStorage.getItem('currentUserId');
      if (!employeeId) throw new Error('Missing employee ID');
      const username = import.meta.env.VITE_CLIENT_ID;
      const password = import.meta.env.VITE_CLIENT_SECRET;
      const basicAuth = btoa(`${username}:${password}`);
      const payload = {
        method: 'addStateW4Information',
        employeeId,
        stateW4Information: {
          'Filing Status': form.filingStatus,
          'Withholding Allowance': form.withholdingAllowance,
          'Additional Withholding': form.additionalWithholding,
          'NYC Withholding Allowance': form.nycWithholdingAllowance,
          'NYC Additional Withholding': form.nycAdditionalWithholding
        }
      };
      const response = await fetch('https://sandbox.rollfi.xyz/userOnboarding#addStateW4Information', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${basicAuth}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      const data = await response.json();
      if (data.error) throw new Error(data.error.message || 'Failed to add state W-4 info');
      setSuccess('State W-4 information added successfully!');
      setForm({ filingStatus: '', withholdingAllowance: '', additionalWithholding: '', nycWithholdingAllowance: '', nycAdditionalWithholding: '' });
    } catch (err: any) {
      setError(err.message || 'Failed to add state W-4 info');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Add State W-4 Information</h2>
        <button className="text-teal-600 border border-teal-600 px-3 py-1 rounded hover:bg-teal-50" disabled>Edit</button>
      </div>
      {success && <div className="mb-2 text-green-600">{success}</div>}
      {error && <div className="mb-2 text-red-600">{error}</div>}
      <form onSubmit={handleSubmit} className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Filing Status</label>
          <input type="text" name="filingStatus" value={form.filingStatus} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Withholding Allowance</label>
          <input type="number" name="withholdingAllowance" value={form.withholdingAllowance} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Additional Withholding</label>
          <input type="number" name="additionalWithholding" value={form.additionalWithholding} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" step="0.01" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">NYC Withholding Allowance</label>
          <input type="number" name="nycWithholdingAllowance" value={form.nycWithholdingAllowance} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">NYC Additional Withholding</label>
          <input type="number" name="nycAdditionalWithholding" value={form.nycAdditionalWithholding} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" step="0.01" required />
        </div>
        <div className="col-span-2 flex justify-end">
          <button type="submit" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700" disabled={loading}>{loading ? 'Saving...' : 'Add State W-4 Info'}</button>
        </div>
      </form>
    </div>
  );
};

// --- Form wrappers to call onComplete after submit ---
function BankAccountFormWithCallback({ onComplete }: { onComplete: () => void }) {
  const [form, setForm] = useState({
    accountNumber: '',
    routingNumber: '',
    bankName: '',
    accountType: '',
    accountName: '',
    payPercentage: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      const userId = localStorage.getItem('currentUserId');
      const companyId = localStorage.getItem('selectedCompanyId');
      if (!userId || !companyId) throw new Error('Missing user or company ID');
      const username = import.meta.env.VITE_CLIENT_ID;
      const password = import.meta.env.VITE_CLIENT_SECRET;
      const basicAuth = btoa(`${username}:${password}`);
      const payload = {
        method: 'addUserBankAccount',
        userPayAccountEntity: {
          companyId,
          userId,
          accountNumber: form.accountNumber,
          routingNumber: form.routingNumber,
          bankName: form.bankName,
          accountType: form.accountType,
          accountName: form.accountName,
          payPercentage: parseFloat(form.payPercentage)
        }
      };
      const response = await fetch('https://sandbox.rollfi.xyz/userPortal#addUserBankAccount', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${basicAuth}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      const data = await response.json();
      if (data.error) throw new Error(data.error.message || 'Failed to add bank account');
      setSuccess('Bank account added successfully!');
      setForm({ accountNumber: '', routingNumber: '', bankName: '', accountType: '', accountName: '', payPercentage: '' });
    } catch (err: any) {
      setError(err.message || 'Failed to add bank account');
    } finally {
      setLoading(false);
      onComplete(); // Call the onComplete prop
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Add Bank Account</h2>
        <button className="text-teal-600 border border-teal-600 px-3 py-1 rounded hover:bg-teal-50" disabled>Edit</button>
      </div>
      {success && <div className="mb-2 text-green-600">{success}</div>}
      {error && <div className="mb-2 text-red-600">{error}</div>}
      <form onSubmit={handleSubmit} className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Account Number</label>
          <input type="text" name="accountNumber" value={form.accountNumber} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Routing Number</label>
          <input type="text" name="routingNumber" value={form.routingNumber} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Bank Name</label>
          <input type="text" name="bankName" value={form.bankName} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Account Type</label>
          <select name="accountType" value={form.accountType} onChange={handleChange} className="w-full border px-3 py-2 rounded" required>
            <option value="">Select</option>
            <option value="savings">Savings</option>
            <option value="checking">Checking</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Account Name</label>
          <input type="text" name="accountName" value={form.accountName} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Pay Percentage</label>
          <input type="number" name="payPercentage" value={form.payPercentage} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" max="100" step="0.01" required />
        </div>
        <div className="col-span-2 flex justify-end">
          <button type="submit" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700" disabled={loading}>{loading ? 'Saving...' : 'Add Bank Account'}</button>
        </div>
      </form>
    </div>
  );
}
function FederalW4FormWithCallback({ onComplete }: { onComplete: () => void }) {
  const [form, setForm] = useState({
    w4FilingStatus: '',
    haveMultipleJob: false,
    dependents: '',
    dependentsAbove18: '',
    otherIncome: '',
    otherDeduction: '',
    extraWithholding: '',
    hasMilitarySpouseExemption: false,
    stateAdditionalDeduction: '',
    isNonResident: false,
    azDeductionPercent: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox' && e.target instanceof HTMLInputElement) {
      setForm({ ...form, [name]: e.target.checked });
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      const userId = localStorage.getItem('currentUserId');
      if (!userId) throw new Error('Missing user ID');
      const username = import.meta.env.VITE_CLIENT_ID;
      const password = import.meta.env.VITE_CLIENT_SECRET;
      const basicAuth = btoa(`${username}:${password}`);
      const payload = {
        method: 'addW4Information',
        w4Information: {
          userId,
          w4FilingStatus: form.w4FilingStatus,
          haveMultipleJob: form.haveMultipleJob,
          dependents: form.dependents === '' ? undefined : parseInt(form.dependents),
          dependentsAbove18: form.dependentsAbove18 === '' ? undefined : parseInt(form.dependentsAbove18),
          otherIncome: form.otherIncome === '' ? undefined : parseInt(form.otherIncome),
          otherDeduction: form.otherDeduction === '' ? undefined : parseInt(form.otherDeduction),
          extraWithholding: form.extraWithholding === '' ? undefined : parseInt(form.extraWithholding),
          hasMilitarySpouseExemption: form.hasMilitarySpouseExemption,
          stateAdditionalDeduction: form.stateAdditionalDeduction === '' ? undefined : parseInt(form.stateAdditionalDeduction),
          isNonResident: form.isNonResident,
          azDeductionPercent: form.azDeductionPercent === '' ? undefined : parseInt(form.azDeductionPercent)
        }
      };
      const response = await fetch('https://sandbox.rollfi.xyz/userOnboarding#addW4Information', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${basicAuth}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      const data = await response.json();
      if (data.error) throw new Error(data.error.message || 'Failed to add W-4 info');
      setSuccess('Federal W-4 information added successfully!');
      setForm({
        w4FilingStatus: '',
        haveMultipleJob: false,
        dependents: '',
        dependentsAbove18: '',
        otherIncome: '',
        otherDeduction: '',
        extraWithholding: '',
        hasMilitarySpouseExemption: false,
        stateAdditionalDeduction: '',
        isNonResident: false,
        azDeductionPercent: ''
      });
    } catch (err: any) {
      setError(err.message || 'Failed to add W-4 info');
    } finally {
      setLoading(false);
      onComplete(); // Call the onComplete prop
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Add Federal W-4 Information</h2>
        <button className="text-teal-600 border border-teal-600 px-3 py-1 rounded hover:bg-teal-50" disabled>Edit</button>
      </div>
      {success && <div className="mb-2 text-green-600">{success}</div>}
      {error && <div className="mb-2 text-red-600">{error}</div>}
      <form onSubmit={handleSubmit} className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Filing Status <span className="text-red-500">*</span></label>
          <select name="w4FilingStatus" value={form.w4FilingStatus} onChange={handleChange} className="w-full border px-3 py-2 rounded" required>
            <option value="">Select</option>
            <option value="Single">Single</option>
            <option value="Married filing jointly">Married filing jointly</option>
            <option value="Head of household">Head of household</option>
            <option value="Married Qualifying widow(er)">Married Qualifying widow(er)</option>
            <option value="Married Filing Separately">Married Filing Separately</option>
          </select>
        </div>
        <div className="flex items-center mt-6">
          <input type="checkbox" name="haveMultipleJob" checked={form.haveMultipleJob} onChange={handleChange} className="mr-2" />
          <label className="text-sm font-medium">Have Multiple Jobs?</label>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Dependents</label>
          <input type="number" name="dependents" value={form.dependents} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Dependents Above 18</label>
          <input type="number" name="dependentsAbove18" value={form.dependentsAbove18} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Other Income</label>
          <input type="number" name="otherIncome" value={form.otherIncome} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Other Deduction</label>
          <input type="number" name="otherDeduction" value={form.otherDeduction} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Extra Withholding</label>
          <input type="number" name="extraWithholding" value={form.extraWithholding} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" />
        </div>
        <div className="flex items-center mt-6">
          <input type="checkbox" name="hasMilitarySpouseExemption" checked={form.hasMilitarySpouseExemption} onChange={handleChange} className="mr-2" />
          <label className="text-sm font-medium">Has Military Spouse Exemption?</label>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">State Additional Deduction</label>
          <input type="number" name="stateAdditionalDeduction" value={form.stateAdditionalDeduction} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" />
        </div>
        <div className="flex items-center mt-6">
          <input type="checkbox" name="isNonResident" checked={form.isNonResident} onChange={handleChange} className="mr-2" />
          <label className="text-sm font-medium">Is Non-Resident?</label>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">AZ Deduction Percent</label>
          <input type="number" name="azDeductionPercent" value={form.azDeductionPercent} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" max="100" />
        </div>
        <div className="col-span-2 flex justify-end">
          <button type="submit" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700" disabled={loading}>{loading ? 'Saving...' : 'Add W-4 Info'}</button>
        </div>
      </form>
    </div>
  );
}
function StateW4FormWithCallback({ onComplete }: { onComplete: () => void }) {
  const [form, setForm] = useState({
    filingStatus: '',
    withholdingAllowance: '',
    additionalWithholding: '',
    nycWithholdingAllowance: '',
    nycAdditionalWithholding: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      const employeeId = localStorage.getItem('currentUserId');
      if (!employeeId) throw new Error('Missing employee ID');
      const username = import.meta.env.VITE_CLIENT_ID;
      const password = import.meta.env.VITE_CLIENT_SECRET;
      const basicAuth = btoa(`${username}:${password}`);
      const payload = {
        method: 'addStateW4Information',
        employeeId,
        stateW4Information: {
          'Filing Status': form.filingStatus,
          'Withholding Allowance': form.withholdingAllowance,
          'Additional Withholding': form.additionalWithholding,
          'NYC Withholding Allowance': form.nycWithholdingAllowance,
          'NYC Additional Withholding': form.nycAdditionalWithholding
        }
      };
      const response = await fetch('https://sandbox.rollfi.xyz/userOnboarding#addStateW4Information', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${basicAuth}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      const data = await response.json();
      if (data.error) throw new Error(data.error.message || 'Failed to add state W-4 info');
      setSuccess('State W-4 information added successfully!');
      setForm({ filingStatus: '', withholdingAllowance: '', additionalWithholding: '', nycWithholdingAllowance: '', nycAdditionalWithholding: '' });
    } catch (err: any) {
      setError(err.message || 'Failed to add state W-4 info');
    } finally {
      setLoading(false);
      onComplete(); // Call the onComplete prop
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Add State W-4 Information</h2>
        <button className="text-teal-600 border border-teal-600 px-3 py-1 rounded hover:bg-teal-50" disabled>Edit</button>
      </div>
      {success && <div className="mb-2 text-green-600">{success}</div>}
      {error && <div className="mb-2 text-red-600">{error}</div>}
      <form onSubmit={handleSubmit} className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Filing Status</label>
          <input type="text" name="filingStatus" value={form.filingStatus} onChange={handleChange} className="w-full border px-3 py-2 rounded" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Withholding Allowance</label>
          <input type="number" name="withholdingAllowance" value={form.withholdingAllowance} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Additional Withholding</label>
          <input type="number" name="additionalWithholding" value={form.additionalWithholding} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" step="0.01" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">NYC Withholding Allowance</label>
          <input type="number" name="nycWithholdingAllowance" value={form.nycWithholdingAllowance} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" required />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">NYC Additional Withholding</label>
          <input type="number" name="nycAdditionalWithholding" value={form.nycAdditionalWithholding} onChange={handleChange} className="w-full border px-3 py-2 rounded" min="0" step="0.01" required />
        </div>
        <div className="col-span-2 flex justify-end">
          <button type="submit" className="bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700" disabled={loading}>{loading ? 'Saving...' : 'Add State W-4 Info'}</button>
        </div>
      </form>
    </div>
  );
}

const EmployeeDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [tasks, setTasks] = useState<any[]>([]);
  const [tasksLoading, setTasksLoading] = useState(false);
  const [tasksError, setTasksError] = useState<string | null>(null);
  const [canSwitchToAdmin, setCanSwitchToAdmin] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAdminViewingAsEmployee, setIsAdminViewingAsEmployee] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const { switchToAdminRole, currentRole } = useAppContext();
  const { selectedEmployeeId, setSelectedEmployee: setViewEmployee, setIsAdminViewingAsEmployee: setAdminViewing } = useEmployeeViewStore();

  // State to store the current user's name
  const [currentUserName, setCurrentUserName] = useState<string>('');
  const [nameError, setNameError] = useState<string | null>(null);

  // State for selected employee details
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
  const [selectedEmployeeTasks, setSelectedEmployeeTasks] = useState<any[]>([]);
  const [loadingEmployeeDetails, setLoadingEmployeeDetails] = useState(false);
  const [employeeDetailsError, setEmployeeDetailsError] = useState<string | null>(null);

  // State to track if current user is business user/admin
  const [isBusinessUser, setIsBusinessUser] = useState(false);

  // State for dropdown functionality
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Handle clicking outside dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Map task name/description to action
  const handleTaskComplete = (task: any) => {
    console.log('[handleTaskComplete] called with task:', task);
    const name = (task.taskName || task.type || '').toLowerCase();
    const desc = (task.taskDescription || task.description || '').toLowerCase();
    const isBank =
      name.includes('bank account') ||
      desc.includes('bank account') ||
      name.includes('direct deposit') ||
      desc.includes('direct deposit') ||
      name.includes('deposit account') ||
      desc.includes('deposit account') ||
      name.includes('funding account') ||
      desc.includes('funding account') ||
      name.includes('add account') ||
      desc.includes('add account') ||
      name.includes('link account') ||
      desc.includes('link account');
    const isW4 =
      (/w-?4/.test(name) && !name.includes('state')) ||
      (/w-?4/.test(desc) && !desc.includes('state')) ||
      name.includes('federal tax') ||
      desc.includes('federal tax');
    const isStateW4 =
      name.includes('state w4') ||
      desc.includes('state w4') ||
      name.includes('state tax') ||
      desc.includes('state tax');
    if (isBank) {
      console.log('[handleTaskComplete] Navigating to /employee/dashboard/bankaccount');
      navigate('/employee/dashboard/bankaccount');
    } else if (isW4) {
      console.log('[handleTaskComplete] Navigating to /employee/dashboard/w4info');
      navigate('/employee/dashboard/w4info');
    } else if (isStateW4) {
      console.log('[handleTaskComplete] Navigating to /employee/dashboard/statew4');
      navigate('/employee/dashboard/statew4');
    } else {
      console.log('[handleTaskComplete] No navigation triggered for this task.');
    }
  };

  // Fetch users using the correct Rollfi API endpoint
  const fetchUsers = async () => {
    try {
      console.log('[EmployeeDashboard] Fetching users for admin view...');
      setLoadingUsers(true);

      // First, try to get the selected company ID from localStorage (from company selection)
      let companyId = localStorage.getItem('selectedCompanyId');
      console.log('[EmployeeDashboard] Company ID from localStorage:', companyId);

      // If no company ID in localStorage, get it from user profile
      if (!companyId) {
        console.log('[EmployeeDashboard] No company ID in localStorage, fetching from user profile...');
        const { data: { session } } = await supabase.auth.getSession();

        if (!session?.user?.email) {
          throw new Error('No user session found');
        }

        const { data: userProfile, error: profileError } = await supabase
          .from('temp_website2_users')
          .select('company_id, user_id, is_business_user')
          .eq('email', session.user.email)
          .single();

        if (profileError) {
          throw new Error('Error fetching user profile');
        }

        if (!userProfile || !userProfile.company_id) {
          throw new Error('No company ID found for user');
        }

        companyId = userProfile.company_id;
        if (companyId) {
          localStorage.setItem('selectedCompanyId', companyId);
        }
        if (userProfile.user_id) {
          localStorage.setItem('currentUserId', userProfile.user_id);
        }
      }

      if (!companyId) {
        throw new Error('Company ID not found');
      }

      // Use the correct Rollfi API endpoint for getting users
      const requestBody = {
        method: 'getUsers',
        companyId: companyId.toUpperCase()
      };

      console.log('[EmployeeDashboard] Fetching users with payload:', requestBody);

      // Get credentials for API call
      const username = import.meta.env.VITE_CLIENT_ID;
      const password = import.meta.env.VITE_CLIENT_SECRET;
      const basicAuth = btoa(`${username}:${password}`);

      const response = await api.post('/reports#getUsers', requestBody, {
        headers: {
          'Authorization': `Basic ${basicAuth}`,
          'Content-Type': 'application/json'
        }
      });
      const data = response.data;

      console.log('[EmployeeDashboard] Users API response:', data);

      if (data && data.users && Array.isArray(data.users)) {
        const mappedUsers = data.users.map((user: any) => ({
          userID: user.userId,
          user: user.user || `${user.firstName || ''} ${user.lastName || ''}`.trim(),
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          dateOfJoin: user.dateOfJoin,
          jobTitle: user.jobTitle
        }));
        setUsers(mappedUsers);
        console.log('[EmployeeDashboard] Mapped users:', mappedUsers.length, 'users found');
      } else {
        setUsers([]);
        console.log('[EmployeeDashboard] No users found in response');
      }
    } catch (err: any) {
      console.error('[EmployeeDashboard] Error fetching users:', err);
      setUsers([]);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Fetch employee details using Rollfi API
  const fetchEmployeeDetails = async (userId: string, companyId: string) => {
    try {
      console.log('[EmployeeDashboard] Fetching employee details for:', userId);
      setLoadingEmployeeDetails(true);
      setEmployeeDetailsError(null);

      const requestBody = {
        method: 'getUser',
        companyId: companyId.toUpperCase(),
        userId: userId.toUpperCase()
      };

      console.log('[EmployeeDashboard] Employee details request:', requestBody);
      const response = await api.post('/reports#getUser', requestBody);
      const data = response.data;

      console.log('[EmployeeDashboard] Employee details response:', data);

      if (data && data.user && Array.isArray(data.user) && data.user.length > 0) {
        const employeeData = data.user[0];
        setSelectedEmployee(employeeData);
        console.log('[EmployeeDashboard] Employee details set:', employeeData);

        // Also fetch tasks for this employee
        await fetchEmployeeTasks(userId);
      } else {
        setEmployeeDetailsError('Employee details not found');
        console.log('[EmployeeDashboard] No employee details found');
      }
    } catch (err: any) {
      console.error('[EmployeeDashboard] Error fetching employee details:', err);
      setEmployeeDetailsError(err.message || 'Failed to fetch employee details');
    } finally {
      setLoadingEmployeeDetails(false);
    }
  };

  // Fetch tasks for selected employee
  const fetchEmployeeTasks = async (userId: string) => {
    try {
      console.log('[EmployeeDashboard] Fetching tasks for employee:', userId);

      const requestBody = {
        method: 'getUserTask',
        userId: userId.toUpperCase()
      };

      const response = await api.post('/reports#getUserTask', requestBody);
      const data = response.data;

      console.log('[EmployeeDashboard] Employee tasks response:', data);

      if (data && Array.isArray(data.task)) {
        setSelectedEmployeeTasks(data.task);
        console.log('[EmployeeDashboard] Employee tasks set:', data.task);
      } else {
        setSelectedEmployeeTasks([]);
        console.log('[EmployeeDashboard] No tasks found for employee');
      }
    } catch (err: any) {
      console.error('[EmployeeDashboard] Error fetching employee tasks:', err);
      setSelectedEmployeeTasks([]);
    }
  };

  // Handle user selection - now fetches details instead of navigating
  const handleUserSelect = async (userId: string) => {
    console.log('[EmployeeDashboard] User selected:', userId);
    const companyId = localStorage.getItem('selectedCompanyId');

    if (!companyId) {
      setEmployeeDetailsError('Company ID not found');
      return;
    }

    setViewEmployee(userId);
    setAdminViewing(true);
    localStorage.setItem('viewingEmployeeId', userId);
    localStorage.setItem('currentRole', 'admin-as-employee');

    // Fetch employee details and display in current dashboard
    await fetchEmployeeDetails(userId, companyId);
  };
  
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        console.log('[EmployeeDashboard] Starting fetchUserData...');
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user?.email) {
          console.log('[EmployeeDashboard] User session found, email:', session.user.email);
          const { data: userProfile, error: profileError } = await supabase
            .from('temp_website2_users')
            .select('user_id, company_id, first_name, middle_name, last_name, is_business_user')
            .eq('email', session.user.email)
            .single();

          if (profileError) {
            console.error('[EmployeeDashboard] Error fetching user profile:', profileError);
            setNameError('Could not fetch user profile from Supabase.');
            throw profileError;
          }

          if (userProfile) {
            console.log('[EmployeeDashboard] User profile found:', userProfile);

            // Set user name from Supabase
            const first = userProfile.first_name || '';
            const middle = userProfile.middle_name || '';
            const last = userProfile.last_name || '';
            const fullName = [first, middle, last].filter(Boolean).join(' ').trim();

            if (fullName) {
              setCurrentUserName(fullName);
              setNameError(null);
              console.log('[EmployeeDashboard] User name set:', fullName);
            } else {
              setCurrentUserName('');
              setNameError('Name is missing from your user profile.');
              console.warn('[EmployeeDashboard] No name found in user profile');
            }

            // Use is_business_user for admin switching logic
            const businessUser = userProfile.is_business_user || false;
            setCanSwitchToAdmin(businessUser);
            setIsBusinessUser(businessUser);
            localStorage.setItem('userRoles', JSON.stringify({ isBusinessUser: businessUser }));

            // If business user, automatically fetch users for dropdown
            if (businessUser) {
              console.log('[EmployeeDashboard] Business user detected, fetching users...');
              fetchUsers();
            }

            // Store IDs with logging
            if (userProfile.company_id) {
              localStorage.setItem('selectedCompanyId', userProfile.company_id);
              console.log('[EmployeeDashboard] Company ID stored:', userProfile.company_id);
            }
            if (userProfile.user_id) {
              localStorage.setItem('currentUserId', userProfile.user_id);
              console.log('[EmployeeDashboard] Current User ID stored:', userProfile.user_id);
            } else {
              console.error('[EmployeeDashboard] No user_id found in profile!');
              setError('User ID is missing from your profile. Please contact support.');
            }
          } else {
            console.error('[EmployeeDashboard] No user profile found');
            setNameError('User profile not found.');
          }
        } else {
          console.error('[EmployeeDashboard] No user session found');
          setNameError('You are not logged in.');
        }

        const storedRole = localStorage.getItem('currentRole');
        if (storedRole === 'admin-as-employee') {
          setIsAdminViewingAsEmployee(true);
          fetchUsers();
        }
      } catch (err: any) {
        console.error('[EmployeeDashboard] Error in user data fetch:', err);
        setError('Failed to initialize dashboard.');
        // Fallback to localStorage for roles on error
        const storedRoles = localStorage.getItem('userRoles');
        if (storedRoles) {
          const roles = JSON.parse(storedRoles);
          setCanSwitchToAdmin(roles.isBusinessUser);
        }
      }
    };

    fetchUserData();
  }, [currentRole, navigate]);

  useEffect(() => {
    const fetchTasks = async () => {
      if (!user?.id) {
        console.log('[EmployeeDashboard] No user ID available, skipping task fetch');
        return;
      }

      // Skip fetching tasks for business users - they need to select an employee first
      if (isBusinessUser) {
        console.log('[EmployeeDashboard] Business user detected, skipping own task fetch');
        return;
      }

      console.log('[EmployeeDashboard] Starting fetchTasks...');
      setTasksLoading(true);
      setTasksError(null);

      try {
        // Get credentials from environment variables or config
        const username = import.meta.env.VITE_CLIENT_ID;
        const password = import.meta.env.VITE_CLIENT_SECRET;
        const basicAuth = btoa(`${username}:${password}`);
        console.log('[EmployeeDashboard] Auth header prepared');

        // Get employeeId from localStorage (set earlier from Supabase user_id)
        let employeeId = localStorage.getItem('currentUserId');
        console.log('[EmployeeDashboard] Employee ID from localStorage:', employeeId);

        // If no employeeId in localStorage, try to get it from Supabase
        if (!employeeId) {
          console.log('[EmployeeDashboard] No employee ID in localStorage, fetching from Supabase...');
          const { data: { session } } = await supabase.auth.getSession();

          if (session?.user?.email) {
            const { data: userProfile, error: profileError } = await supabase
              .from('temp_website2_users')
              .select('user_id')
              .eq('email', session.user.email)
              .single();

            if (!profileError && userProfile?.user_id) {
              employeeId = userProfile.user_id;
              localStorage.setItem('currentUserId', userProfile.user_id);
              console.log('[EmployeeDashboard] Employee ID fetched from Supabase and stored:', employeeId);
            } else {
              console.error('[EmployeeDashboard] Failed to fetch user_id from Supabase:', profileError);
            }
          }
        }

        if (!employeeId) {
          throw new Error('Employee ID not found. Please try logging out and logging back in.');
        }

        console.log('[EmployeeDashboard] Making API call with employee ID:', employeeId);
        const requestBody = {
          method: 'getUserTask',
          userId: employeeId.toUpperCase()
        };

        const response = await api.post('/reports#getUserTask', requestBody, {
          headers: {
            'Authorization': `Basic ${basicAuth}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('[EmployeeDashboard] API response:', response.data);
        if (response.data && Array.isArray(response.data.task)) {
            setTasks(response.data.task);
            console.log('[EmployeeDashboard] Tasks set:', response.data.task.length, 'tasks');
        } else {
            setTasks([]);
            console.log('[EmployeeDashboard] No tasks found or invalid response structure');
        }
      } catch (err: any) {
        console.error('[EmployeeDashboard] Error fetching tasks:', err);
        setTasksError(err.message || 'Failed to fetch tasks');
      } finally {
        setTasksLoading(false);
      }
    };

    // Add a small delay to ensure user data is loaded first
    const timer = setTimeout(fetchTasks, 1000);
    return () => clearTimeout(timer);
  }, [user, currentUserName, isBusinessUser]); // Also depend on isBusinessUser

  const handleSwitchToAdmin = () => {
    if (canSwitchToAdmin) {
      switchToAdminRole();
    }
  };
  


  // Clear selected employee and return to business user's own view
  const clearSelectedEmployee = () => {
    setSelectedEmployee(null);
    setSelectedEmployeeTasks([]);
    setEmployeeDetailsError(null);
    setViewEmployee(null);
    setAdminViewing(false);
    localStorage.removeItem('viewingEmployeeId');
    localStorage.removeItem('currentRole');
    console.log('[EmployeeDashboard] Cleared selected employee');
  };

  return (
    <div className="flex-1 p-8 bg-gray-50">
      {/* General error display */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <strong>Error:</strong> {error}
        </div>
      )}



      <div className="flex justify-between items-center mb-8">
        <div>
          {isBusinessUser && selectedEmployee ? (
            <>
              <h2 className="text-lg text-gray-600 mb-1">Viewing Employee Dashboard for:</h2>
              <h1 className="text-2xl font-medium">
                {selectedEmployee.firstName} {selectedEmployee.lastName}
              </h1>
              <p className="text-sm text-gray-500">
                {selectedEmployee.jobTitle} • {selectedEmployee.email}
              </p>
            </>
          ) : isBusinessUser && !selectedEmployee ? (
            <>
              <h2 className="text-lg text-gray-600 mb-1">Employee Dashboard</h2>
              <h1 className="text-2xl font-medium">Select an Employee to View</h1>
              <p className="text-sm text-gray-500">Choose an employee from the dropdown to view their dashboard and tasks</p>
            </>
          ) : (
            <>
              <h2 className="text-lg text-gray-600 mb-1">Welcome,</h2>
              <h1 className="text-2xl font-medium">{currentUserName || '...'}</h1>
              {nameError && <div className="text-red-500 text-sm mt-1">{nameError}</div>}
            </>
          )}
        </div>
        <div className="flex space-x-3">
          {isBusinessUser && (
            <div className="flex space-x-3">
              {/* Employee Dropdown */}
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={() => {
                    setIsDropdownOpen(!isDropdownOpen);
                    if (!isDropdownOpen && users.length === 0) {
                      fetchUsers();
                    }
                  }}
                  className="flex items-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 min-w-[200px] justify-between"
                >
                  <div className="flex items-center">
                    <FiUsers className="mr-2" />
                    <span>
                      {selectedEmployee
                        ? `${selectedEmployee.firstName} ${selectedEmployee.lastName}`
                        : 'Select Employee'
                      }
                    </span>
                  </div>
                  <FiChevronDown className={`ml-2 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                </button>

                {/* Dropdown Menu */}
                {isDropdownOpen && (
                  <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                    {loadingUsers ? (
                      <div className="px-4 py-3 text-gray-500 text-center">Loading employees...</div>
                    ) : users.length === 0 ? (
                      <div className="px-4 py-3 text-gray-500 text-center">No employees found</div>
                    ) : (
                      <>
                        {users.map(user => (
                          <button
                            key={user.userID}
                            onClick={() => {
                              handleUserSelect(user.userID);
                              setIsDropdownOpen(false);
                            }}
                            className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0"
                          >
                            <div>
                              <div className="font-medium text-gray-900">
                                {user.user || `${user.firstName || ''} ${user.lastName || ''}`}
                              </div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                              {user.jobTitle && (
                                <div className="text-xs text-gray-400">{user.jobTitle}</div>
                              )}
                            </div>
                            {selectedEmployee && selectedEmployee.userId === user.userID && (
                              <FiCheck className="text-teal-600" />
                            )}
                          </button>
                        ))}
                      </>
                    )}
                  </div>
                )}
              </div>

              {selectedEmployee && (
                <button
                  onClick={clearSelectedEmployee}
                  className="flex items-center px-4 py-2 bg-gray-100 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-200"
                >
                  Clear Selection
                </button>
              )}
            </div>
          )}



          <button
            onClick={handleSwitchToAdmin}
            disabled={!canSwitchToAdmin}
            className={`
              flex items-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md
              ${canSwitchToAdmin ? 'hover:bg-gray-50' : 'opacity-50 cursor-not-allowed'}
            `}
          >
            <FiRefreshCcw className="mr-2" />
            Switch to Admin
          </button>
        </div>
      </div>
      

      
      {/* Employee Tasks Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">
          {isBusinessUser && selectedEmployee ?
            `${selectedEmployee.firstName}'s Tasks` :
            'Your Tasks'
          }
        </h2>

        {/* Loading states */}
        {(tasksLoading || loadingEmployeeDetails) && (
          <div className="bg-white p-6 rounded-lg shadow text-gray-500">
            Loading {isBusinessUser && selectedEmployee ? 'employee' : ''} tasks...
          </div>
        )}

        {/* Error states */}
        {(tasksError || employeeDetailsError) && (
          <div className="bg-white p-6 rounded-lg shadow text-red-500">
            {tasksError || employeeDetailsError}
          </div>
        )}

        {/* Show message when business user hasn't selected an employee */}
        {isBusinessUser && !selectedEmployee && !loadingEmployeeDetails && (
          <div className="bg-gradient-to-br from-teal-50 to-blue-50 p-12 rounded-lg shadow-sm border border-teal-100 text-center">
            <div className="text-teal-400 mb-6">
              <FiUsers size={64} className="mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-700 mb-3">Welcome to Employee Dashboard</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              As a business user, you can view any employee's dashboard, tasks, and details.
              Please select an employee from the dropdown above to get started.
            </p>
            <div className="text-sm text-gray-500">
              <p>💡 Use the "Select Employee" dropdown in the top-right corner to choose an employee</p>
            </div>
          </div>
        )}

        {/* Tasks display */}
        {(isBusinessUser && selectedEmployee ? selectedEmployeeTasks : tasks).map((task, idx) => {
          const status = task.taskStatus || task.status;
          const name = task.taskName || task.type || '';
          const desc = task.taskDescription || task.description || '';
          const lowerName = name.toLowerCase();
          const lowerDesc = desc.toLowerCase();
          const isBank =
            lowerName.includes('bank account') ||
            lowerDesc.includes('bank account') ||
            lowerName.includes('direct deposit') ||
            lowerDesc.includes('direct deposit') ||
            lowerName.includes('deposit account') ||
            lowerDesc.includes('deposit account') ||
            lowerName.includes('funding account') ||
            lowerDesc.includes('funding account') ||
            lowerName.includes('add account') ||
            lowerDesc.includes('add account') ||
            lowerName.includes('link account') ||
            lowerDesc.includes('link account');
          const isW4 =
            (/w-?4/.test(lowerName) && !lowerName.includes('state')) ||
            (/w-?4/.test(lowerDesc) && !lowerDesc.includes('state')) ||
            lowerName.includes('federal tax') ||
            lowerDesc.includes('federal tax');
          const isStateW4 =
            lowerName.includes('state w4') ||
            lowerDesc.includes('state w4') ||
            lowerName.includes('state tax') ||
            lowerDesc.includes('state tax');
          const hasAction = isBank || isW4 || isStateW4;
          return (
            <div key={idx} className="bg-white p-6 rounded-lg shadow mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="bg-gray-100 p-2 rounded-md mr-4">
                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium">{name}</h3>
                    <p className="text-sm text-gray-500">{desc}</p>
                    {status && <span className={`text-xs font-semibold ml-1 ${status === 'Completed' ? 'text-green-600' : 'text-yellow-600'}`}>{status}</span>}
                  </div>
                </div>
                <button
                  className={`ml-4 px-4 py-2 rounded text-white font-semibold ${hasAction ? 'bg-teal-600 hover:bg-teal-700' : 'bg-gray-300 cursor-not-allowed'}`}
                  onClick={() => {
                    console.log('[Complete Button] Clicked for task:', task);
                    hasAction && handleTaskComplete(task);
                  }}
                  disabled={!hasAction}
                >
                  Complete
                </button>
              </div>
            </div>
          );
        })}
      </div>
      {/* Render sub-route forms here */}
      <Outlet />
    </div>
  );
};

export { BankAccountForm, FederalW4Form, StateW4Form };
export default EmployeeDashboard;

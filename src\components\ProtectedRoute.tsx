import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const publicRoutes = ['/login', '/signup', '/forgot-password'];

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { userData, isAdmin, isEmployee } = useAuth();

  console.log('[ProtectedRoute] location:', location.pathname);
  console.log('[ProtectedRoute] userData:', userData);
  console.log('[ProtectedRoute] isAdmin:', isAdmin, 'isEmployee:', isEmployee);

  const userRolesString = localStorage.getItem('userRoles');
  const userRoles = userRolesString ? JSON.parse(userRolesString) : {};
  const isBusinessUser = userRoles?.isBusinessUser || false;

  const getRedirectPathForUser = () => {
    if (isAdmin) return '/admin';
    if (isBusinessUser) return '/employer/dashboard';
    if (isEmployee) return '/employee/dashboard';
    return '/login';
  };

  // Check if employee is trying to access forbidden routes
  const isRestrictedRouteForEmployee = () => {
    const restrictedPrefixes = ['/admin', '/employer'];
    return restrictedPrefixes.some(prefix => location.pathname.startsWith(prefix));
  };

  // Show loading spinner while userData is being determined
  if (userData === undefined || userData === null) {
    console.log('[ProtectedRoute] userData is undefined or null, showing loading spinner');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-lg">Loading...</p>
      </div>
    );
  }

  // Only redirect if userData is definitely not present after loading
  if (!userData) {
    console.log('[ProtectedRoute] userData is falsy after loading, redirecting to /login');
    navigate('/login', { replace: true });
    return null;
  }

  if (isEmployee && isRestrictedRouteForEmployee()) {
    console.log('[ProtectedRoute] Employee trying to access restricted route, redirecting to /employee/dashboard');
    navigate('/employee/dashboard', { replace: true });
    return null;
  }

  console.log('[ProtectedRoute] Access granted, rendering children');
  return <>{children}</>;
};

export default ProtectedRoute;

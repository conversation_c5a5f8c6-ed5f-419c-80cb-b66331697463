import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "../lib/supabaseClient";

const SignupPage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Initialize form data, checking if we have pending data from login
  const [formData, setFormData] = useState(() => {
    const pendingUserDataStr = localStorage.getItem('pendingUserData');
    if (pendingUserDataStr) {
      try {
        const pendingUserData = JSON.parse(pendingUserDataStr);
        if (pendingUserData.bussiness_email) {
          return {
            first_name: '',
            middle_name: '',
            last_name: '',
            email: pendingUserData.bussiness_email,
            confirm_email: pendingUserData.bussiness_email,
            phone: '',
          };
        }
      } catch (e) {
        console.error('Error parsing pending user data:', e);
      }
    }
    return {
      first_name: '',
      middle_name: '',
      last_name: '',
      email: '',
      confirm_email: '',
      phone: '',
    };
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.first_name.trim()) {
      setError('First name is required');
      return false;
    }
    if (!formData.last_name.trim()) {
      setError('Last name is required');
      return false;
    }
    if (!formData.email.trim()) {
      setError('Email is required');
      return false;
    }
    if (formData.email !== formData.confirm_email) {
      setError('Emails do not match');
      return false;
    }
    if (!formData.phone.trim()) {
      setError('Phone number is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      // Get existing pending data if available
      let pendingUserData = {
        first_name: formData.first_name,
        middle_name: formData.middle_name || null,
        last_name: formData.last_name,
        phone: formData.phone,
        bussiness_email: formData.email,
      };
      
      const pendingUserDataStr = localStorage.getItem('pendingUserData');
      if (pendingUserDataStr) {
        try {
          const existingData = JSON.parse(pendingUserDataStr);
          // Merge existing data with new form data
          pendingUserData = {
            ...pendingUserData,
            bussiness_email: existingData.bussiness_email || formData.email,
          };
        } catch (e) {
          console.error('Error parsing pending user data:', e);
        }
      }
      
      // Store updated user data in localStorage
      localStorage.setItem('pendingUserData', JSON.stringify(pendingUserData));
      
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session) {
        // User is already authenticated, create profile directly
        const { error: insertError } = await supabase
          .from('website2_users')
          .insert([
            {
              user_id: session.user.id,
              first_name: pendingUserData.first_name,
              middle_name: pendingUserData.middle_name,
              last_name: pendingUserData.last_name,
              phone: pendingUserData.phone,
              bussiness_email: pendingUserData.bussiness_email
            }
          ]);
        
        if (insertError) throw insertError;
        
        // Clear the pending user data
        localStorage.removeItem('pendingUserData');
        
        setSuccess('Profile created successfully! Redirecting to dashboard...');
        setTimeout(() => navigate('/admin'), 2000);
      } else {
        // User is not authenticated, send OTP
        const { error: signInError } = await supabase.auth.signInWithOtp({
          email: pendingUserData.bussiness_email,
          options: {
            shouldCreateUser: true,
          }
        });
        
        if (signInError) throw signInError;
        
        setSuccess('Please check your email for the 6-digit code to complete registration.');
        
        // Redirect to login page after a delay
        setTimeout(() => {
          navigate('/login', { state: { email: pendingUserData.bussiness_email } });
        }, 3000);
      }
    } catch (err: any) {
      console.error('Signup error:', err);
      setError(err.message || 'An error occurred during signup');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4 py-8">
      <div className="w-full max-w-md">
        <h1 className="text-[32px] font-bold text-center mb-2">Tell us about yourself</h1>
        <p className="text-gray-500 text-center mb-8">
          We'll create your account and add you as an admin to your company.
        </p>

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-lg">
            {success}
          </div>
        )}

        <form className="space-y-6" onSubmit={handleSubmit}>
          <div>
            <label className="block text-gray-700 mb-2">First name <span className="text-red-500">*</span></label>
            <input
              type="text"
              name="first_name"
              value={formData.first_name}
              onChange={handleChange}
              placeholder="Enter first name"
              className="w-full px-4 py-3 text-gray-700 border border-gray-200 rounded-lg"
              required
            />
          </div>

          <div>
            <label className="block text-gray-700 mb-2">Middle name</label>
            <input
              type="text"
              name="middle_name"
              value={formData.middle_name}
              onChange={handleChange}
              placeholder="Enter middle name"
              className="w-full px-4 py-3 text-gray-700 border border-gray-200 rounded-lg"
            />
          </div>

          <div>
            <label className="block text-gray-700 mb-2">Last name <span className="text-red-500">*</span></label>
            <input
              type="text"
              name="last_name"
              value={formData.last_name}
              onChange={handleChange}
              placeholder="Enter last name"
              className="w-full px-4 py-3 text-gray-700 border border-gray-200 rounded-lg"
              required
            />
          </div>

          <div>
            <label className="block text-gray-700 mb-2">Business email <span className="text-red-500">*</span></label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter email"
              className="w-full px-4 py-3 text-gray-700 border border-gray-200 rounded-lg"
              disabled={!!localStorage.getItem('pendingUserData')}
              required
            />
            {localStorage.getItem('pendingUserData') && (
              <p className="text-sm text-gray-500 mt-1">Email already saved from login page</p>
            )}
          </div>

          <div>
            <label className="block text-gray-700 mb-2">Confirm email <span className="text-red-500">*</span></label>
            <input
              type="email"
              name="confirm_email"
              value={formData.confirm_email}
              onChange={handleChange}
              placeholder="Enter email"
              className="w-full px-4 py-3 text-gray-700 border border-gray-200 rounded-lg"
              disabled={!!localStorage.getItem('pendingUserData')}
              required
            />
          </div>

          <div>
            <label className="block text-gray-700 mb-2">Phone number <span className="text-red-500">*</span></label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Enter phone number"
              className="w-full px-4 py-3 text-gray-700 border border-gray-200 rounded-lg"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`w-full py-3 px-4 bg-[#008080] text-white font-medium rounded-lg mt-4 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {loading ? 'Processing...' : 'Continue'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default SignupPage;
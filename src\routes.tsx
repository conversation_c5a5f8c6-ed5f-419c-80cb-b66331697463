import { createBrowserRouter, Navigate } from 'react-router-dom';
import CopilotProvider from './components/CopilotProvider';
import { CopilotNavigation } from './components/CopilotNavigation';
import CopilotActionsProvider from './components/CopilotActionsProvider';
import AdminDashboard from './pages/AdminDashboard';
import AddCompanyPage from './pages/Admin/AddCompanyPage/AddCompanyPage';
import EmployeeDashboard from './pages/EmployeeDashboard';
import EmployeeDetailsPage from './pages/EmployeeDetailsPage';
import DocumentsPage from './pages/DocumentsPage';
import TeamDocuments from './pages/TeamDocuments';
import PersonalDocuments from './pages/PersonalDocuments';
import PaystubsPage from './pages/PaystubsPage';
import UsersPage from './pages/UsersPage';
import EmployerLayout from './layouts/EmployerLayout';
import EmployeeLayout from './layouts/EmployeeLayout';
import EmployerDashboard from './pages/EmployerDashboard';
import PayrollPage from './pages/PayrollPage';
import HiringPage from './pages/HiringPage';
import TeamPage from './pages/TeamPage';
import CompanyPage from './pages/CompanyPage';
import EmployerDocumentsPage from './pages/EmployerDocumentsPage';
import BenefitsPage from './pages/BenefitsPage';
import TaxesPage from './pages/TaxesPage';
import AddEmployeePage from './pages/AddEmployeePage';
import AddIndependentContractorPage from './pages/AddIndependentContractorPage';
import AddBusinessContractorPage from './pages/AddBusinessContractorPage';
import BusinessDocuments from './pages/BusinessDocuments';
import EmployerSettingsPage from './pages/EmployerSettingsPage';
import EmployerHelpPage from './pages/EmployerHelpPage';
import LoginPage from './pages/LoginPage';
import SignupPage from './pages/SignupPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import AuthCallback from './pages/AuthCallback';
import ReportsPage from './pages/ReportsPage';
import React from 'react';
import AddBankAccountPage from './pages/AddBankAccountPage';

export const router = createBrowserRouter([
  {
    path: "/",
    element: <Navigate to="/login" replace />,
  },
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/forgot-password",
    element: <ForgotPasswordPage />,
  },
  {
    path: "/signup",
    element: <SignupPage />,
  },
  {
    path: "/auth/callback",
    element: <AuthCallback />,
  },
  // Admin routes
  {
    path: "/admin",
    element: <AdminDashboard />,
  },
  {
    path: "/admin/add-company",
    element: <AddCompanyPage />,
  },
  {
    path: "/admin/users",
    element: <UsersPage />,
  },
  // Employee routes with shared layout
  {
    path: "/employee",
    element: <EmployeeLayout />,
    children: [
      {
        path: "dashboard",
        element: <EmployeeDashboard />, // This will render <Outlet /> for sub-routes
        children: [
          { path: "w4info", element: <EmployeeDashboard formRoute="w4info" /> },
          { path: "bankaccount", element: <EmployeeDashboard formRoute="bankaccount" /> },
          { path: "statew4", element: <EmployeeDashboard formRoute="statew4" /> },
        ]
      },
      {
        path: "details",
        element: <EmployeeDetailsPage />,
      },
      {
        path: "documents",
        element: <DocumentsPage />,
        children: [
          {
            path: "business",
            element: <BusinessDocuments />,
          },
          {
            path: "team",
            element: <TeamDocuments />,
          },
          {
            path: "personal",
            element: <PersonalDocuments />,
          }
        ]
      },
      {
        path: "paystubs",
        element: <PaystubsPage />,
      },
    ]
  },
  // Employer routes with shared layout
  {
    path: "/employer",
    element: <EmployerLayout />,
    children: [
      {
        path: "dashboard",
        element: <EmployerDashboard />,
      },
      {
        path: "payroll",
        element: <PayrollPage />,
      },
      {
        path: "hiring",
        element: <HiringPage />,
      },
      {
        path: "hiring/add-employee",
        element: <AddEmployeePage />,
      },
      {
        path: "hiring/add-independent-contractor",
        element: <AddIndependentContractorPage />,
      },
      {
        path: "hiring/add-business-contractor",
        element: <AddBusinessContractorPage />,
      },
      {
        path: "team",
        element: <TeamPage />,
      },
      {
        path: "company",
        element: <CompanyPage />,
      },
      {
        path: "documents",
        element: <EmployerDocumentsPage />,
      },
      {
        path: "benefits",
        element: <BenefitsPage />,
      },
      {
        path: "taxes",
        element: <TaxesPage />,
      },
      {
        path: "settings",
        element: <EmployerSettingsPage />,
        children: [
          {
            path: "bank-account",
            element: <AddBankAccountPage />,
          },
        ],
      },
      {
        path: "help",
        element: <EmployerHelpPage />,
      },
      {
        path: "reports",
        element: <ReportsPage />,
      }
    ]
  }
]);

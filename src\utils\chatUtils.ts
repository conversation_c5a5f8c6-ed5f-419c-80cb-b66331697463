/**
 * Utility functions for working with chat messages
 */

/**
 * Extract the last message from a chat conversation
 * @param messages - Array of chat messages
 * @returns The content of the last message or empty string if no messages
 */
export function getLastMessage(messages: any[]): string {
  if (!messages || messages.length === 0) {
    return '';
  }
  
  return messages[messages.length - 1].content;
}

/**
 * Extract the last user message from a chat conversation
 * @param messages - Array of chat messages
 * @returns The content of the last user message or empty string if no user messages
 */
export function getLastUserMessage(messages: any[]): string {
  if (!messages || messages.length === 0) {
    return '';
  }
  
  // Find the last message with role 'user'
  for (let i = messages.length - 1; i >= 0; i--) {
    if (messages[i].role === 'user') {
      return messages[i].content;
    }
  }
  
  return '';
}

/**
 * Extract the last assistant message from a chat conversation
 * @param messages - Array of chat messages
 * @returns The content of the last assistant message or empty string if no assistant messages
 */
export function getLastAssistantMessage(messages: any[]): string {
  if (!messages || messages.length === 0) {
    return '';
  }
  
  // Find the last message with role 'assistant'
  for (let i = messages.length - 1; i >= 0; i--) {
    if (messages[i].role === 'assistant') {
      return messages[i].content;
    }
  }
  
  return '';
}

/**
 * Get all messages from a specific role
 * @param messages - Array of chat messages
 * @param role - The role to filter by ('user', 'assistant', or 'system')
 * @returns Array of messages from the specified role
 */
export function getMessagesByRole(messages: any[], role: 'user' | 'assistant' | 'system'): any[] {
  if (!messages || messages.length === 0) {
    return [];
  }
  
  return messages.filter(message => message.role === role);
}

import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate,Link } from 'react-router-dom';
import {
  FiGrid, FiUser, FiFileText, FiDollarSign, 
  FiRefreshCcw, FiLogOut
} from 'react-icons/fi';
import { useAppContext } from '../context/AppContext';
import { supabase } from '../lib/supabaseClient';
import { authService } from '../services/authService';

const EmployeeSidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);
  const [canSwitchToAdmin, setCanSwitchToAdmin] = useState(false);
  const { switchToAdminRole, userRoles, resetAppContext } = useAppContext();

  useEffect(() => {
    // Check if user has admin role to determine if they can switch roles
    const checkUserRoles = async () => {
      try {
        // Check if user can switch based on userRoles from context
        if (userRoles) {
          console.log("User roles from context:", userRoles);
          setCanSwitchToAdmin(userRoles.isPayrollAdmin || userRoles.isBusinessUser);
          return;
        }
        
        // Fallback to checking from database
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.user) {
          // Fetch user profile from database
          const { data: userProfile, error } = await supabase
            .from('temp_website2_users')
            .select('is_business_user, user_id, company_id')
            .eq('user_id', session.user.id)
            .single();
          
          if (!error && userProfile) {
            // Determine user roles based on the 'is_business_user' field
            const isBusinessUser = userProfile.is_business_user === true;
            // For now, we'll consider business users as admins too
            const isPayrollAdmin = isBusinessUser;
            console.log("User profile from DB:", { isBusinessUser, isPayrollAdmin });
            setCanSwitchToAdmin(isPayrollAdmin || isBusinessUser);
          } else {
            // Try to get roles from localStorage as fallback
            const storedRoles = localStorage.getItem('userRoles');
            if (storedRoles) {
              const roles = JSON.parse(storedRoles);
              console.log("User roles from localStorage:", roles);
              setCanSwitchToAdmin(roles.isPayrollAdmin || roles.isBusinessUser);
            }
          }
        }
      } catch (err) {
        console.error('Error checking user roles:', err);
      }
    };
    
    checkUserRoles();
  }, [userRoles, location.pathname]); // Re-check when userRoles or location changes

  const menuItems = [
    { icon: <FiGrid size={20} />, label: 'Dashboard', path: '/employee/dashboard' },
    { icon: <FiUser size={20} />, label: 'Your Details', path: '/employee/details' },
    { icon: <FiFileText size={20} />, label: 'Documents', path: '/employee/documents' },
    { icon: <FiDollarSign size={20} />, label: 'Paystubs', path: '/employee/paystubs' },
  ];

  const handleSwitchToAdmin = () => {
    console.log("Switch to Admin clicked, canSwitchToAdmin:", canSwitchToAdmin);
    console.log("Current userRoles:", userRoles);
    
    if (canSwitchToAdmin) {
      console.log("Calling switchToAdminRole function");
      switchToAdminRole();
      
      // As a fallback, directly navigate to admin route
      setTimeout(() => {
        console.log("Fallback navigation to /admin");
        navigate('/admin');
      }, 300);
    }
  };

  const handleSignOut = async () => {
    try {
      console.log('EmployeeSidebar: calling authService.logout()');
      await authService.logout();
      console.log('EmployeeSidebar: calling resetAppContext()');
      resetAppContext();
      console.log('EmployeeSidebar: navigating to /login');
      navigate('/login');
    } catch (error) {
      console.error('EmployeeSidebar: Sign out failed:', error);
    }
  };

  return (
    <>
      <div 
        className={`fixed left-0 top-0 h-screen bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out z-50 ${
          isHovered ? 'w-64' : 'w-16'
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Company Logo */}
        <div className={`px-4 py-6 ${isHovered ? 'px-6' : 'px-2'}`}>
          {isHovered ? (
            <div>
              <h2 className="text-lg font-semibold text-gray-900">GrowthPods</h2>
              <div className="text-sm text-gray-500">Hire. Pay. Manage.</div>
            </div>
          ) : (
            <div className="w-8 h-8">
              <img src="/growth-pods.png" alt="GP" className="w-full h-full" />
            </div>
          )}
        </div>

        {/* Divider */}
        <div className="h-px bg-gray-200" />

        {/* Navigation Menu */}
        <nav className={`flex-1 ${isHovered ? 'px-3' : 'px-2'} py-6 overflow-y-auto`}>
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`
                flex items-center px-3 py-2 mb-1 rounded-lg text-sm font-medium
                ${location.pathname === item.path
                  ? 'bg-teal-600 text-white' 
                  : 'text-gray-900 hover:bg-gray-100'}
                ${!isHovered ? 'justify-center' : ''}
              `}
            >
              <span className={isHovered ? 'mr-3' : ''}>{item.icon}</span>
              {isHovered && <span className="truncate">{item.label}</span>}
            </Link>
          ))}
        </nav>

        {/* Bottom section */}
        <div className={`${isHovered ? 'px-3' : 'px-2'} py-4 border-t border-gray-200`}>
          <button
            onClick={handleSwitchToAdmin}
            disabled={!canSwitchToAdmin}
            className={`
              flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 
              ${canSwitchToAdmin ? 'hover:bg-gray-100' : 'opacity-50 cursor-not-allowed'}
              rounded-lg mb-2
              ${!isHovered ? 'justify-center' : ''}
            `}
          >
            <FiRefreshCcw className={isHovered ? 'mr-3' : ''} size={20} />
            {isHovered && 'Switch to Admin'}
          </button>
          <button
            onClick={handleSignOut}
            className={`
              flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 
              hover:bg-gray-100 rounded-lg
              ${!isHovered ? 'justify-center' : ''}
            `}
          >
            <FiLogOut className={isHovered ? 'mr-3' : ''} size={20} />
            {isHovered && 'Sign out'}
          </button>
        </div>
      </div>

      {/* Main content margin */}
      <div className={`${isHovered ? 'ml-64' : 'ml-16'} transition-all duration-300 ease-in-out`}>
        {/* Your main content goes here */}
      </div>
    </>
  );
};

export default EmployeeSidebar;

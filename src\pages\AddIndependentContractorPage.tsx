import React, { useState, useRef, useEffect } from 'react';
import { utf8ToBase64 } from '../utils/base64';

const AddIndependentContractorPage: React.FC = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    dateOfJoin: '',
    workerType: '',
    jobTitle: '',
    companyLocationCategory: '',
    stateCode: '',
    // wage step fields
    wageBasis: '',
    wageAmount: '',
    differentialPay: false,
    contractorType: '',
    contractorStatus: '',
    userType: '',
    employmentStatus: '',
    userRefTaxExempt: '',
    paymentMethod: '',
  });
  const [step, setStep] = useState(1);
  const [userId, setUserId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const errorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (error) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [error]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Step 1: User Details
  const handleUserSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.phoneNumber || !formData.dateOfJoin || !formData.workerType || !formData.jobTitle || !formData.companyLocationCategory || !formData.stateCode) {
      setError('Please fill in all required fields.');
      return;
    }
    let formattedDate = formData.dateOfJoin;
    if (formattedDate && formattedDate.includes('/')) {
      const [month, day, year] = formattedDate.split('/');
      formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }
    const credentials = `${import.meta.env.VITE_CLIENT_ID}:${import.meta.env.VITE_CLIENT_SECRET}`;
    const basicAuth = utf8ToBase64(credentials);
    const selectedCompanyStr = localStorage.getItem('selectedCompany');
    if (!selectedCompanyStr) {
      setError('No company selected.');
      return;
    }
    const selectedCompany = JSON.parse(selectedCompanyStr);
    const companyId = selectedCompany.companyID;
    if (!companyId) {
      setError('Invalid company ID.');
      return;
    }
    const userPayload = {
      method: 'addUser',
      user: {
        companyId,
        firstName: formData.firstName,
        middleName: formData.middleName,
        lastName: formData.lastName,
        email: formData.email,
        phoneNumber: formData.phoneNumber,
        dateOfJoin: formattedDate,
        workerType: formData.workerType,
        jobTitle: formData.jobTitle,
        companyLocationCategory: formData.companyLocationCategory,
        stateCode: formData.stateCode,
        companyLocationId: ''
      }
    };
    try {
      const userResponse = await fetch('https://sandbox.rollfi.xyz/adminPortal#addUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${basicAuth}`
        },
        body: JSON.stringify(userPayload)
      });
      const contentType = userResponse.headers.get('content-type');
      let userResult;
      if (contentType && contentType.includes('application/json')) {
        userResult = await userResponse.json();
      } else {
        const text = await userResponse.text();
        setError(text || 'Unknown server error');
        return;
      }
      if (userResult && userResult.error) {
        setError(userResult.error.message || 'Failed to add contractor.');
        return;
      }
      const newUserId = userResult.user?.userId;
      if (!newUserId) {
        setError('User ID not returned from addUser API.');
        return;
      }
      setUserId(newUserId);
      setStep(2);
    } catch (err: any) {
      setError(err?.message || 'Failed to add contractor.');
    }
  };

  // Step 2: Wage Details (same as before, but only show if userId is set)
  const handleWageSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    if (!formData.wageBasis || (!formData.differentialPay && !formData.wageAmount)) {
      setError('Please fill in all required fields. Wage amount is required unless differential pay is selected.');
      return;
    }
    const credentials = `${import.meta.env.VITE_CLIENT_ID}:${import.meta.env.VITE_CLIENT_SECRET}`;
    const basicAuth = utf8ToBase64(credentials);
    const selectedCompanyStr = localStorage.getItem('selectedCompany');
    if (!selectedCompanyStr) {
      setError('No company selected.');
      return;
    }
    const selectedCompany = JSON.parse(selectedCompanyStr);
    const companyId = selectedCompany.companyID;
    if (!companyId) {
      setError('Invalid company ID.');
      return;
    }
    const wagePayload = {
      method: 'addUserWage',
      userWage: {
        companyId,
        userId,
        differentialPay: formData.differentialPay ? 'Yes' : 'No',
        wageRate: formData.wageAmount,
        workerType: formData.workerType,
        wageBasis: formData.wageBasis,
        userType: formData.userType,
        employmentStatus: formData.employmentStatus,
        userRefTaxExempt: formData.userRefTaxExempt,
        startDate: formData.dateOfJoin,
        paymentMethod: formData.paymentMethod
      }
    };
    try {
      const wageResponse = await fetch('https://sandbox.rollfi.xyz/adminPortal#addUserWage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${basicAuth}`
        },
        body: JSON.stringify(wagePayload)
      });
      const contentType = wageResponse.headers.get('content-type');
      let wageResult;
      if (contentType && contentType.includes('application/json')) {
        wageResult = await wageResponse.json();
      } else {
        const text = await wageResponse.text();
        setError(text || 'Unknown server error');
        return;
      }
      if (wageResult && wageResult.error) {
        setError(wageResult.error.message || 'Failed to add contractor wage.');
        return;
      }
      setSuccess('Contractor and compensation added successfully!');
      setFormData({
        firstName: '',
        middleName: '',
        lastName: '',
        email: '',
        phoneNumber: '',
        dateOfJoin: '',
        workerType: '',
        jobTitle: '',
        companyLocationCategory: '',
        stateCode: '',
        wageBasis: '',
        wageAmount: '',
        differentialPay: false,
        contractorType: '',
        contractorStatus: '',
      });
      setStep(1);
      setUserId(null);
    } catch (err: any) {
      setError(err?.message || 'Failed to add contractor wage.');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-6">
      <div className="w-full max-w-2xl bg-white rounded-lg shadow p-8">
      <h1 className="text-2xl font-semibold mb-6">Add Independent Contractor</h1>
        {error && <div ref={errorRef} className="mb-4 p-3 bg-red-100 text-red-700 rounded">{error}</div>}
        {success && <div className="mb-4 p-3 bg-green-100 text-green-700 rounded">{success}</div>}
        {step === 1 && (
          <form className="space-y-8" onSubmit={handleUserSubmit}>
            <div>
              <h2 className="text-xl font-medium mb-4">Contractor Information</h2>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">First Name <span className="text-red-500">*</span></label>
                  <input type="text" name="firstName" value={formData.firstName} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Middle Name</label>
                  <input type="text" name="middleName" value={formData.middleName} onChange={handleChange} className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Last Name <span className="text-red-500">*</span></label>
                  <input type="text" name="lastName" value={formData.lastName} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" />
            </div>
            <div>
                  <label className="block text-sm font-medium text-gray-700">Email <span className="text-red-500">*</span></label>
                  <input type="email" name="email" value={formData.email} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" />
            </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Phone Number <span className="text-red-500">*</span></label>
                  <input type="tel" name="phoneNumber" value={formData.phoneNumber} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" />
          </div>
          <div>
                  <label className="block text-sm font-medium text-gray-700">Date of Join <span className="text-red-500">*</span></label>
                  <input type="text" name="dateOfJoin" value={formData.dateOfJoin} onChange={handleChange} placeholder="MM/DD/YYYY" required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" maxLength={10} />
          </div>
          <div>
                  <label className="block text-sm font-medium text-gray-700">Worker Type <span className="text-red-500">*</span></label>
                  <select name="workerType" value={formData.workerType} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2">
                    <option value="">Select</option>
                    <option value="W2">W2</option>
                    <option value="1099">1099</option>
                  </select>
          </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Job Title <span className="text-red-500">*</span></label>
                  <input type="text" name="jobTitle" value={formData.jobTitle} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" />
        </div>
          <div>
                  <label className="block text-sm font-medium text-gray-700">Company Location Category <span className="text-red-500">*</span></label>
                  <select name="companyLocationCategory" value={formData.companyLocationCategory} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2">
                    <option value="">Select</option>
                    <option value="Remote">Remote</option>
                    <option value="On-site">On-site</option>
                  </select>
          </div>
            <div>
                  <label className="block text-sm font-medium text-gray-700">State Code <span className="text-red-500">*</span></label>
                  <input type="text" name="stateCode" value={formData.stateCode} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" maxLength={2} />
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-4">
              <button type="button" className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" onClick={() => setFormData({
                firstName: '', middleName: '', lastName: '', email: '', phoneNumber: '', dateOfJoin: '', workerType: '', jobTitle: '', companyLocationCategory: '', stateCode: '', wageBasis: '', wageAmount: '', differentialPay: false, contractorType: '', contractorStatus: '', userType: '', employmentStatus: '', userRefTaxExempt: '', paymentMethod: ''
              })}>Cancel</button>
              <button type="submit" className="px-4 py-2 text-white bg-teal-600 rounded-md hover:bg-teal-700">Continue</button>
            </div>
          </form>
        )}
        {step === 2 && (
          <form className="space-y-8" onSubmit={handleWageSubmit}>
            <div>
              <h2 className="text-xl font-medium mb-4">Compensation Details</h2>
              <div className="grid grid-cols-2 gap-4">
                <div className={formData.differentialPay ? 'col-span-2' : ''}>
                  <label className="block text-sm font-medium text-gray-700">Payment Basis <span className="text-red-500">*</span></label>
                  <select name="wageBasis" value={formData.wageBasis} onChange={handleChange} required className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2">
                    <option value="">Select Payment basis</option>
                    <option value="Per Hour">Per Hour</option>
                    <option value="Per Day">Per Day</option>
                    <option value="Per Week">Per Week</option>
                    <option value="Per Month">Per Month</option>
              </select>
            </div>
                {!formData.differentialPay && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Wage Amount</label>
                    <input type="number" name="wageAmount" value={formData.wageAmount} onChange={handleChange} className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2" />
                  </div>
                )}
              </div>
              <div className="flex items-center mt-4">
                <input type="checkbox" name="differentialPay" checked={formData.differentialPay} onChange={handleChange} className="mr-2" />
                <span>This person has <span className="text-blue-600 cursor-pointer">differential pay</span></span>
          </div>
        </div>
        <div className="flex justify-end space-x-4">
              <button type="button" className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" onClick={() => setStep(1)}>Cancel</button>
              <button type="submit" className="px-4 py-2 text-white bg-teal-600 rounded-md hover:bg-teal-700">Add Contractor</button>
        </div>
      </form>
        )}
      </div>
    </div>
  );
};

export default AddIndependentContractorPage;
export interface EmployeeDetails {
    // General details
    fullName: string;
    dateOfBirth: string;
    ssn: string | null;
    
    // Contact information
    address: string;
    businessPhone: string;
    workEmail: string;
    
    // Security
    username: string;
    password: string;
    
    // Two-Factor authentication
    businessEmailMasked: string;
    businessPhoneMasked: string;
    
    // Job Details
    employmentType: string;
    jobTitle: string;
    compensation: string;
    startDate: string;
    
    // Work Location
    workLocation: string;
  }

// User profile interface based on temp_website2_users table schema
export interface UserProfile {
  id: string;
  email: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  phone: string;
  company_id: string;
  company_name: string;
  business_user_id: string;
  ownership_percentage: number;
  date_of_birth: string;
  ssn: string;
  created_at: string;
  job_title: string;
  date_of_join: string;
  kyc_status: string;
  user_status: string;
  worker_type: string;
  wage_rate: number;
  wage_basis: string;
  payment_method: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  user_id: string;
  is_business_user: boolean;
}
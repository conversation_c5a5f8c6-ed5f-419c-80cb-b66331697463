import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL!;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY!;

// Create Supabase client with proper headers
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Prefer': 'return=minimal'
  },
  auth: {
    persistSession: true,
    autoRefreshToken: true
  }
});
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      colors: {
        teal: {
          50: '#e6f7f7',
          100: '#b3e6e6',
          200: '#80d6d6',
          300: '#4dc6c6',
          400: '#26b6b6',
          500: '#00a0a0',
          600: '#008080',
          700: '#006060',
          800: '#004040',
          900: '#002020',
        },
      },
      animation: {
        'fade-in-right': 'fadeInRight 0.3s ease-out',
        'fade-out-left': 'fadeOutLeft 0.3s ease-in',
      },
      keyframes: {
        fadeInRight: {
          '0%': { opacity: '0', transform: 'translateX(20px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        fadeOutLeft: {
          '0%': { opacity: '1', transform: 'translateX(0)' },
          '100%': { opacity: '0', transform: 'translateX(-20px)' },
        },
      },
    },
  },
  plugins: [],
};

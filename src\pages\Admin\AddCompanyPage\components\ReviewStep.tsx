import React from 'react';
import { CreateBusinessPayload } from '../../../../services/adminService';
import styles from '../AddCompanyPage.module.css';

interface ReviewStepProps {
  data: CreateBusinessPayload;
  onSubmit: () => void;
  onBack: () => void;
  isLoading: boolean;
}

const ReviewStep: React.FC<ReviewStepProps> = ({ data, onSubmit, onBack, isLoading }) => {
  const { registration, kybInformation, companyLocation, businessUser } = data;

  return (
    <div>
      <h2 className={styles.stepTitle}>Step 5: Review and Submit</h2>

      <div className={styles.reviewSection}>
        <h3 className={styles.reviewSectionTitle}>Registration Information</h3>
        <p><strong>Company Name:</strong> {registration.company}</p>
        <p><strong>Business Website:</strong> {registration.businessWebsite}</p>
        <p><strong>NAICS Sub-Category Code:</strong> {registration.nacisSubCategoryCode}</p>
        <p><strong>E-Sign Enabled:</strong> {registration.isEsign ? 'Yes' : 'No'}</p>
        <p><strong>Terms Accepted:</strong> {registration.isTermsAccepted ? 'Yes' : 'No'}</p>
      </div>

      <div className={styles.reviewSection}>
        <h3 className={styles.reviewSectionTitle}>KYB Information</h3>
        <p><strong>EIN:</strong> {kybInformation.ein}</p>
        <p><strong>Entity Type:</strong> {kybInformation.entityType}</p>
        <p><strong>Date of Incorporation:</strong> {kybInformation.dateOfIncorporation}</p>
        <p><strong>IRS Assigned Federal Filing Form:</strong> {kybInformation.irsAssisgnedFederalFilingForm}</p>
      </div>

      <div className={styles.reviewSection}>
        <h3 className={styles.reviewSectionTitle}>Company Location</h3>
        <p><strong>Location Type:</strong> {companyLocation.companyLocation}</p>
        <p><strong>Address Line 1:</strong> {companyLocation.address1}</p>
        {companyLocation.address2 && <p><strong>Address Line 2:</strong> {companyLocation.address2}</p>}
        <p><strong>City:</strong> {companyLocation.city}</p>
        <p><strong>State:</strong> {companyLocation.state}</p>
        <p><strong>Zipcode:</strong> {companyLocation.zipcode}</p>
        <p><strong>Phone Number:</strong> {companyLocation.phoneNumber}</p>
        <p><strong>Work Location:</strong> {companyLocation.isWorkLocation ? 'Yes' : 'No'}</p>
        <p><strong>Mailing Address:</strong> {companyLocation.isMailingAddress ? 'Yes' : 'No'}</p>
        <p><strong>Filing Address:</strong> {companyLocation.isFilingAddress ? 'Yes' : 'No'}</p>
      </div>

      <div className={styles.reviewSection}>
        <h3 className={styles.reviewSectionTitle}>Business User Information</h3>
        <p><strong>First Name:</strong> {businessUser.firstName}</p>
        {businessUser.middleName && <p><strong>Middle Name:</strong> {businessUser.middleName}</p>}
        <p><strong>Last Name:</strong> {businessUser.lastName}</p>
        <p><strong>Phone Number:</strong> {businessUser.phoneNumber}</p>
        <p><strong>Email:</strong> {businessUser.email}</p>
        <p><strong>Address Line 1:</strong> {businessUser.address1}</p>
        {businessUser.address2 && <p><strong>Address Line 2:</strong> {businessUser.address2}</p>}
        <p><strong>City:</strong> {businessUser.city}</p>
        <p><strong>State:</strong> {businessUser.state}</p>
        <p><strong>Zipcode:</strong> {businessUser.zipcode}</p>
        <p><strong>SSN:</strong> {businessUser.ssn}</p>
        <p><strong>Date of Birth:</strong> {businessUser.dateOfBirth}</p>
        <p><strong>Ownership Percentage:</strong> {businessUser.ownershipPercentage}%</p>
        <p><strong>Payroll Admin:</strong> {businessUser.payrollAdmin ? 'Yes' : 'No'}</p>
        <p><strong>Book Keeper:</strong> {businessUser.bookKeeper ? 'Yes' : 'No'}</p>
        <p><strong>Beneficial Owner:</strong> {businessUser.beneficialOwner ? 'Yes' : 'No'}</p>
      </div>

      <div className={styles.buttonContainer}>
        <button type="button" onClick={onBack} className={styles.secondary} disabled={isLoading}>
          Back
        </button>
        <button type="button" onClick={onSubmit} className={styles.primary} disabled={isLoading}>
          {isLoading ? 'Submitting...' : 'Submit Company'}
        </button>
      </div>
    </div>
  );
};

export default ReviewStep;
import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { employerService } from '../services/employerService'; // Import the service
import DatePicker from 'react-datepicker';
import { format } from 'date-fns';
import 'react-datepicker/dist/react-datepicker.css';

interface EmployeeFormData {
  firstName: string;
  middleName: string;
  lastName: string;
  employeeId: string; // Maps to userReferenceId
  phoneNumber: string;
  businessEmail: string; // Maps to email
  startDate: string; // Maps to dateOfJoin (YYYY-MM-DD from type="date")
  jobTitle: string;
  workLocation: 'Remote' | 'On-site' | ''; // Allow empty for initial "Select"
  workerType: 'W2' | '1099' | ''; // Allow empty for initial "Select"
  stateCode: string;
  // salary is not in this form as per screenshot, but API might handle it if present
}

const AddEmployeePage: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<EmployeeFormData>({
    firstName: '',
    middleName: '',
    lastName: '',
    employeeId: '',
    phoneNumber: '',
    businessEmail: '',
    startDate: '',
    jobTitle: '',
    workLocation: '', // Default to empty for "Select Work Location"
    workerType: '', // Default to empty for "Select Worker Type"
    stateCode: '' // Default to an empty string for "Select State"
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [inputValue, setInputValue] = useState('');
  const errorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (error && errorRef.current) {
      errorRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [error]);

  useEffect(() => {
    if (error) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [error]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      const formatted = format(date, 'MM/dd/yyyy');
      setStartDate(date);
      setInputValue(formatted);
      setFormData({ ...formData, startDate: formatted });
    }
  };

  const handleInputChangeDate = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/[^0-9/]/g, '');
    if ((value.length === 2 || value.length === 5) && inputValue.length < value.length) {
      value += '/';
    }
    setInputValue(value);
    // Optionally, parse and set date if valid
    const parts = value.split('/');
    if (parts.length === 3 && parts[0].length === 2 && parts[1].length === 2 && parts[2].length === 4) {
      const parsed = new Date(`${parts[2]}-${parts[0]}-${parts[1]}`);
      if (!isNaN(parsed.getTime())) {
        setStartDate(parsed);
        setFormData({ ...formData, startDate: value });
      }
    } else {
      setFormData({ ...formData, startDate: value });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Convert MM/DD/YYYY or other to YYYY-MM-DD for API
    let formattedDate = formData.startDate;
    if (formattedDate && formattedDate.includes('/')) {
      const [month, day, year] = formattedDate.split('/');
      formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }
    try {
      console.log('Submitting employee data:', { ...formData, startDate: formattedDate });
      const response = await employerService.addEmployee({ ...formData, startDate: formattedDate });
      console.log('Add employee API response:', response);
      // Check for error in API response
      if (response && response.error) {
        setError(response.error.message || 'Failed to add employee. Please try again.');
        setSuccess(null);
        return;
      }
      setSuccess(`Successfully added ${formData.firstName} ${formData.lastName}`);
      setFormData({
        firstName: '',
        middleName: '',
        lastName: '',
        employeeId: '',
        phoneNumber: '',
        businessEmail: '',
        startDate: '',
        jobTitle: '',
        workLocation: '',
        workerType: '',
        stateCode: ''
      });
      setError(null);
      navigate('/employer/hiring');
    } catch (error: any) {
      // Show error message from API if available
      setError(error?.message || 'Failed to add employee. Please try again.');
      setSuccess(null);
    }
  };

  return (
    <div className="flex-1 p-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-semibold mb-6">Add New Employee</h1>
        
        {error && <div ref={errorRef} className="mb-4 p-3 bg-red-100 text-red-700 rounded">{error}</div>}
        {success && <div className="mb-4 p-3 bg-green-100 text-green-700 rounded">{success}</div>}
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Middle Name
              </label>
              <input
                type="text"
                name="middleName"
                value={formData.middleName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Employee ID
              </label>
              <input
                type="text"
                name="employeeId"
                value={formData.employeeId}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                // Not making it required as API userReferenceId can be ""
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number <span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Business Email <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              name="businessEmail"
              value={formData.businessEmail}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date <span className="text-red-500">*</span>
              </label>
              <DatePicker
                selected={startDate}
                onChange={handleDateChange}
                customInput={
              <input
                    type="text"
                    value={inputValue}
                    onChange={handleInputChangeDate}
                    placeholder="MM/DD/YYYY"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    maxLength={10}
                    pattern="\d{2}/\d{2}/\d{4}"
                required
              />
                }
                dateFormat="MM/dd/yyyy"
                showMonthDropdown
                showYearDropdown
                dropdownMode="select"
              />
              <small className="text-gray-500">Format: MM/DD/YYYY</small>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Job Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="jobTitle"
                value={formData.jobTitle}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Work Location <span className="text-red-500">*</span>
            </label>
            <select
              name="workLocation"
              value={formData.workLocation}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              required
            >
              <option value="">Select Work Location</option>
              <option value="Remote">Remote</option>
              <option value="On-site">On-site</option>
            </select>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Worker Type <span className="text-red-500">*</span>
              </label>
              <select
                name="workerType"
                value={formData.workerType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              >
                <option value="">Select Worker Type</option>
                <option value="W2">W2</option>
                <option value="1099">1099</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                State Code
              </label>
              <select
                name="stateCode"
                value={formData.stateCode}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                // Removed required attribute
              >
                <option value="">Select State</option>
                {['TX', 'AK', 'ND', 'IL', 'MA', 'KS', 'AS', 'AL', 'UT', 'SD', 'MI', 'DC', 'GA', 'SC', 'TN', 'OK', 'CO', 'CA', 'ME', 'MO', 'WY', 'MD', 'VT', 'NM', 'CT', 'AZ', 'WI', 'VA', 'WV', 'NJ', 'DE', 'NC', 'PR', 'IN', 'MT', 'NH', 'HI', 'IA', 'KY', 'WA', 'OH', 'FL', 'OR', 'MP', 'GU', 'MN', 'MS', 'PA', 'NE', 'RI', 'ID', 'LA', 'NY', 'NV', 'AR'].map(state => (
                  <option key={state} value={state}>{state}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate('/employer/hiring')}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              Add Employee
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddEmployeePage;

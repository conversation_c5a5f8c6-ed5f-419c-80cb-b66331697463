import React, { createContext, useContext, useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { employerService } from '../services/employerService';
import { adminService } from '../services/adminService';
import { supabase } from '../lib/supabaseClient';

// Define user roles interface
interface UserRoles {
  isBusinessUser: boolean;
  isPayrollAdmin: boolean;
  isEmployee: boolean;
}

interface AppContextType {
  currentRoute: string;
  selectedCompany: any;
  userData: any;
  userRoles: UserRoles | null;
  employeeData: any;
  dashboardStats: any;
  companyList: any[];
  updateSelectedCompany: (company: any) => void;
  refreshData: () => Promise<void>;
  switchToAdminRole: () => void;
  switchToBusinessRole: () => void;
  switchToEmployeeRole: () => void;
  switchToEmployeeViewAsAdmin: (employeeId: string) => void;
  currentRole: string | null;
  resetAppContext: () => void;
}

const AppContext = createContext<AppContextType>({
  currentRoute: '',
  selectedCompany: null,
  userData: null,
  userRoles: null,
  employeeData: null,
  dashboardStats: null,
  companyList: [],
  updateSelectedCompany: () => {},
  refreshData: async () => {},
  switchToAdminRole: () => {},
  switchToBusinessRole: () => {},
  switchToEmployeeRole: () => {},
  switchToEmployeeViewAsAdmin: () => {},
  currentRole: null,
  resetAppContext: () => {},
});

function AppProvider({ children }: { children: React.ReactNode }) {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentRoute, setCurrentRoute] = useState(location.pathname);
  const [selectedCompany, setSelectedCompany] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [userRoles, setUserRoles] = useState<UserRoles | null>(null);
  const [currentRole, setCurrentRole] = useState<string | null>(null);
  const [employeeData, setEmployeeData] = useState<any>(null);
  const [dashboardStats, setDashboardStats] = useState<any>(null);
  const [companyList, setCompanyList] = useState<any[]>([]);
  const [viewingEmployeeId, setViewingEmployeeId] = useState<string | null>(null);

  const refreshData = async () => {
    try {
      console.log('[AppContext] Starting data refresh...');
      
      // Only fetch companies if user has admin rights
      let companies = { data: [] };
      let employees = { data: [] };
      
      try {
        if (userRoles?.isPayrollAdmin || userRoles?.isBusinessUser) {
          console.log('[AppContext] Fetching companies for admin/business user...');
          companies = await adminService.getAllCompanies();
        }
      } catch (companyError) {
        console.error('[AppContext] Error fetching companies:', companyError);
      }
      
      try {
        if (selectedCompany?.companyID) {
          console.log('[AppContext] Fetching employees for company:', selectedCompany.companyID);
          employees = await employerService.getAllEmployees();
        } else {
          console.log('[AppContext] No selected company, skipping employee fetch');
        }
      } catch (employeeError) {
        console.error('[AppContext] Error fetching employees:', employeeError);
      }

      // Extract and filter users array for employees and contractors
      const users = Array.isArray(employees.users) ? employees.users : [];
      const employeesList = users.filter(u => u.WorkerType && u.WorkerType.WorkerType === 'W2');
      const contractorsList = users.filter(u => u.WorkerType && u.WorkerType.WorkerType !== 'W2');
      console.log('AppContext filtered employees:', employeesList.length, 'contractors:', contractorsList.length);
      console.log('AppContext users array:', users);
      setCompanyList(companies.data || []);
      setEmployeeData({
        employees: employeesList,
        contractors: contractorsList
      });
      setDashboardStats({
        // payroll: payrollData.data, // Removed
        // bankBalance: bankData.data?.balance // Removed
      });
      
      console.log('[AppContext] Data refresh completed successfully');
    } catch (error) {
      console.error('[AppContext] Error refreshing data:', error);
    }
  };

  // Function to switch to admin role
  const switchToAdminRole = () => {
    console.log("switchToAdminRole called, userRoles:", userRoles);
    
    // Check if user has admin privileges
    if (userRoles?.isPayrollAdmin || userRoles?.isBusinessUser) {
      console.log("User has admin privileges, switching role");
      
      // Update state and localStorage
      setCurrentRole('admin');
      localStorage.setItem('currentRole', 'admin');
      
      // Navigate to admin dashboard
      navigate('/admin');
      
      // Force refresh user roles to ensure they're up to date
      setTimeout(() => {
        console.log("Refreshing user data after role switch");
        fetchUserProfile();
        refreshData();
      }, 100);
    } else {
      console.log("User does not have admin privileges");
    }
  };

  // Function to switch to business user role
  const switchToBusinessRole = () => {
    if (userRoles?.isBusinessUser) {
      setCurrentRole('business');
      localStorage.setItem('currentRole', 'business');
      // Use navigate instead of window.location for a smoother transition
      navigate('/employer/dashboard');
      
      // Force refresh user roles to ensure they're up to date
      setTimeout(() => {
        fetchUserProfile();
        refreshData();
      }, 100);
    }
  };

  // Function to switch to employee role
  const switchToEmployeeRole = () => {
    if (userRoles?.isEmployee) {
      setCurrentRole('employee');
      localStorage.setItem('currentRole', 'employee');
      // Use navigate instead of window.location for a smoother transition
      navigate('/employee/dashboard');
      
      // Force refresh user roles to ensure they're up to date
      setTimeout(() => {
        fetchUserProfile();
        refreshData();
      }, 100);
    }
  };
  
  // Function for admin to view as a specific employee
  const switchToEmployeeViewAsAdmin = async (employeeId: string) => {
    console.log("switchToEmployeeViewAsAdmin called with employeeId:", employeeId);
    
    try {
      // Check if user has admin privileges
      if (userRoles?.isPayrollAdmin || userRoles?.isBusinessUser) {
        console.log("Admin is switching to view as employee:", employeeId);
        
        // Make sure we have a company ID
        let companyId = localStorage.getItem('selectedCompanyId');
        
        // If not in localStorage, try to get it from Supabase
        if (!companyId) {
          const { data: { session } } = await supabase.auth.getSession();
          
          if (session?.user?.email) {
            const { data: userProfile, error: profileError } = await supabase
              .from('temp_website2_users')
              .select('company_id')
              .eq('email', session.user.email)
              .maybeSingle();
            
            if (!profileError && userProfile && userProfile.company_id) {
              companyId = userProfile.company_id;
              localStorage.setItem('selectedCompanyId', companyId);
              console.log("Set company ID from Supabase:", companyId);
            } else {
              console.error("Error getting company ID from Supabase:", profileError);
            }
          }
        }
        
        if (!companyId) {
          console.error("No company ID found");
          alert("Company ID is missing. Please try again.");
          return;
        }
        
        // Store the employee ID being viewed
        setViewingEmployeeId(employeeId);
        localStorage.setItem('viewingEmployeeId', employeeId);
        
        // Set role to 'admin-as-employee' to distinguish from regular employee
        setCurrentRole('admin-as-employee');
        localStorage.setItem('currentRole', 'admin-as-employee');
        
        // Update the employee view store
        // This will be used by components to determine if admin is viewing as employee
        try {
          // Import dynamically to avoid circular dependencies
          const { useEmployeeViewStore } = await import('../store/employeeViewStore');
          const setSelectedEmployee = useEmployeeViewStore.getState().setSelectedEmployee;
          const setIsAdminViewingAsEmployee = useEmployeeViewStore.getState().setIsAdminViewingAsEmployee;
          
          // Update the store
          setSelectedEmployee(employeeId);
          setIsAdminViewingAsEmployee(true);
          
          console.log("Updated employee view store with employee ID:", employeeId);
        } catch (err) {
          console.error("Error updating employee view store:", err);
        }
        
        // Navigate directly to employee details page
        navigate('/employee/details');
        
        // Force refresh data
        setTimeout(() => {
          refreshData();
        }, 100);
      } else {
        console.log("User does not have admin privileges to view as employee");
      }
    } catch (error) {
      console.error("Error in switchToEmployeeViewAsAdmin:", error);
    }
  };

  // Fetch user profile and determine roles
  const fetchUserProfile = async () => {
    try {
      console.log("[AppContext] Fetching user profile");
      const { data: { session } } = await supabase.auth.getSession();
      console.log("[AppContext] Session:", session);
      
      if (session?.user) {
        console.log("[AppContext] User session found:", session.user.email);
        
        // Fetch user profile from database
        const { data: userProfile, error } = await supabase
          .from('temp_website2_users')
          .select('*')
          .eq('email', session.user.email)
          .maybeSingle();
        console.log("[AppContext] Querying temp_website2_users with email:", session.user.email);
        console.log("[AppContext] User profile result:", userProfile, error);
        
        if (error) {
          console.error("[AppContext] Error fetching user profile:", error);
          setUserData(null);
          setUserRoles(null);
          setCurrentRole(null);
          localStorage.removeItem('currentRole');
          localStorage.removeItem('userRoles');
        } else if (userProfile) {
          setUserData(userProfile);
          // Set selected company if company_id exists
          if (userProfile.company_id) {
            const companyObj = {
              companyID: userProfile.company_id,
              company: userProfile.company_name || ''
            };
            setSelectedCompany(companyObj);
            console.log('[AppContext] Set selectedCompany in state:', companyObj);
          } else {
            setSelectedCompany(null);
            console.log('[AppContext] No company_id found in userProfile');
          }
          // Determine user roles based on is_business_user
          const isBusinessUser = userProfile.is_business_user || false;
          const isPayrollAdmin = false; // Not used
          const isEmployee = !isBusinessUser;
          const roles = { isBusinessUser, isPayrollAdmin, isEmployee };
          setUserRoles(roles);
          console.log('[AppContext] Determined user roles:', roles);
          
          // Set current role and navigate
          let defaultRole = 'employee';
          let navigationPath = '/employee/dashboard';
          
          if (isBusinessUser) {
            defaultRole = 'business';
            navigationPath = '/employer/dashboard';
          }
          
          setCurrentRole(defaultRole);
          console.log('[AppContext] Set currentRole:', defaultRole);
          
          // Store role in localStorage for consistency
          localStorage.setItem('currentRole', defaultRole);
          localStorage.setItem('userRoles', JSON.stringify(roles));
          
          // Navigate to appropriate dashboard
          const currentPath = location.pathname;
          if (currentPath === '/login' || currentPath === '/') {
            console.log('[AppContext] Navigating to:', navigationPath);
            navigate(navigationPath);
          }
        } else {
          console.log("[AppContext] No user profile found in database for user:", session.user.email);
          setUserData(null);
          setSelectedCompany(null);
          const defaultRoles = {
            isBusinessUser: false,
            isPayrollAdmin: false,
            isEmployee: true
          };
          setUserRoles(defaultRoles);
          setCurrentRole('employee');
          localStorage.setItem('currentRole', 'employee');
          localStorage.setItem('userRoles', JSON.stringify(defaultRoles));
          
          // Navigate to employee dashboard if no profile found
          const currentPath = location.pathname;
          if (currentPath === '/login' || currentPath === '/') {
            console.log('[AppContext] No profile found, navigating to employee dashboard');
            navigate('/employee/dashboard');
          }
        }
      } else {
        console.log("[AppContext] No user session found");
      }
    } catch (error) {
      console.error('[AppContext] Error fetching user profile:', error);
    }
  };

  useEffect(() => {
    console.log('[AppContext] Location changed to:', location.pathname);
    setCurrentRoute(location.pathname);
    const storedCompany = localStorage.getItem('selectedCompany');
    if (storedCompany) {
      console.log('[AppContext] Found stored company:', storedCompany);
      setSelectedCompany(JSON.parse(storedCompany));
    }
    fetchUserProfile();
  }, [location]);

  // Separate useEffect for refreshData that depends on userData and userRoles
  useEffect(() => {
    if (userData && userRoles) {
      console.log('[AppContext] User data and roles available, refreshing data...');
      refreshData();
    } else {
      console.log('[AppContext] Waiting for user data and roles before refreshing data');
    }
  }, [userData, userRoles, selectedCompany]);

  const updateSelectedCompany = (company: any) => {
    setSelectedCompany(company);
    refreshData();
    console.log('[AppContext] Updated selectedCompany in state:', company);
  };

  // Reset all context state (for logout)
  const resetAppContext = () => {
    setCurrentRoute('/login');
    setSelectedCompany(null);
    setUserData(null);
    setUserRoles(null);
    setCurrentRole(null);
    setEmployeeData(null);
    setDashboardStats(null);
    setCompanyList([]);
    setViewingEmployeeId(null);
  };

  return (
    <AppContext.Provider 
      value={{
        currentRoute,
        selectedCompany,
        userData,
        userRoles,
        employeeData,
        dashboardStats,
        companyList,
        updateSelectedCompany,
        refreshData,
        switchToAdminRole,
        switchToBusinessRole,
        switchToEmployeeRole,
        switchToEmployeeViewAsAdmin,
        currentRole,
        resetAppContext,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

function useAppContext() {
  return useContext(AppContext);
}

export { AppProvider, useAppContext };

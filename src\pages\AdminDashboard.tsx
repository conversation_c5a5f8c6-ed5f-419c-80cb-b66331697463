import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Sidebar from '../components/Sidebar';
import { FiRefreshCcw } from 'react-icons/fi';
import { adminService, type AdminCompanyResponse } from '../services/adminService';
import { useCopilotReadable, useCopilotAction } from '@copilotkit/react-core';
import { useAppContext } from '../context/AppContext';
import { supabase } from '../lib/supabaseClient';

const AdminDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [companies, setCompanies] = useState<AdminCompanyResponse[]>([]);
  const [selectedCompany, setSelectedCompany] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null); // Add missing error state
  const { userRoles, switchToEmployeeRole } = useAppContext();
  const [canSwitchToEmployee, setCanSwitchToEmployee] = useState(false);

  useEffect(() => {
    // Check if user has employee role to determine if they can switch roles
    const checkUserRoles = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.user) {
          // Fetch user profile from database
          const { data: userProfile, error } = await supabase
            .from('temp_website2_users')
            .select('*')
            .eq('user_id', session.user.id)
            .single();
          
          if (!error && userProfile) {
            // User can switch to employee role if they are NOT just a regular employee
            // They need to be either a payroll admin or a business user
            const isBusinessUser = !!userProfile.business_user_id || !!userProfile.is_business_user;
            const isPayrollAdmin = !!userProfile.is_payroll_admin;
            
            // Can switch if they have admin or business user roles AND have a company_id
            setCanSwitchToEmployee((isPayrollAdmin || isBusinessUser) && !!userProfile.company_id);
          } else {
            // Try to get roles from localStorage as fallback
            const storedRoles = localStorage.getItem('userRoles');
            if (storedRoles) {
              const roles = JSON.parse(storedRoles);
              setCanSwitchToEmployee((roles.isPayrollAdmin || roles.isBusinessUser) && roles.isEmployee);
            }
          }
        }
      } catch (err) {
        console.error('Error checking user roles:', err);
      }
    };
    
    checkUserRoles();
  }, []);

  // Define Copilot readable states
  useCopilotReadable({
    name: "companies",
    description: "List of all companies in the system",
    value: companies,
  });

  useCopilotReadable({
    name: "selectedCompany",
    description: "Currently selected company",
    value: selectedCompany,
  });

  useCopilotReadable({
    name: "dashboardStats",
    description: "Dashboard statistics including total companies, active companies, and total employees",
    value: {
      totalCompanies: companies.length,
      activeCompanies: companies.filter(c => c.status === 'active').length,
      totalEmployees: companies.reduce((sum, company) => sum + (company.employeeCount || 0), 0),
    },
  });

  // Define Copilot actions
  useCopilotAction({
    name: "selectCompany",
    description: "Select a company by ID or name",
    parameters: [{
      name: "companyIdentifier",
      type: "string",
      description: "The company ID or name to select"
    }],
    run: async (params) => {
      const company = companies.find(
        c => c.companyID === params.companyIdentifier || c.company === params.companyIdentifier
      );
      if (company) {
        handleCompanySelect(company.companyID);
        return `Selected company: ${company.company}`;
      }
      return "Company not found";
    },
  });

  useCopilotAction({
    name: "switchToEmployee",
    description: "Switch to employee dashboard view",
    parameters: [],
    run: async () => {
      handleSwitchToEmployee();
      return "Switched to employee dashboard";
    },
  });

  useEffect(() => {
    const fetchCompanies = async () => {
      setLoading(true);
      setError(null); // Reset error state before fetching
      try {
        const response = await adminService.getAllCompanies();
        
        if (response?.data && Array.isArray(response.data)) {
          setCompanies(response.data);
          
          const growthPodsDemo = response.data.find(company => company.company === 'GrowthPods Demo');
          if (growthPodsDemo) {
            setSelectedCompany(growthPodsDemo.companyID);
            handleCompanySelect(growthPodsDemo.companyID);
          }
          // Removed auto-selection of GrowthPods Demo
          setSelectedCompany(''); // Ensure default selection is empty

        } else {
          setError('Invalid data format received from server');
          setCompanies([]);
        }
      } catch (error: any) {
        setError(error.response?.data?.message || 'Failed to fetch companies');
        setCompanies([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCompanies();
  }, []);


  const handleCompanySelect = (companyId: string) => {
    setSelectedCompany(companyId);
    const selectedCompany = companies.find(company => company.companyID === companyId);
    
    if (selectedCompany) {
      localStorage.setItem('selectedCompany', JSON.stringify(selectedCompany));
      navigate('/employer/dashboard');
    }
  };

  const handleSwitchToEmployee = () => {
    if (canSwitchToEmployee) {
      switchToEmployeeRole();
    }
  };

  return (
    <div className="flex min-h-screen bg-white">
      <Sidebar />
      
      <div className="flex-1 p-8 page-transition">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-semibold">Welcome Raj R</h1>
          <div className="flex space-x-4">
            {canSwitchToEmployee && (
              <button
                onClick={handleSwitchToEmployee}
                className="flex items-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <FiRefreshCcw className="mr-2" />
                Switch to Employee
              </button>
            )}
            <button 
              onClick={() => navigate('/admin/add-company')}
              className="bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700"
            >
              Add New Company
            </button>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-6">
          {/* Company Selection Card */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-gray-500 mb-2">Company Selection</h3>
            {loading ? (
              <div className="text-gray-500">Loading companies...</div>
            ) : error ? (
              <div className="text-red-500">{error}</div>
            ) : (
              <select 
                className="w-full p-2 border border-gray-300 rounded-md"
                value={selectedCompany}
                onChange={(e) => handleCompanySelect(e.target.value)}
              >
                <option value="">Select Company</option>
                {Array.isArray(companies) && companies.map(company => (
                  <option key={company.companyID} value={company.companyID}>
                    {company.company}
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* Number of Companies Card */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-gray-500 mb-2">Number of Companies</h3>
            <p className="text-4xl font-bold">{Array.isArray(companies) ? companies.length : 0}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;

# Rollfi API - Detailed Technical Documentation

## Introduction

This document provides comprehensive technical documentation for the Rollfi Payroll API suite. It includes detailed information for each endpoint across all sections, including cURL examples, request body formats, parameter descriptions, response codes, and response body examples.


# Company Onboarding API - Detailed Documentation

## Overview

The Company Onboarding API provides endpoints for seamlessly integrating a new company into the Rollfi system. This section details the endpoints necessary for registering a company, setting up bank accounts, and configuring initial settings.

## Endpoints

### POST createBusiness

The `createBusiness` API offers a holistic solution for seamlessly integrating a new company into the system. This API is designed to streamline the onboarding process by consolidating multiple steps into a single, efficient call. This approach is ideal for businesses seeking a quick and straightforward onboarding experience.

**Endpoint:** `/companyOnboarding#createBusiness`

**Features:**
1. Company Registration
2. KYB Information
3. Location Setup
4. Admin User Addition
5. KYB Process Initiation

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#createBusiness' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "createBusiness",
  "registration": {
    "company": "Wise Wonders co",
    "businessWebsite": "www.Wonders.com",
    "nacisSubCategoryCode": 6233,
    "isEsign": true,
    "isTermsAccepted": true
  },
  "kybInformation": {
    "ein": "78-8965412",
    "entityType": "LLP",
    "dateOfIncorporation": "YYYY-MM-DD",
    "irsAssisgnedFederalFilingForm": "941"
  },
  "companyLocation": {
    "companyLocation": "Suggested Values : M",
    "address1": "8745 Colard Ln",
    "address2": "",
    "city": "Lyons",
    "state": "CO",
    "zipcode": "80540",
    "phoneNumber": "9874563210",
    "isWorkLocation": true,
    "isMailingAddress": true,
    "isFilingAddress": true
  },
  "businessUser": {
    "firstName": "Jason",
    "middleName": "",
    "lastName": "Roy",
    "phoneNumber": "9874563210",
    "email": "<EMAIL>",
    "address1": "8745 Colard Ln",
    "address2": "",
    "city": "Lyons",
    "state": "CO",
    "zipcode": "80540",
    "ssn": "***********",
    "dateOfBirth": "2000-09-09",
    "payrollAdmin": true,
    "bookKeeper": true,
    "beneficialOwner": true,
    "ownershipPercentage": 25
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "createBusiness"
- `registration` (object, required):
  - `company` (string, required): Company name
  - `businessWebsite` (string, required): Company website URL
  - `nacisSubCategoryCode` (number, required): NAICS code for business category
  - `isEsign` (boolean, required): Whether e-signature is enabled
  - `isTermsAccepted` (boolean, required): Whether terms are accepted
- `kybInformation` (object, required):
  - `ein` (string, required): Employer Identification Number
  - `entityType` (string, required): Business entity type (e.g., "LLP")
  - `dateOfIncorporation` (string, required): Date in "YYYY-MM-DD" format
  - `irsAssisgnedFederalFilingForm` (string, required): IRS form code
- `companyLocation` (object, required):
  - `companyLocation` (string, required): Location type
  - `address1` (string, required): Primary address
  - `address2` (string, optional): Secondary address
  - `city` (string, required): City name
  - `state` (string, required): State code
  - `zipcode` (string, required): ZIP code
  - `phoneNumber` (string, required): Contact phone number
  - `isWorkLocation` (boolean, required): Whether this is a work location
  - `isMailingAddress` (boolean, required): Whether this is a mailing address
  - `isFilingAddress` (boolean, required): Whether this is a filing address
- `businessUser` (object, required):
  - `firstName` (string, required): User's first name
  - `middleName` (string, optional): User's middle name
  - `lastName` (string, required): User's last name
  - `phoneNumber` (string, required): User's phone number
  - `email` (string, required): User's email address
  - `address1` (string, required): User's primary address
  - `address2` (string, optional): User's secondary address
  - `city` (string, required): User's city
  - `state` (string, required): User's state code
  - `zipcode` (string, required): User's ZIP code
  - `ssn` (string, required): User's Social Security Number
  - `dateOfBirth` (string, required): User's date of birth in "YYYY-MM-DD" format
  - `payrollAdmin` (boolean, required): Whether user is a payroll admin
  - `bookKeeper` (boolean, required): Whether user is a bookkeeper
  - `beneficialOwner` (boolean, required): Whether user is a beneficial owner
  - `ownershipPercentage` (number, required): User's ownership percentage

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "registration": {
    "companyId": "9801C0B2-FAA4-47E5-8173-DD6F29C7C397",
    "kybInformationId": "32E9AE44-529A-484E-9C40-56DFCE99E712",
    "businessUserId": "4B47C1DA-F279-40E8-ABF6-ADD82E7C2582",
    "companyLocationId": "FB3B108C-62C2-43D8-A1EF-B3C6C5C6FAE3",
    "status": "Registered",
    "message": "A company with name Wise Wonders co is registered successfully and KYB verification is in progress."
  }
}
```

**Error Response (400):**
```json
{
  "error": {
    "code": "400",
    "message": "Invalid request parameters"
  }
}
```

**Note:** NAICS codes are a numerical system with a range from two to six digits. These codes are used for various administrative, regulatory, contracting, taxation, and business development purposes. The NAICS codes are being deprecated soon.

### POST companyRegistration

Endpoint for registering a company in the system.

**Endpoint:** `/companyOnboarding#companyRegistration`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#companyRegistration' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "companyRegistration",
  "registration": {
    "company": "Wise Wonders co",
    "businessWebsite": "www.Wonders.com",
    "nacisSubCategoryCode": 6233,
    "isEsign": true,
    "isTermsAccepted": true
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "companyRegistration"
- `registration` (object, required):
  - `company` (string, required): Company name
  - `businessWebsite` (string, required): Company website URL
  - `nacisSubCategoryCode` (number, required): NAICS code for business category
  - `isEsign` (boolean, required): Whether e-signature is enabled
  - `isTermsAccepted` (boolean, required): Whether terms are accepted

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "registration": {
    "companyId": "9801C0B2-FAA4-47E5-8173-DD6F29C7C397",
    "status": "Registered",
    "message": "A company with name Wise Wonders co is registered successfully."
  }
}
```

### PUT updateAdminUser

Endpoint for updating information about an administrative user.

**Endpoint:** `/companyOnboarding#updateAdminUser`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#updateAdminUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateAdminUser",
  "businessUser": {
    "businessUserId": "4B47C1DA-F279-40E8-ABF6-ADD82E7C2582",
    "firstName": "Jason",
    "middleName": "Michael",
    "lastName": "Roy",
    "phoneNumber": "9874563210",
    "email": "<EMAIL>",
    "address1": "8745 Colard Ln",
    "address2": "Suite 101",
    "city": "Lyons",
    "state": "CO",
    "zipcode": "80540",
    "payrollAdmin": true,
    "bookKeeper": true,
    "beneficialOwner": true,
    "ownershipPercentage": 30
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateAdminUser"
- `businessUser` (object, required):
  - `businessUserId` (string, required): ID of the business user to update
  - `firstName` (string, optional): User's first name
  - `middleName` (string, optional): User's middle name
  - `lastName` (string, optional): User's last name
  - `phoneNumber` (string, optional): User's phone number
  - `email` (string, optional): User's email address
  - `address1` (string, optional): User's primary address
  - `address2` (string, optional): User's secondary address
  - `city` (string, optional): User's city
  - `state` (string, optional): User's state code
  - `zipcode` (string, optional): User's ZIP code
  - `payrollAdmin` (boolean, optional): Whether user is a payroll admin
  - `bookKeeper` (boolean, optional): Whether user is a bookkeeper
  - `beneficialOwner` (boolean, optional): Whether user is a beneficial owner
  - `ownershipPercentage` (number, optional): User's ownership percentage

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "businessUser": {
    "businessUserId": "4B47C1DA-F279-40E8-ABF6-ADD82E7C2582",
    "status": "Updated",
    "message": "Business user information updated successfully."
  }
}
```

### POST addCompanyLocation

Endpoint for adding a new location to a company's profile.

**Endpoint:** `/companyOnboarding#addCompanyLocation`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#addCompanyLocation' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addCompanyLocation",
  "companyLocation": {
    "companyId": "9801C0B2-FAA4-47E5-8173-DD6F29C7C397",
    "companyLocation": "Branch Office",
    "address1": "123 Business Ave",
    "address2": "Floor 3",
    "city": "Denver",
    "state": "CO",
    "zipcode": "80202",
    "phoneNumber": "3035551234",
    "isWorkLocation": true,
    "isMailingAddress": false,
    "isFilingAddress": false
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addCompanyLocation"
- `companyLocation` (object, required):
  - `companyId` (string, required): ID of the company
  - `companyLocation` (string, required): Location type or name
  - `address1` (string, required): Primary address
  - `address2` (string, optional): Secondary address
  - `city` (string, required): City name
  - `state` (string, required): State code
  - `zipcode` (string, required): ZIP code
  - `phoneNumber` (string, required): Contact phone number
  - `isWorkLocation` (boolean, required): Whether this is a work location
  - `isMailingAddress` (boolean, required): Whether this is a mailing address
  - `isFilingAddress` (boolean, required): Whether this is a filing address

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "companyLocation": {
    "companyLocationId": "FB3B108C-62C2-43D8-A1EF-B3C6C5C6FAE3",
    "status": "Added",
    "message": "Company location added successfully."
  }
}
```

### POST addKybInformation

Endpoint for adding Know Your Business (KYB) information to a company profile.

**Endpoint:** `/companyOnboarding#addKybInformation`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#addKybInformation' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addKybInformation",
  "kybInformation": {
    "companyId": "9801C0B2-FAA4-47E5-8173-DD6F29C7C397",
    "ein": "78-8965412",
    "entityType": "LLP",
    "dateOfIncorporation": "2020-01-15",
    "irsAssisgnedFederalFilingForm": "941"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addKybInformation"
- `kybInformation` (object, required):
  - `companyId` (string, required): ID of the company
  - `ein` (string, required): Employer Identification Number
  - `entityType` (string, required): Business entity type (e.g., "LLP", "LLC", "Corporation")
  - `dateOfIncorporation` (string, required): Date in "YYYY-MM-DD" format
  - `irsAssisgnedFederalFilingForm` (string, required): IRS form code

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "kybInformation": {
    "kybInformationId": "32E9AE44-529A-484E-9C40-56DFCE99E712",
    "status": "Added",
    "message": "KYB information added successfully."
  }
}
```

### POST initiateCompanyKyb

Endpoint for initiating the KYB verification process for a company.

**Endpoint:** `/companyOnboarding#initiateCompanyKyb`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#initiateCompanyKyb' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "initiateCompanyKyb",
  "companyId": "9801C0B2-FAA4-47E5-8173-DD6F29C7C397"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "initiateCompanyKyb"
- `companyId` (string, required): ID of the company to initiate KYB for

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "kybVerification": {
    "status": "Initiated",
    "message": "KYB verification process has been initiated successfully."
  }
}
```

### POST addBusinessUser

Endpoint for adding a new business user to a company.

**Endpoint:** `/companyOnboarding#addBusinessUser`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#addBusinessUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addBusinessUser",
  "businessUser": {
    "companyId": "9801C0B2-FAA4-47E5-8173-DD6F29C7C397",
    "firstName": "Sarah",
    "middleName": "",
    "lastName": "Johnson",
    "phoneNumber": "7205551234",
    "email": "<EMAIL>",
    "address1": "456 Main St",
    "address2": "Apt 2B",
    "city": "Boulder",
    "state": "CO",
    "zipcode": "80301",
    "ssn": "***********",
    "dateOfBirth": "1985-06-15",
    "payrollAdmin": false,
    "bookKeeper": true,
    "beneficialOwner": false,
    "ownershipPercentage": 0
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addBusinessUser"
- `businessUser` (object, required):
  - `companyId` (string, required): ID of the company
  - `firstName` (string, required): User's first name
  - `middleName` (string, optional): User's middle name
  - `lastName` (string, required): User's last name
  - `phoneNumber` (string, required): User's phone number
  - `email` (string, required): User's email address
  - `address1` (string, required): User's primary address
  - `address2` (string, optional): User's secondary address
  - `city` (string, required): User's city
  - `state` (string, required): User's state code
  - `zipcode` (string, required): User's ZIP code
  - `ssn` (string, required): User's Social Security Number
  - `dateOfBirth` (string, required): User's date of birth in "YYYY-MM-DD" format
  - `payrollAdmin` (boolean, required): Whether user is a payroll admin
  - `bookKeeper` (boolean, required): Whether user is a bookkeeper
  - `beneficialOwner` (boolean, required): Whether user is a beneficial owner
  - `ownershipPercentage` (number, required): User's ownership percentage

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "businessUser": {
    "businessUserId": "5C8D7E9A-B2F1-48D3-9E6C-7A1B2C3D4E5F",
    "status": "Added",
    "message": "Business user added successfully."
  }
}
```

### PUT updateKybInformation

Endpoint for updating the KYB information of a company.

**Endpoint:** `/companyOnboarding#updateKybInformation`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#updateKybInformation' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateKybInformation",
  "kybInformation": {
    "kybInformationId": "32E9AE44-529A-484E-9C40-56DFCE99E712",
    "ein": "78-8965412",
    "entityType": "LLC",
    "dateOfIncorporation": "2020-01-15",
    "irsAssisgnedFederalFilingForm": "941"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateKybInformation"
- `kybInformation` (object, required):
  - `kybInformationId` (string, required): ID of the KYB information to update
  - `ein` (string, optional): Employer Identification Number
  - `entityType` (string, optional): Business entity type
  - `dateOfIncorporation` (string, optional): Date in "YYYY-MM-DD" format
  - `irsAssisgnedFederalFilingForm` (string, optional): IRS form code

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "kybInformation": {
    "kybInformationId": "32E9AE44-529A-484E-9C40-56DFCE99E712",
    "status": "Updated",
    "message": "KYB information updated successfully."
  }
}
```

### DEL deleteBusinessUser

Endpoint for removing a business user from a company.

**Endpoint:** `/companyOnboarding#deleteBusinessUser`

**cURL Example:**
```bash
curl --request DELETE \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#deleteBusinessUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "deleteBusinessUser",
  "businessUserId": "5C8D7E9A-B2F1-48D3-9E6C-7A1B2C3D4E5F"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "deleteBusinessUser"
- `businessUserId` (string, required): ID of the business user to delete

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "businessUser": {
    "businessUserId": "5C8D7E9A-B2F1-48D3-9E6C-7A1B2C3D4E5F",
    "status": "Deleted",
    "message": "Business user deleted successfully."
  }
}
```

### PUT updateCompany

Endpoint for updating general company information.

**Endpoint:** `/companyOnboarding#updateCompany`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/companyOnboarding#updateCompany' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateCompany",
  "company": {
    "companyId": "9801C0B2-FAA4-47E5-8173-DD6F29C7C397",
    "companyName": "Wise Wonders Corporation",
    "businessWebsite": "www.wisewonders.com",
    "nacisSubCategoryCode": 6233
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateCompany"
- `company` (object, required):
  - `companyId` (string, required): ID of the company to update
  - `companyName` (string, optional): Updated company name
  - `businessWebsite` (string, optional): Updated company website URL
  - `nacisSubCategoryCode` (number, optional): Updated NAICS code

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "company": {
    "companyId": "9801C0B2-FAA4-47E5-8173-DD6F29C7C397",
    "status": "Updated",
    "message": "Company information updated successfully."
  }
}
```

**Note:** NAICS codes are a numerical system with a range from two to six digits. These codes are used for various administrative, regulatory, contracting, taxation, and business development purposes. The NAICS codes are being deprecated soon.
# Employee/User Onboarding API - Detailed Documentation

## Overview

The Employee/User Onboarding API provides endpoints for onboarding new employees into the Rollfi system. This section details the APIs used to onboard new employees, including personal details and tax information.

## Endpoints

### POST addKycInformation

Use this endpoint to add Know Your Customer (KYC) information if it satisfies the condition - The customer operates from the company's registered location.

**Endpoint:** `/userOnboarding#addKycInformation`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/userOnboarding#addKycInformation' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addKycInformation",
  "kycInformation": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "ssn": "*********",
    "dateOfBirth": "2000-01-01",
    "address1": "777 West Convention Way",
    "address2": "",
    "city": "Anaheim",
    "state": "CA",
    "zipcode": "92802"
  },
  "userRemoteLocation": {
    "address1": "e",
    "address2": "e",
    "city": "e",
    "state": "AZ",
    "zipcode": "50023"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addKycInformation"
- `kycInformation` (object, required):
  - `userId` (string, required): ID of the user
  - `ssn` (string, required): Social Security Number
  - `dateOfBirth` (string, required): Date of birth in "YYYY-MM-DD" format
  - `address1` (string, required): Primary address
  - `address2` (string, optional): Secondary address
  - `city` (string, required): City name
  - `state` (string, required): State code
  - `zipcode` (string, required): ZIP code
- `userRemoteLocation` (object, optional): Required if user works remotely
  - `address1` (string, required): Remote location primary address
  - `address2` (string, optional): Remote location secondary address
  - `city` (string, required): Remote location city
  - `state` (string, required): Remote location state code
  - `zipcode` (string, required): Remote location ZIP code

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "kycInformation": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "status": "KYC Pending",
    "message": "The KYC Information for Jacob king has been added successfully."
  }
}
```

**Error Response (400):**
```json
{
  "error": {
    "code": "400",
    "message": "Invalid request parameters"
  }
}
```

### POST addW4Information

Endpoint for adding W4 tax information for an employee.

**Endpoint:** `/userOnboarding#addW4Information`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/userOnboarding#addW4Information' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addW4Information",
  "w4Information": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "filingStatus": "Single",
    "multipleJobsOrSpouseWorks": false,
    "claimDependents": false,
    "otherIncome": 0,
    "deductions": 0,
    "extraWithholding": 0
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addW4Information"
- `w4Information` (object, required):
  - `userId` (string, required): ID of the user
  - `filingStatus` (string, required): Tax filing status (e.g., "Single", "Married", "Head of Household")
  - `multipleJobsOrSpouseWorks` (boolean, required): Whether user has multiple jobs or spouse works
  - `claimDependents` (boolean, required): Whether user claims dependents
  - `otherIncome` (number, required): Amount of other income
  - `deductions` (number, required): Amount of deductions
  - `extraWithholding` (number, required): Amount of extra withholding

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "w4Information": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "status": "Added",
    "message": "W4 information has been added successfully."
  }
}
```

### PUT acceptTermsAndCondition

Endpoint for accepting terms and conditions during the user onboarding process.

**Endpoint:** `/userOnboarding#acceptTermsAndCondition`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/userOnboarding#acceptTermsAndCondition' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "acceptTermsAndCondition",
  "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
  "isTermsAccepted": true
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "acceptTermsAndCondition"
- `userId` (string, required): ID of the user
- `isTermsAccepted` (boolean, required): Whether terms are accepted

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "termsAndCondition": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "status": "Accepted",
    "message": "Terms and conditions have been accepted successfully."
  }
}
```

### POST initiateUserKyc

Endpoint for initiating the KYC verification process for a user.

**Endpoint:** `/userOnboarding#initiateUserKyc`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/userOnboarding#initiateUserKyc' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "initiateUserKyc",
  "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "initiateUserKyc"
- `userId` (string, required): ID of the user to initiate KYC for

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "kycVerification": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "status": "Initiated",
    "message": "KYC verification process has been initiated successfully."
  }
}
```

### PUT updateKycInformation

Endpoint for updating the KYC information of a user.

**Endpoint:** `/userOnboarding#updateKycInformation`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/userOnboarding#updateKycInformation' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateKycInformation",
  "kycInformation": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "ssn": "*********",
    "dateOfBirth": "2000-01-01",
    "address1": "777 West Convention Way",
    "address2": "Suite 101",
    "city": "Anaheim",
    "state": "CA",
    "zipcode": "92802"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateKycInformation"
- `kycInformation` (object, required):
  - `userId` (string, required): ID of the user
  - `ssn` (string, optional): Social Security Number
  - `dateOfBirth` (string, optional): Date of birth in "YYYY-MM-DD" format
  - `address1` (string, optional): Primary address
  - `address2` (string, optional): Secondary address
  - `city` (string, optional): City name
  - `state` (string, optional): State code
  - `zipcode` (string, optional): ZIP code

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "kycInformation": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "status": "Updated",
    "message": "KYC information has been updated successfully."
  }
}
```

### POST addStateW4Information

Endpoint for adding state-specific W4 tax information for an employee.

**Endpoint:** `/userOnboarding#addStateW4Information`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/userOnboarding#addStateW4Information' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addStateW4Information",
  "stateW4Information": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "state": "CA",
    "filingStatus": "Single",
    "allowances": 1,
    "additionalWithholding": 0,
    "exemption": false
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addStateW4Information"
- `stateW4Information` (object, required):
  - `userId` (string, required): ID of the user
  - `state` (string, required): State code
  - `filingStatus` (string, required): State tax filing status
  - `allowances` (number, required): Number of allowances
  - `additionalWithholding` (number, required): Additional withholding amount
  - `exemption` (boolean, required): Whether user claims exemption

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "stateW4Information": {
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "state": "CA",
    "status": "Added",
    "message": "State W4 information has been added successfully."
  }
}
```

### PUT updateStateW4Information

Endpoint for updating state-specific W4 tax information for an employee.

**Endpoint:** `/userOnboarding#updateStateW4Information`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/userOnboarding#updateStateW4Information' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateStateW4Information",
  "stateW4Information": {
    "stateW4InformationId": "7b8c9d0e-1f2a-3b4c-5d6e-7f8a9b0c1d2e",
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "state": "CA",
    "filingStatus": "Married",
    "allowances": 2,
    "additionalWithholding": 50,
    "exemption": false
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateStateW4Information"
- `stateW4Information` (object, required):
  - `stateW4InformationId` (string, required): ID of the state W4 information to update
  - `userId` (string, required): ID of the user
  - `state` (string, optional): State code
  - `filingStatus` (string, optional): State tax filing status
  - `allowances` (number, optional): Number of allowances
  - `additionalWithholding` (number, optional): Additional withholding amount
  - `exemption` (boolean, optional): Whether user claims exemption

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "stateW4Information": {
    "stateW4InformationId": "7b8c9d0e-1f2a-3b4c-5d6e-7f8a9b0c1d2e",
    "status": "Updated",
    "message": "State W4 information has been updated successfully."
  }
}
```

### PUT updateW4Information

Endpoint for updating W4 tax information for an employee.

**Endpoint:** `/userOnboarding#updateW4Information`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/userOnboarding#updateW4Information' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateW4Information",
  "w4Information": {
    "w4InformationId": "3e4f5a6b-7c8d-9e0f-1a2b-3c4d5e6f7a8b",
    "userId": "5a2390e1-0f83-46bd-ae1c-81b30fe364e1",
    "filingStatus": "Married",
    "multipleJobsOrSpouseWorks": true,
    "claimDependents": true,
    "otherIncome": 5000,
    "deductions": 2000,
    "extraWithholding": 100
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateW4Information"
- `w4Information` (object, required):
  - `w4InformationId` (string, required): ID of the W4 information to update
  - `userId` (string, required): ID of the user
  - `filingStatus` (string, optional): Tax filing status
  - `multipleJobsOrSpouseWorks` (boolean, optional): Whether user has multiple jobs or spouse works
  - `claimDependents` (boolean, optional): Whether user claims dependents
  - `otherIncome` (number, optional): Amount of other income
  - `deductions` (number, optional): Amount of deductions
  - `extraWithholding` (number, optional): Amount of extra withholding

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "w4Information": {
    "w4InformationId": "3e4f5a6b-7c8d-9e0f-1a2b-3c4d5e6f7a8b",
    "status": "Updated",
    "message": "W4 information has been updated successfully."
  }
}
```
# Admin Portal API - Detailed Documentation

## Overview

The Admin Portal API provides endpoints for employers to manage company information, employee data, bank accounts, and other administrative functions.

## Endpoints

### POST addCompanyBankAccount

This API is used to add the Company Bank Account Details.

**Endpoint:** `/adminPortal#addCompanyBankAccount`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/adminPortal#addCompanyBankAccount' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addCompanyBankAccount",
  "companyFundingSourceEntity": {
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "accountNumber": "**********",
    "routingNumber": "*********",
    "bankName": "Chase Bank",
    "accountType": "savings",
    "accountName": "default"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addCompanyBankAccount"
- `companyFundingSourceEntity` (object, required):
  - `companyId` (string, required): ID of the company
  - `accountNumber` (string, required): Bank account number (max length: 40)
  - `routingNumber` (string, required): Bank routing number (max length: 40)
  - `bankName` (string, required): Name of the bank (max length: 40)
  - `accountType` (string, required): Type of account (e.g., "savings", "checking")
  - `accountName` (string, required): Name of the account

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "companyFundingSourceEntity": {
    "companyFundingSourceEntityId": "5E5A316D-5226-47CF-BDC8-994EDC01C8B7",
    "status": "Ready",
    "message": "The Company's bank account has been added successfully."
  }
}
```

### PUT deactivateCompanyBankAccount

Endpoint for deactivating a company's bank account.

**Endpoint:** `/adminPortal#deactivateCompanyBankAccount`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/adminPortal#deactivateCompanyBankAccount' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "deactivateCompanyBankAccount",
  "companyFundingSourceEntityId": "5E5A316D-5226-47CF-BDC8-994EDC01C8B7"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "deactivateCompanyBankAccount"
- `companyFundingSourceEntityId` (string, required): ID of the company funding source entity to deactivate

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "companyFundingSourceEntity": {
    "companyFundingSourceEntityId": "5E5A316D-5226-47CF-BDC8-994EDC01C8B7",
    "status": "Deactivated",
    "message": "The Company's bank account has been deactivated successfully."
  }
}
```

### POST addUser

Endpoint for adding a new user to the system.

**Endpoint:** `/adminPortal#addUser`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/adminPortal#addUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addUser",
  "user": {
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "firstName": "John",
    "middleName": "",
    "lastName": "Doe",
    "phoneNumber": "**********",
    "email": "<EMAIL>",
    "address1": "123 Main St",
    "address2": "Apt 4B",
    "city": "Anytown",
    "state": "CA",
    "zipcode": "90210",
    "dateOfJoin": "2023-05-15"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addUser"
- `user` (object, required):
  - `companyId` (string, required): ID of the company
  - `firstName` (string, required): User's first name
  - `middleName` (string, optional): User's middle name
  - `lastName` (string, required): User's last name
  - `phoneNumber` (string, required): User's phone number
  - `email` (string, required): User's email address
  - `address1` (string, required): User's primary address
  - `address2` (string, optional): User's secondary address
  - `city` (string, required): User's city
  - `state` (string, required): User's state code
  - `zipcode` (string, required): User's ZIP code
  - `dateOfJoin` (string, required): User's join date in "YYYY-MM-DD" format

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "user": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "status": "Added",
    "message": "User has been added successfully."
  }
}
```

### POST addBusinessContractor

Endpoint for adding a business contractor to the company.

**Endpoint:** `/adminPortal#addBusinessContractor`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/adminPortal#addBusinessContractor' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addBusinessContractor",
  "businessContractor": {
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "businessName": "ABC Consulting",
    "ein": "12-3456789",
    "phoneNumber": "5559876543",
    "email": "<EMAIL>",
    "address1": "456 Business Ave",
    "address2": "Suite 200",
    "city": "Enterprise",
    "state": "NV",
    "zipcode": "89123",
    "dateOfContract": "2023-06-01"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addBusinessContractor"
- `businessContractor` (object, required):
  - `companyId` (string, required): ID of the company
  - `businessName` (string, required): Name of the contractor business
  - `ein` (string, required): Employer Identification Number
  - `phoneNumber` (string, required): Contact phone number
  - `email` (string, required): Contact email address
  - `address1` (string, required): Primary address
  - `address2` (string, optional): Secondary address
  - `city` (string, required): City
  - `state` (string, required): State code
  - `zipcode` (string, required): ZIP code
  - `dateOfContract` (string, required): Contract start date in "YYYY-MM-DD" format

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "businessContractor": {
    "businessContractorId": "3A4B5C6D-7E8F-9G0H-1I2J-3K4L5M6N7O8P",
    "status": "Added",
    "message": "Business contractor has been added successfully."
  }
}
```

### PUT updateUser

Endpoint for updating user information.

**Endpoint:** `/adminPortal#updateUser`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/adminPortal#updateUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateUser",
  "user": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "firstName": "John",
    "middleName": "Robert",
    "lastName": "Doe",
    "phoneNumber": "**********",
    "email": "<EMAIL>",
    "address1": "123 Main St",
    "address2": "Apt 5C",
    "city": "Anytown",
    "state": "CA",
    "zipcode": "90210"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateUser"
- `user` (object, required):
  - `userId` (string, required): ID of the user to update
  - `firstName` (string, optional): User's first name
  - `middleName` (string, optional): User's middle name
  - `lastName` (string, optional): User's last name
  - `phoneNumber` (string, optional): User's phone number
  - `email` (string, optional): User's email address
  - `address1` (string, optional): User's primary address
  - `address2` (string, optional): User's secondary address
  - `city` (string, optional): User's city
  - `state` (string, optional): User's state code
  - `zipcode` (string, optional): User's ZIP code

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "user": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "status": "Updated",
    "message": "User information has been updated successfully."
  }
}
```

### POST addUsers

Endpoint for adding multiple users at once.

**Endpoint:** `/adminPortal#addUsers`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/adminPortal#addUsers' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addUsers",
  "users": [
    {
      "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
      "firstName": "Jane",
      "middleName": "",
      "lastName": "Smith",
      "phoneNumber": "5551112222",
      "email": "<EMAIL>",
      "address1": "789 Oak St",
      "address2": "",
      "city": "Sometown",
      "state": "NY",
      "zipcode": "10001",
      "dateOfJoin": "2023-05-20"
    },
    {
      "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
      "firstName": "Michael",
      "middleName": "James",
      "lastName": "Johnson",
      "phoneNumber": "5553334444",
      "email": "<EMAIL>",
      "address1": "456 Pine St",
      "address2": "Unit 10",
      "city": "Othertown",
      "state": "TX",
      "zipcode": "75001",
      "dateOfJoin": "2023-05-22"
    }
  ]
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addUsers"
- `users` (array, required): Array of user objects
  - Each user object contains the same fields as in the addUser endpoint

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "users": {
    "status": "Added",
    "message": "2 users have been added successfully.",
    "userIds": [
      "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
      "9P8O7N6M-5L4K-3J2I-1H0G-F9E8D7C6B5A4"
    ]
  }
}
```

### POST addStateRegistration

Endpoint for adding state registration information for a company.

**Endpoint:** `/adminPortal#addStateRegistration`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/adminPortal#addStateRegistration' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addStateRegistration",
  "stateRegistration": {
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "state": "CA",
    "stateEIN": "123-4567-8",
    "stateUnemploymentInsuranceRate": 3.5,
    "stateDisabilityInsuranceRate": 1.2,
    "effectiveDate": "2023-01-01"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addStateRegistration"
- `stateRegistration` (object, required):
  - `companyId` (string, required): ID of the company
  - `state` (string, required): State code
  - `stateEIN` (string, required): State Employer Identification Number
  - `stateUnemploymentInsuranceRate` (number, required): State unemployment insurance rate percentage
  - `stateDisabilityInsuranceRate` (number, required): State disability insurance rate percentage
  - `effectiveDate` (string, required): Effective date in "YYYY-MM-DD" format

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "stateRegistration": {
    "stateRegistrationId": "1A2B3C4D-5E6F-7G8H-9I0J-1K2L3M4N5O6P",
    "status": "Added",
    "message": "State registration information has been added successfully."
  }
}
```

### PUT updateStateRegistration

Endpoint for updating state registration information.

**Endpoint:** `/adminPortal#updateStateRegistration`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/adminPortal#updateStateRegistration' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateStateRegistration",
  "stateRegistration": {
    "stateRegistrationId": "1A2B3C4D-5E6F-7G8H-9I0J-1K2L3M4N5O6P",
    "stateUnemploymentInsuranceRate": 3.8,
    "stateDisabilityInsuranceRate": 1.5,
    "effectiveDate": "2023-07-01"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateStateRegistration"
- `stateRegistration` (object, required):
  - `stateRegistrationId` (string, required): ID of the state registration to update
  - `stateEIN` (string, optional): State Employer Identification Number
  - `stateUnemploymentInsuranceRate` (number, optional): State unemployment insurance rate percentage
  - `stateDisabilityInsuranceRate` (number, optional): State disability insurance rate percentage
  - `effectiveDate` (string, optional): Effective date in "YYYY-MM-DD" format

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "stateRegistration": {
    "stateRegistrationId": "1A2B3C4D-5E6F-7G8H-9I0J-1K2L3M4N5O6P",
    "status": "Updated",
    "message": "State registration information has been updated successfully."
  }
}
```

### POST addUserWage

Endpoint for adding wage information for a user.

**Endpoint:** `/adminPortal#addUserWage`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/adminPortal#addUserWage' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addUserWage",
  "userWage": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "wageType": "Hourly",
    "wageAmount": 25.50,
    "effectiveDate": "2023-06-01",
    "payScheduleId": "2B3C4D5E-6F7G-8H9I-0J1K-2L3M4N5O6P7Q"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addUserWage"
- `userWage` (object, required):
  - `userId` (string, required): ID of the user
  - `wageType` (string, required): Type of wage (e.g., "Hourly", "Salary")
  - `wageAmount` (number, required): Amount of wage
  - `effectiveDate` (string, required): Effective date in "YYYY-MM-DD" format
  - `payScheduleId` (string, required): ID of the pay schedule

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "userWage": {
    "userWageId": "8B9C0D1E-2F3G-4H5I-6J7K-8L9M0N1O2P3Q",
    "status": "Added",
    "message": "User wage information has been added successfully."
  }
}
```

### PUT updateUserWage

Endpoint for updating wage information for a user.

**Endpoint:** `/adminPortal#updateUserWage`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/adminPortal#updateUserWage' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateUserWage",
  "userWage": {
    "userWageId": "8B9C0D1E-2F3G-4H5I-6J7K-8L9M0N1O2P3Q",
    "wageType": "Hourly",
    "wageAmount": 27.75,
    "effectiveDate": "2023-07-15"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateUserWage"
- `userWage` (object, required):
  - `userWageId` (string, required): ID of the user wage to update
  - `wageType` (string, optional): Type of wage
  - `wageAmount` (number, optional): Amount of wage
  - `effectiveDate` (string, optional): Effective date in "YYYY-MM-DD" format
  - `payScheduleId` (string, optional): ID of the pay schedule

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "userWage": {
    "userWageId": "8B9C0D1E-2F3G-4H5I-6J7K-8L9M0N1O2P3Q",
    "status": "Updated",
    "message": "User wage information has been updated successfully."
  }
}
```

### PUT updateBusinessUserAsEmployee

Endpoint for updating a business user to an employee role.

**Endpoint:** `/adminPortal#updateBusinessUserAsEmployee`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/adminPortal#updateBusinessUserAsEmployee' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateBusinessUserAsEmployee",
  "businessUser": {
    "businessUserId": "4B47C1DA-F279-40E8-ABF6-ADD82E7C2582",
    "dateOfJoin": "2023-06-01",
    "payScheduleId": "2B3C4D5E-6F7G-8H9I-0J1K-2L3M4N5O6P7Q",
    "wageType": "Salary",
    "wageAmount": 85000
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateBusinessUserAsEmployee"
- `businessUser` (object, required):
  - `businessUserId` (string, required): ID of the business user
  - `dateOfJoin` (string, required): Join date in "YYYY-MM-DD" format
  - `payScheduleId` (string, required): ID of the pay schedule
  - `wageType` (string, required): Type of wage
  - `wageAmount` (number, required): Amount of wage

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "businessUser": {
    "businessUserId": "4B47C1DA-F279-40E8-ABF6-ADD82E7C2582",
    "status": "Updated",
    "message": "Business user has been updated as an employee successfully."
  }
}
```

### PUT updateEmployeeAsBusinessUser

Endpoint for updating an employee to a business user role.

**Endpoint:** `/adminPortal#updateEmployeeAsBusinessUser`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/adminPortal#updateEmployeeAsBusinessUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateEmployeeAsBusinessUser",
  "user": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "payrollAdmin": true,
    "bookKeeper": false,
    "beneficialOwner": false,
    "ownershipPercentage": 0
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateEmployeeAsBusinessUser"
- `user` (object, required):
  - `userId` (string, required): ID of the user
  - `payrollAdmin` (boolean, required): Whether user is a payroll admin
  - `bookKeeper` (boolean, required): Whether user is a bookkeeper
  - `beneficialOwner` (boolean, required): Whether user is a beneficial owner
  - `ownershipPercentage` (number, required): User's ownership percentage

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "user": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "status": "Updated",
    "message": "Employee has been updated as a business user successfully."
  }
}
```

### PUT updateBusinessUser

Endpoint for updating business user information.

**Endpoint:** `/adminPortal#updateBusinessUser`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/adminPortal#updateBusinessUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateBusinessUser",
  "businessUser": {
    "businessUserId": "4B47C1DA-F279-40E8-ABF6-ADD82E7C2582",
    "payrollAdmin": true,
    "bookKeeper": true,
    "beneficialOwner": true,
    "ownershipPercentage": 35
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateBusinessUser"
- `businessUser` (object, required):
  - `businessUserId` (string, required): ID of the business user
  - `payrollAdmin` (boolean, optional): Whether user is a payroll admin
  - `bookKeeper` (boolean, optional): Whether user is a bookkeeper
  - `beneficialOwner` (boolean, optional): Whether user is a beneficial owner
  - `ownershipPercentage` (number, optional): User's ownership percentage

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "businessUser": {
    "businessUserId": "4B47C1DA-F279-40E8-ABF6-ADD82E7C2582",
    "status": "Updated",
    "message": "Business user information has been updated successfully."
  }
}
```

### PUT updateCompanyLocation

Endpoint for updating company location information.

**Endpoint:** `/adminPortal#updateCompanyLocation`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/adminPortal#updateCompanyLocation' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateCompanyLocation",
  "companyLocation": {
    "companyLocationId": "FB3B108C-62C2-43D8-A1EF-B3C6C5C6FAE3",
    "companyLocation": "Headquarters",
    "address1": "123 Corporate Blvd",
    "address2": "Floor 5",
    "city": "Denver",
    "state": "CO",
    "zipcode": "80202",
    "phoneNumber": "3035551234",
    "isWorkLocation": true,
    "isMailingAddress": true,
    "isFilingAddress": true
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateCompanyLocation"
- `companyLocation` (object, required):
  - `companyLocationId` (string, required): ID of the company location to update
  - `companyLocation` (string, optional): Location type or name
  - `address1` (string, optional): Primary address
  - `address2` (string, optional): Secondary address
  - `city` (string, optional): City name
  - `state` (string, optional): State code
  - `zipcode` (string, optional): ZIP code
  - `phoneNumber` (string, optional): Contact phone number
  - `isWorkLocation` (boolean, optional): Whether this is a work location
  - `isMailingAddress` (boolean, optional): Whether this is a mailing address
  - `isFilingAddress` (boolean, optional): Whether this is a filing address

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "companyLocation": {
    "companyLocationId": "FB3B108C-62C2-43D8-A1EF-B3C6C5C6FAE3",
    "status": "Updated",
    "message": "Company location has been updated successfully."
  }
}
```

### POST activateUser

Endpoint for activating a user in the system.

**Endpoint:** `/adminPortal#activateUser`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/adminPortal#activateUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "activateUser",
  "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "activateUser"
- `userId` (string, required): ID of the user to activate

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "user": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "status": "Activated",
    "message": "User has been activated successfully."
  }
}
```

### POST deactivateUser

Endpoint for deactivating a user in the system.

**Endpoint:** `/adminPortal#deactivateUser`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/adminPortal#deactivateUser' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "deactivateUser",
  "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "deactivateUser"
- `userId` (string, required): ID of the user to deactivate

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "user": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "status": "Deactivated",
    "message": "User has been deactivated successfully."
  }
}
```
# User Portal API - Detailed Documentation

## Overview

The User Portal API provides endpoints for employees to manage their own information, including bank accounts and personal details.

## Endpoints

### POST addUserBankAccount

In order to save the Company Funding Source Entity this Endpoint is used. It comprises of information related to bank to process payroll.

**Endpoint:** `/userPortal#addUserBankAccount`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/userPortal#addUserBankAccount' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addUserBankAccount",
  "userPayAccountEntity": {
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "userId": "e066e305-3816-4b06-8576-50d2502be436",
    "accountNumber": "**********",
    "routingNumber": "*********",
    "bankName": "Chase Bank",
    "accountType": "savings",
    "accountName": "default",
    "payPercentage": 50.98
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addUserBankAccount"
- `userPayAccountEntity` (object, required):
  - `companyId` (string, required): ID of the company
  - `userId` (string, required): ID of the user
  - `accountNumber` (string, required): Bank account number
  - `routingNumber` (string, required): Bank routing number
  - `bankName` (string, required): Name of the bank
  - `accountType` (string, required): Type of account (e.g., "savings", "checking")
  - `accountName` (string, required): Name of the account
  - `payPercentage` (number, required): Percentage of pay to deposit to this account

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "userPayAccountEntity": {
    "userPayAccountEntityId": "7C8D9E0F-1A2B-3C4D-5E6F-7G8H9I0J1K2L",
    "status": "Added",
    "message": "User bank account has been added successfully."
  }
}
```

### PUT updateUserBankAccount

Endpoint for updating a user's bank account information.

**Endpoint:** `/userPortal#updateUserBankAccount`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/userPortal#updateUserBankAccount' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateUserBankAccount",
  "userPayAccountEntity": {
    "userPayAccountEntityId": "7C8D9E0F-1A2B-3C4D-5E6F-7G8H9I0J1K2L",
    "accountNumber": "**********",
    "routingNumber": "*********",
    "bankName": "Chase Bank",
    "accountType": "checking",
    "accountName": "primary",
    "payPercentage": 100
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateUserBankAccount"
- `userPayAccountEntity` (object, required):
  - `userPayAccountEntityId` (string, required): ID of the user pay account entity to update
  - `accountNumber` (string, optional): Bank account number
  - `routingNumber` (string, optional): Bank routing number
  - `bankName` (string, optional): Name of the bank
  - `accountType` (string, optional): Type of account
  - `accountName` (string, optional): Name of the account
  - `payPercentage` (number, optional): Percentage of pay to deposit to this account

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "userPayAccountEntity": {
    "userPayAccountEntityId": "7C8D9E0F-1A2B-3C4D-5E6F-7G8H9I0J1K2L",
    "status": "Updated",
    "message": "User bank account has been updated successfully."
  }
}
```

### PUT updateKycInformation

Endpoint for updating a user's Know Your Customer (KYC) information.

**Endpoint:** `/userPortal#updateKycInformation`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/userPortal#updateKycInformation' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateKycInformation",
  "kycInformation": {
    "userId": "e066e305-3816-4b06-8576-50d2502be436",
    "ssn": "*********",
    "dateOfBirth": "2000-01-01",
    "address1": "777 West Convention Way",
    "address2": "Suite 101",
    "city": "Anaheim",
    "state": "CA",
    "zipcode": "92802"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateKycInformation"
- `kycInformation` (object, required):
  - `userId` (string, required): ID of the user
  - `ssn` (string, optional): Social Security Number
  - `dateOfBirth` (string, optional): Date of birth in "YYYY-MM-DD" format
  - `address1` (string, optional): Primary address
  - `address2` (string, optional): Secondary address
  - `city` (string, optional): City name
  - `state` (string, optional): State code
  - `zipcode` (string, optional): ZIP code

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "kycInformation": {
    "userId": "e066e305-3816-4b06-8576-50d2502be436",
    "status": "Updated",
    "message": "KYC information has been updated successfully."
  }
}
```
# Payroll API - Detailed Documentation

## Overview

The Payroll API provides endpoints for managing payroll processes, including pay schedules, payroll data import, and payroll initiation.

## Endpoints

### POST addPaySchedule

Add Pay Schedule Endpoint - Use this endpoint to create a new Pay Schedule for the company's employees.

**Endpoint:** `/payroll#addPaySchedule`

**Note:** When CompensationFrequency is Monthly/Semi-Monthly please use [this link](https://docs.google.com/spreadsheets/d/12LWp9jWqiovMQf18XaWPPWv_oOFvbZAsLHKSP3Q74pQ/ed) to Update the PayBeginDate.

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/payroll#addPaySchedule' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addPaySchedule",
  "paySchedule": {
    "companyId": "3199E230-A3D8-4205-AD04-DEB7D9B7B9C2",
    "payBeginDate": "2023-12-09",
    "standardWorkingHours": 8,
    "workerType": "1099-NEC",
    "compensationFrequency": "BiWeekly",
    "option": 1,
    "paymentMode": "Automatic"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addPaySchedule"
- `paySchedule` (object, required):
  - `companyId` (string, required): ID of the company
  - `payBeginDate` (string, required): Start date of the pay period in "YYYY-MM-DD" format
  - `standardWorkingHours` (number, required): Standard working hours per day
  - `workerType` (string, required): Type of worker (e.g., "1099-NEC", "W2")
  - `compensationFrequency` (string, required): Frequency of compensation (e.g., "BiWeekly", "Monthly", "Semi-Monthly")
  - `option` (number, required): Option number for the pay schedule
  - `paymentMode` (string, required): Mode of payment (e.g., "Automatic", "Manual")

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "paySchedule": {
    "payScheduleId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "status": "Added",
    "message": "Pay schedule has been added successfully."
  }
}
```

### POST importRegularDifferntialPayrollData

Endpoint for importing regular differential payroll data.

**Endpoint:** `/payroll#importRegularDifferntialPayrollData`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/payroll#importRegularDifferntialPayrollData' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "importRegularDifferntialPayrollData",
  "payrollData": {
    "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "payrollLineItems": [
      {
        "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
        "regularHours": 80,
        "overtimeHours": 5,
        "doubleTimeHours": 0,
        "differentialHours": 10,
        "differentialRate": 1.5
      },
      {
        "userId": "9P8O7N6M-5L4K-3J2I-1H0G-F9E8D7C6B5A4",
        "regularHours": 75,
        "overtimeHours": 0,
        "doubleTimeHours": 0,
        "differentialHours": 0,
        "differentialRate": 0
      }
    ]
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "importRegularDifferntialPayrollData"
- `payrollData` (object, required):
  - `payPeriodId` (string, required): ID of the pay period
  - `payrollLineItems` (array, required): Array of payroll line items
    - Each item contains:
      - `userId` (string, required): ID of the user
      - `regularHours` (number, required): Regular hours worked
      - `overtimeHours` (number, required): Overtime hours worked
      - `doubleTimeHours` (number, required): Double time hours worked
      - `differentialHours` (number, required): Differential hours worked
      - `differentialRate` (number, required): Differential rate multiplier

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "payrollData": {
    "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "status": "Imported",
    "message": "Payroll data has been imported successfully."
  }
}
```

### POST importRegularPayrollLData

Endpoint for importing regular payroll data.

**Endpoint:** `/payroll#importRegularPayrollLData`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/payroll#importRegularPayrollLData' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "importRegularPayrollLData",
  "payrollData": {
    "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "payrollLineItems": [
      {
        "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
        "regularHours": 80,
        "overtimeHours": 5,
        "doubleTimeHours": 0
      },
      {
        "userId": "9P8O7N6M-5L4K-3J2I-1H0G-F9E8D7C6B5A4",
        "regularHours": 75,
        "overtimeHours": 0,
        "doubleTimeHours": 0
      }
    ]
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "importRegularPayrollLData"
- `payrollData` (object, required):
  - `payPeriodId` (string, required): ID of the pay period
  - `payrollLineItems` (array, required): Array of payroll line items
    - Each item contains:
      - `userId` (string, required): ID of the user
      - `regularHours` (number, required): Regular hours worked
      - `overtimeHours` (number, required): Overtime hours worked
      - `doubleTimeHours` (number, required): Double time hours worked

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "payrollData": {
    "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "status": "Imported",
    "message": "Payroll data has been imported successfully."
  }
}
```

### POST deactivatePaySchedule

Endpoint for deactivating a pay schedule.

**Endpoint:** `/payroll#deactivatePaySchedule`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/payroll#deactivatePaySchedule' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "deactivatePaySchedule",
  "payScheduleId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "deactivatePaySchedule"
- `payScheduleId` (string, required): ID of the pay schedule to deactivate

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "paySchedule": {
    "payScheduleId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "status": "Deactivated",
    "message": "Pay schedule has been deactivated successfully."
  }
}
```

### POST addSupplementalPayPeriod

Endpoint for adding a supplemental pay period.

**Endpoint:** `/payroll#addSupplementalPayPeriod`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/payroll#addSupplementalPayPeriod' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addSupplementalPayPeriod",
  "supplementalPayPeriod": {
    "payScheduleId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "payPeriodName": "Year-End Bonus",
    "payDate": "2023-12-15"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addSupplementalPayPeriod"
- `supplementalPayPeriod` (object, required):
  - `payScheduleId` (string, required): ID of the pay schedule
  - `payPeriodName` (string, required): Name of the supplemental pay period
  - `payDate` (string, required): Date of payment in "YYYY-MM-DD" format

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "supplementalPayPeriod": {
    "supplementalPayPeriodId": "5C6D7E8F-9G0H-1I2J-3K4L-5M6N7O8P9Q0R",
    "status": "Added",
    "message": "Supplemental pay period has been added successfully."
  }
}
```

### POST initiatePayroll

Endpoint for initiating the payroll process.

**Endpoint:** `/payroll#initiatePayroll`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/payroll#initiatePayroll' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "initiatePayroll",
  "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "initiatePayroll"
- `payPeriodId` (string, required): ID of the pay period to initiate

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "payroll": {
    "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "status": "Initiated",
    "message": "Payroll has been initiated successfully."
  }
}
```

### POST addSupplementalPayrollLineItem

Endpoint for adding supplemental payroll line items.

**Endpoint:** `/payroll#addSupplementalPayrollLineItem`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/payroll#addSupplementalPayrollLineItem' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addSupplementalPayrollLineItem",
  "supplementalPayrollLineItem": {
    "supplementalPayPeriodId": "5C6D7E8F-9G0H-1I2J-3K4L-5M6N7O8P9Q0R",
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "amount": 1000,
    "description": "Year-End Performance Bonus"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addSupplementalPayrollLineItem"
- `supplementalPayrollLineItem` (object, required):
  - `supplementalPayPeriodId` (string, required): ID of the supplemental pay period
  - `userId` (string, required): ID of the user
  - `amount` (number, required): Amount of the supplemental payment
  - `description` (string, required): Description of the supplemental payment

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "supplementalPayrollLineItem": {
    "supplementalPayrollLineItemId": "6D7E8F9G-0H1I-2J3K-4L5M-6N7O8P9Q0R1S",
    "status": "Added",
    "message": "Supplemental payroll line item has been added successfully."
  }
}
```
# Benefits API - Detailed Documentation

## Overview

The Benefits API provides endpoints for managing employee benefits, including adding, updating, and removing benefits for both employees and companies.

## Endpoints

### POST addEmployeeBenefits

This endpoint is used to add employee benefits.

**Endpoint:** `/benefits#addEmployeeBenefits`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/benefits#addEmployeeBenefits' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addEmployeeBenefits",
  "benefitEmployeeElection": {
    "userId": "C0378C3F-0B62-491D-9CD9-1B4CE347D8F1",
    "benefitType": "Medical",
    "effectiveMonth": "December 2024",
    "companyContributionAmountPerPayPeriod": 55,
    "employeeDeductionAmountPerPayPeriod": 25
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addEmployeeBenefits"
- `benefitEmployeeElection` (object, required):
  - `userId` (string, required): ID of the user
  - `benefitType` (string, required): Type of benefit (e.g., "Medical", "Dental", "Vision")
  - `effectiveMonth` (string, required): Month when the benefit becomes effective
  - `companyContributionAmountPerPayPeriod` (number, required): Amount contributed by the company per pay period
  - `employeeDeductionAmountPerPayPeriod` (number, required): Amount deducted from employee's pay per pay period

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "Benefits": {
    "status": "Ready",
    "message": "The Benefits for Medical has been saved successfully.",
    "benefitType": "Medical"
  }
}
```

### PUT updateEmployeeBenefits

Endpoint for updating employee benefits.

**Endpoint:** `/benefits#updateEmployeeBenefits`

**cURL Example:**
```bash
curl --request PUT \
  --url 'https://sandbox.rollfi.xyz/benefits#updateEmployeeBenefits' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "updateEmployeeBenefits",
  "benefitEmployeeElection": {
    "benefitEmployeeElectionId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "companyContributionAmountPerPayPeriod": 60,
    "employeeDeductionAmountPerPayPeriod": 30,
    "effectiveMonth": "January 2025"
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "updateEmployeeBenefits"
- `benefitEmployeeElection` (object, required):
  - `benefitEmployeeElectionId` (string, required): ID of the benefit employee election to update
  - `companyContributionAmountPerPayPeriod` (number, optional): Updated amount contributed by the company per pay period
  - `employeeDeductionAmountPerPayPeriod` (number, optional): Updated amount deducted from employee's pay per pay period
  - `effectiveMonth` (string, optional): Updated month when the benefit becomes effective

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "Benefits": {
    "status": "Updated",
    "message": "The Benefits has been updated successfully.",
    "benefitEmployeeElectionId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M"
  }
}
```

### POST addCompanyEmployeeBenefits

Endpoint for adding benefits for multiple employees at the company level.

**Endpoint:** `/benefits#addCompanyEmployeeBenefits`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/benefits#addCompanyEmployeeBenefits' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addCompanyEmployeeBenefits",
  "companyBenefitId": "9B8C7D6E-5F4G-3H2I-1J0K-9L8M7N6O5P4Q",
  "benefitEmployeeElections": [
    {
      "userId": "C0378C3F-0B62-491D-9CD9-1B4CE347D8F1",
      "effectiveMonth": "December 2024",
      "companyContributionAmountPerPayPeriod": 55,
      "employeeDeductionAmountPerPayPeriod": 25
    },
    {
      "userId": "D1489D4G-1C73-592E-0DE0-2C5DF458E9G2",
      "effectiveMonth": "December 2024",
      "companyContributionAmountPerPayPeriod": 55,
      "employeeDeductionAmountPerPayPeriod": 25
    }
  ]
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addCompanyEmployeeBenefits"
- `companyBenefitId` (string, required): ID of the company benefit
- `benefitEmployeeElections` (array, required): Array of benefit employee elections
  - Each item contains:
    - `userId` (string, required): ID of the user
    - `effectiveMonth` (string, required): Month when the benefit becomes effective
    - `companyContributionAmountPerPayPeriod` (number, required): Amount contributed by the company per pay period
    - `employeeDeductionAmountPerPayPeriod` (number, required): Amount deducted from employee's pay per pay period

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "Benefits": {
    "status": "Ready",
    "message": "The Benefits for 2 employees have been saved successfully.",
    "companyBenefitId": "9B8C7D6E-5F4G-3H2I-1J0K-9L8M7N6O5P4Q"
  }
}
```

### POST removeCompanyBenefits

Endpoint for removing company benefits.

**Endpoint:** `/benefits#removeCompanyBenefits`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/benefits#removeCompanyBenefits' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "removeCompanyBenefits",
  "companyBenefitId": "9B8C7D6E-5F4G-3H2I-1J0K-9L8M7N6O5P4Q"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "removeCompanyBenefits"
- `companyBenefitId` (string, required): ID of the company benefit to remove

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "Benefits": {
    "status": "Removed",
    "message": "The Company Benefits has been removed successfully.",
    "companyBenefitId": "9B8C7D6E-5F4G-3H2I-1J0K-9L8M7N6O5P4Q"
  }
}
```

### POST removeEmployeeBenefits

Endpoint for removing employee benefits.

**Endpoint:** `/benefits#removeEmployeeBenefits`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/benefits#removeEmployeeBenefits' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "removeEmployeeBenefits",
  "benefitEmployeeElectionId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "removeEmployeeBenefits"
- `benefitEmployeeElectionId` (string, required): ID of the benefit employee election to remove

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "Benefits": {
    "status": "Removed",
    "message": "The Employee Benefits has been removed successfully.",
    "benefitEmployeeElectionId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M"
  }
}
```

### POST addCompanyBenefits

Endpoint for adding company-wide benefits.

**Endpoint:** `/benefits#addCompanyBenefits`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/benefits#addCompanyBenefits' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "addCompanyBenefits",
  "companyBenefit": {
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "benefitType": "Medical",
    "benefitName": "Premium Health Plan",
    "benefitDescription": "Comprehensive medical coverage for employees",
    "isActive": true
  }
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "addCompanyBenefits"
- `companyBenefit` (object, required):
  - `companyId` (string, required): ID of the company
  - `benefitType` (string, required): Type of benefit
  - `benefitName` (string, required): Name of the benefit
  - `benefitDescription` (string, required): Description of the benefit
  - `isActive` (boolean, required): Whether the benefit is active

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "Benefits": {
    "companyBenefitId": "9B8C7D6E-5F4G-3H2I-1J0K-9L8M7N6O5P4Q",
    "status": "Added",
    "message": "The Company Benefits has been added successfully."
  }
}
```

### GET getBenefitValidEffectiveMonths

Endpoint for retrieving valid effective months for benefits.

**Endpoint:** `/benefits#getBenefitValidEffectiveMonths`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/benefits#getBenefitValidEffectiveMonths?companyId=8A9B7683-8E02-494D-8422-963FD05EE785'
```

**Request Parameters:**
- `companyId` (string, required): ID of the company

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "validEffectiveMonths": [
    "December 2024",
    "January 2025",
    "February 2025",
    "March 2025",
    "April 2025",
    "May 2025"
  ]
}
```
# Reports API - Detailed Documentation

## Overview

The Reports API provides endpoints for retrieving various types of information about businesses, users, companies, and payroll data.

## Endpoints

### GET getBusinessUserInfo

Endpoint for retrieving detailed information about business users.

**Endpoint:** `/reports#getBusinessUserInfo`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getBusinessUserInfo' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getBusinessUserById",
  "businessUserId": "094ddef5-7c6e-4f6b-a7ed-10d28b11b9b9"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getBusinessUserById"
- `businessUserId` (string, required): ID of the business user to retrieve information for

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "businessUser": [
    {
      "businessUserId": "83D38CFC-DF7C-45D3-A986-97029DF1F36E",
      "businessUser": "Kim Harry Zen",
      "firstName": "Kim",
      "middleName": "Harry",
      "lastName": "Zen",
      "email": "<EMAIL>",
      "phoneNumber": "9988884441",
      "isPayrollAdmin": true,
      "isController": true,
      "isBeneficialOwner": true,
      "isEmployee": true,
      "ownershipPercentage": 25,
      "companyLocationID": "6E8F08D3-5556-470C-83EF-13C8426EFDBC",
      "address1": "8745 Colard Ln",
      "address2": "",
      "city": "Lyons",
      "state": "CO",
      "zipcode": "80540",
      "country": "US",
      "ssn": "MTIzMDk4MTQ5",
      "dateOfJoin": "2023-09-09",
      "dateOfBirth": "2000-09-09"
    }
  ]
}
```

### GET getUserTask

Endpoint for retrieving tasks assigned to a user.

**Endpoint:** `/reports#getUserTask`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getUserTask' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getUserTask",
  "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getUserTask"
- `userId` (string, required): ID of the user to retrieve tasks for

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "userTasks": [
    {
      "taskId": "9B8C7D6E-5F4G-3H2I-1J0K-9L8M7N6O5P4Q",
      "taskName": "Complete W4 Information",
      "taskDescription": "Please complete your W4 tax information",
      "taskStatus": "Pending",
      "dueDate": "2023-12-15"
    },
    {
      "taskId": "0C9D8E7F-6G5H-4I3J-2K1L-0M9N8O7P6Q5R",
      "taskName": "Review Benefits Options",
      "taskDescription": "Please review and select your benefits options",
      "taskStatus": "Completed",
      "dueDate": "2023-11-30"
    }
  ]
}
```

### GET getUsersByCompanyName

Endpoint for retrieving users by company name.

**Endpoint:** `/reports#getUsersByCompanyName`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getUsersByCompanyName' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getUsersByCompanyName",
  "companyName": "Acme Corporation"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getUsersByCompanyName"
- `companyName` (string, required): Name of the company to retrieve users for

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "users": [
    {
      "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
      "firstName": "John",
      "middleName": "Robert",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phoneNumber": "**********",
      "isActive": true,
      "dateOfJoin": "2023-05-15"
    },
    {
      "userId": "9P8O7N6M-5L4K-3J2I-1H0G-F9E8D7C6B5A4",
      "firstName": "Jane",
      "middleName": "",
      "lastName": "Smith",
      "email": "<EMAIL>",
      "phoneNumber": "5551112222",
      "isActive": true,
      "dateOfJoin": "2023-05-20"
    }
  ]
}
```

### GET getCompanies

Endpoint for retrieving a list of companies.

**Endpoint:** `/reports#getCompanies`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getCompanies' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getCompanies"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getCompanies"

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "companies": [
    {
      "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
      "companyName": "Acme Corporation",
      "ein": "12-3456789",
      "companyType": "LLC",
      "isActive": true
    },
    {
      "companyId": "3199E230-A3D8-4205-AD04-DEB7D9B7B9C2",
      "companyName": "XYZ Enterprises",
      "ein": "98-7654321",
      "companyType": "Corporation",
      "isActive": true
    }
  ]
}
```

### GET getPaidEmployeesCountBasedOnDateRange

Endpoint for retrieving the count of paid employees within a date range.

**Endpoint:** `/reports#getPaidEmployeesCountBasedOnDateRange`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getPaidEmployeesCountBasedOnDateRange' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getPaidEmployeesCountBasedOnDateRange",
  "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
  "startDate": "2023-01-01",
  "endDate": "2023-12-31"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getPaidEmployeesCountBasedOnDateRange"
- `companyId` (string, required): ID of the company
- `startDate` (string, required): Start date of the range in "YYYY-MM-DD" format
- `endDate` (string, required): End date of the range in "YYYY-MM-DD" format

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "paidEmployeesCount": 42
}
```

### GET getPayPeriodDetails

Endpoint for retrieving detailed information about a pay period.

**Endpoint:** `/reports#getPayPeriodDetails`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getPayPeriodDetails' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getPayPeriodDetails",
  "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getPayPeriodDetails"
- `payPeriodId` (string, required): ID of the pay period

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "payPeriodDetails": {
    "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "payScheduleId": "2B3C4D5E-6F7G-8H9I-0J1K-2L3M4N5O6P7Q",
    "payPeriodName": "December 1-15, 2023",
    "startDate": "2023-12-01",
    "endDate": "2023-12-15",
    "payDate": "2023-12-20",
    "status": "Processed",
    "totalGrossPay": 125000.00,
    "totalNetPay": 87500.00,
    "totalEmployees": 25
  }
}
```

### GET getPayPeriod

Endpoint for retrieving basic information about a pay period.

**Endpoint:** `/reports#getPayPeriod`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getPayPeriod' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getPayPeriod",
  "payScheduleId": "2B3C4D5E-6F7G-8H9I-0J1K-2L3M4N5O6P7Q"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getPayPeriod"
- `payScheduleId` (string, required): ID of the pay schedule

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "payPeriods": [
    {
      "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
      "payPeriodName": "December 1-15, 2023",
      "startDate": "2023-12-01",
      "endDate": "2023-12-15",
      "payDate": "2023-12-20",
      "status": "Processed"
    },
    {
      "payPeriodId": "5D6E7F8G-9H0I-1J2K-3L4M-5N6O7P8Q9R0S",
      "payPeriodName": "December 16-31, 2023",
      "startDate": "2023-12-16",
      "endDate": "2023-12-31",
      "payDate": "2024-01-05",
      "status": "Pending"
    }
  ]
}
```

### GET getOverTimeTypes

Endpoint for retrieving overtime types.

**Endpoint:** `/reports#getOverTimeTypes`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getOverTimeTypes' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getOverTimeTypes"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getOverTimeTypes"

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "overTimeTypes": [
    {
      "overTimeTypeId": "1A2B3C4D-5E6F-7G8H-9I0J-1K2L3M4N5O6P",
      "overTimeTypeName": "Regular Overtime",
      "overTimeTypeDescription": "Time and a half for hours worked over 40 per week",
      "overTimeTypeMultiplier": 1.5
    },
    {
      "overTimeTypeId": "2B3C4D5E-6F7G-8H9I-0J1K-2L3M4N5O6P7Q",
      "overTimeTypeName": "Double Time",
      "overTimeTypeDescription": "Double pay for hours worked on holidays or over 12 per day",
      "overTimeTypeMultiplier": 2.0
    }
  ]
}
```

### GET getDeductionDescription

Endpoint for retrieving deduction descriptions.

**Endpoint:** `/reports#getDeductionDescription`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getDeductionDescription' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getDeductionDescription"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getDeductionDescription"

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "deductionDescriptions": [
    {
      "deductionId": "3C4D5E6F-7G8H-9I0J-1K2L-3M4N5O6P7Q8R",
      "deductionName": "Health Insurance",
      "deductionDescription": "Employee contribution to health insurance premium"
    },
    {
      "deductionId": "4D5E6F7G-8H9I-0J1K-2L3M-4N5O6P7Q8R9S",
      "deductionName": "401(k)",
      "deductionDescription": "Employee contribution to 401(k) retirement plan"
    },
    {
      "deductionId": "5E6F7G8H-9I0J-1K2L-3M4N-5O6P7Q8R9S0T",
      "deductionName": "Dental Insurance",
      "deductionDescription": "Employee contribution to dental insurance premium"
    }
  ]
}
```

### GET getAdditionalCompensationDescription

Endpoint for retrieving additional compensation descriptions.

**Endpoint:** `/reports#getAdditionalCompensationDescription`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getAdditionalCompensationDescription' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getAdditionalCompensationDescription"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getAdditionalCompensationDescription"

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "additionalCompensationDescriptions": [
    {
      "compensationId": "6F7G8H9I-0J1K-2L3M-4N5O-6P7Q8R9S0T1U",
      "compensationName": "Bonus",
      "compensationDescription": "Performance or holiday bonus payment"
    },
    {
      "compensationId": "7G8H9I0J-1K2L-3M4N-5O6P-7Q8R9S0T1U2V",
      "compensationName": "Commission",
      "compensationDescription": "Sales commission payment"
    },
    {
      "compensationId": "8H9I0J1K-2L3M-4N5O-6P7Q-8R9S0T1U2V3W",
      "compensationName": "Reimbursement",
      "compensationDescription": "Reimbursement for business expenses"
    }
  ]
}
```

### GET getUsers

Endpoint for retrieving a list of users.

**Endpoint:** `/reports#getUsers`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getUsers' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getUsers",
  "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getUsers"
- `companyId` (string, required): ID of the company to retrieve users for

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "users": [
    {
      "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
      "firstName": "John",
      "middleName": "Robert",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phoneNumber": "**********",
      "isActive": true,
      "dateOfJoin": "2023-05-15"
    },
    {
      "userId": "9P8O7N6M-5L4K-3J2I-1H0G-F9E8D7C6B5A4",
      "firstName": "Jane",
      "middleName": "",
      "lastName": "Smith",
      "email": "<EMAIL>",
      "phoneNumber": "5551112222",
      "isActive": true,
      "dateOfJoin": "2023-05-20"
    }
  ]
}
```

### GET getCompanyInfo

Endpoint for retrieving detailed information about a company.

**Endpoint:** `/reports#getCompanyInfo`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/reports#getCompanyInfo' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "getCompanyInfo",
  "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "getCompanyInfo"
- `companyId` (string, required): ID of the company to retrieve information for

**Response Codes:**
- `200`: Success

**Success Response (200):**
```json
{
  "companyInfo": {
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "companyName": "Acme Corporation",
    "ein": "12-3456789",
    "companyType": "LLC",
    "industry": "Technology",
    "foundedDate": "2010-01-15",
    "fiscalYearEnd": "12-31",
    "website": "https://www.acmecorp.example.com",
    "primaryAddress": {
      "address1": "123 Corporate Blvd",
      "address2": "Floor 5",
      "city": "Denver",
      "state": "CO",
      "zipcode": "80202",
      "country": "US"
    },
    "mailingAddress": {
      "address1": "PO Box 12345",
      "address2": "",
      "city": "Denver",
      "state": "CO",
      "zipcode": "80201",
      "country": "US"
    },
    "primaryContact": {
      "name": "John Doe",
      "title": "CEO",
      "email": "<EMAIL>",
      "phoneNumber": "**********"
    },
    "employeeCount": 125,
    "isActive": true
  }
}
```
# Webhooks API - Detailed Documentation

## Overview

The Webhooks API provides endpoints for setting up real-time data transmission between the Rollfi Payroll API and your application or service. This allows for optimal synchronization of data.

## Endpoints

### POST subscribeWebhook

Endpoint for configuring and subscribing to webhooks.

**Endpoint:** `/webhooks#subscribeWebhook`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/webhooks#subscribeWebhook' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "subscribeWebhook",
  "url": "https://example.com/payroll-webhooks",
  "events": ["company.created", "employee.onboarded", "payroll.processed"]
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "subscribeWebhook"
- `url` (string, required): The callback URL for the webhook. Example: "https://example.com/payroll-webhooks"
- `events` (array, required): Array of event types to subscribe to

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "webhook": {
    "webhookId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "status": "Active",
    "message": "Webhook has been subscribed successfully."
  }
}
```

### POST unsubscribeWebhook

Endpoint for unsubscribing from webhooks.

**Endpoint:** `/webhooks#unsubscribeWebhook`

**cURL Example:**
```bash
curl --request POST \
  --url 'https://sandbox.rollfi.xyz/webhooks#unsubscribeWebhook' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "unsubscribeWebhook",
  "webhookId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "unsubscribeWebhook"
- `webhookId` (string, required): ID of the webhook to unsubscribe

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "webhook": {
    "webhookId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "status": "Inactive",
    "message": "Webhook has been unsubscribed successfully."
  }
}
```

### GET listWebhooks

Endpoint for listing all subscribed webhooks.

**Endpoint:** `/webhooks#listWebhooks`

**cURL Example:**
```bash
curl --request GET \
  --url 'https://sandbox.rollfi.xyz/webhooks#listWebhooks' \
  --header 'Content-Type: application/json' \
  --data '{
  "method": "listWebhooks"
}'
```

**Request Body Parameters:**
- `method` (string, required): Must be "listWebhooks"

**Response Codes:**
- `200`: Success
- `400`: Bad Request

**Success Response (200):**
```json
{
  "webhooks": [
    {
      "webhookId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
      "url": "https://example.com/payroll-webhooks",
      "events": ["company.created", "employee.onboarded", "payroll.processed"],
      "status": "Active",
      "createdAt": "2023-05-15T10:30:00Z"
    },
    {
      "webhookId": "9P8O7N6M-5L4K-3J2I-1H0G-F9E8D7C6B5A4",
      "url": "https://example.org/rollfi-events",
      "events": ["payroll.processed", "employee.updated"],
      "status": "Active",
      "createdAt": "2023-06-20T14:45:00Z"
    }
  ]
}
```

## Webhook Event Types

The following event types are available for subscription:

1. **company.created** - Triggered when a new company is created
2. **company.updated** - Triggered when company information is updated
3. **employee.onboarded** - Triggered when a new employee is onboarded
4. **employee.updated** - Triggered when employee information is updated
5. **payroll.initiated** - Triggered when a payroll process is initiated
6. **payroll.processed** - Triggered when a payroll process is completed
7. **payroll.failed** - Triggered when a payroll process fails
8. **benefit.added** - Triggered when a benefit is added
9. **benefit.updated** - Triggered when a benefit is updated
10. **benefit.removed** - Triggered when a benefit is removed

## Webhook Response Examples

### 1. Onboarding and Process Responses:

```json
{
  "event": "company.created",
  "timestamp": "2023-12-15T08:30:00Z",
  "data": {
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "companyName": "Acme Corporation",
    "status": "Active"
  }
}
```

### 2. Employee Event Response:

```json
{
  "event": "employee.onboarded",
  "timestamp": "2023-12-16T10:15:00Z",
  "data": {
    "userId": "7D8E9F0A-1B2C-3D4E-5F6G-7H8I9J0K1L2M",
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "status": "Active"
  }
}
```

### 3. Payroll Event Response:

```json
{
  "event": "payroll.processed",
  "timestamp": "2023-12-20T16:45:00Z",
  "data": {
    "payPeriodId": "4B5C6D7E-8F9G-0H1I-2J3K-4L5M6N7O8P9Q",
    "companyId": "8A9B7683-8E02-494D-8422-963FD05EE785",
    "payPeriodName": "December 1-15, 2023",
    "totalEmployees": 25,
    "totalGrossPay": 125000.00,
    "status": "Processed"
  }
}
```

## Best Practices for Webhook Implementation

1. **Implement Proper Error Handling**: Your webhook endpoint should return a 2xx HTTP status code to acknowledge receipt of the webhook event. If our system doesn't receive a 2xx response, it will retry sending the webhook several times.

2. **Process Webhooks Asynchronously**: To avoid timeouts, acknowledge receipt of the webhook immediately and process the data asynchronously.

3. **Implement Idempotency**: Design your webhook handler to be idempotent, as the same webhook may be delivered multiple times in rare cases.

4. **Verify Webhook Signatures**: Implement signature verification to ensure the webhook is coming from Rollfi.

5. **Monitor Webhook Deliveries**: Regularly check the webhook delivery status in your Rollfi dashboard to ensure your endpoints are receiving events properly.

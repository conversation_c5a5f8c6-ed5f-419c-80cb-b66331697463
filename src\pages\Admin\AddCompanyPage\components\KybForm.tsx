import React, { useState, ChangeEvent, FormEvent } from 'react';
import styles from '../AddCompanyPage.module.css';

interface KybData {
  ein: string;
  entityType: string;
  dateOfIncorporation: string; // YYYY-MM-DD
  irsAssisgnedFederalFilingForm: string;
}

interface KybFormProps {
  data: KybData;
  onNext: (data: { kybInformation: KybData }) => void;
  onBack: () => void;
}

const KybForm: React.FC<KybFormProps> = ({ data, onNext, onBack }) => {
  const [formData, setFormData] = useState<KybData>(data);
  const [errors, setErrors] = useState<Partial<Record<keyof KybData, string>>>({});

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name as keyof KybData]) {
      setErrors(prev => ({...prev, [name]: undefined}));
    }
  };

  const validate = (): boolean => {
    const newErrors: Partial<Record<keyof KybData, string>> = {};
    if (!formData.ein.trim()) {
      newErrors.ein = 'EIN is required.';
    } else if (!/^\d{9}$/.test(formData.ein)) {
      newErrors.ein = 'EIN must be a 9-digit number.';
    }
    if (!formData.entityType.trim()) newErrors.entityType = 'Entity type is required.';
    if (!formData.dateOfIncorporation) {
      newErrors.dateOfIncorporation = 'Date of incorporation is required.';
    } else {
      // Basic YYYY-MM-DD validation
      const datePattern = /^\d{4}-\d{2}-\d{2}$/;
      if (!datePattern.test(formData.dateOfIncorporation)) {
        newErrors.dateOfIncorporation = 'Date must be in YYYY-MM-DD format.';
      } else {
        const date = new Date(formData.dateOfIncorporation);
        const [year, month, day] = formData.dateOfIncorporation.split('-').map(Number);
        if (!(date.getFullYear() === year && date.getMonth() + 1 === month && date.getDate() === day)) {
          newErrors.dateOfIncorporation = 'Invalid date.';
        }
      }
    }
    if (!formData.irsAssisgnedFederalFilingForm.trim()) newErrors.irsAssisgnedFederalFilingForm = 'IRS Assigned Federal Filing Form is required.';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (validate()) {
      onNext({ kybInformation: formData });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <h2 className={styles.stepTitle}>Step 2: KYB Information</h2>
      
      <div className={styles.formGroup}>
        <label htmlFor="ein">EIN (Employer Identification Number) <sup>*</sup></label>
        <input
          type="text"
          id="ein"
          name="ein"
          value={formData.ein}
          onChange={handleChange}
          placeholder="XXXXXXXXX"
        />
        {errors.ein && <p className={styles.errorText}>{errors.ein}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="entityType">Entity Type <sup>*</sup></label>
        {/* Consider making this a select dropdown for predefined entity types */}
        <input
          type="text"
          id="entityType"
          name="entityType"
          value={formData.entityType}
          onChange={handleChange}
          placeholder="e.g., LLP, Corp, LLC"
        />
        {errors.entityType && <p className={styles.errorText}>{errors.entityType}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="dateOfIncorporation">Date of Incorporation <sup>*</sup></label>
        <input
          type="date"
          id="dateOfIncorporation"
          name="dateOfIncorporation"
          value={formData.dateOfIncorporation}
          onChange={handleChange}
        />
        {errors.dateOfIncorporation && <p className={styles.errorText}>{errors.dateOfIncorporation}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="irsAssisgnedFederalFilingForm">IRS Assigned Federal Filing Form <sup>*</sup></label>
        {/* Consider making this a select dropdown if there's a fixed list (e.g., 941, 944) */}
        <input
          type="text"
          id="irsAssisgnedFederalFilingForm"
          name="irsAssisgnedFederalFilingForm"
          value={formData.irsAssisgnedFederalFilingForm}
          onChange={handleChange}
          placeholder="e.g., 941"
        />
        {errors.irsAssisgnedFederalFilingForm && <p className={styles.errorText}>{errors.irsAssisgnedFederalFilingForm}</p>}
      </div>

      <div className={styles.buttonContainer}>
        <button type="button" onClick={onBack} className={styles.secondary}>Back</button>
        <button type="submit" className={styles.primary}>Next</button>
      </div>
    </form>
  );
};

export default KybForm;

import { getLastMessage, getLastUserMessage, getLastAssistantMessage } from '../utils/chatUtils';

/**
 * Example of how to extract the last message from a chat conversation
 */
function extractLastMessageExample() {
  // Sample chat messages (this would typically come from your chat state or API)
  const messages = [
    { role: 'system', content: 'You are a helpful assistant.' },
    { role: 'user', content: 'Hello, how are you?' },
    { role: 'assistant', content: 'I\'m doing well, thank you! How can I help you today?' },
    { role: 'user', content: 'I need to add a new employee.' },
    { role: 'assistant', content: 'I\'ll help you add a new employee. What is the employee\'s first name?' }
  ];

  // Extract the last message (regardless of role)
  const lastMessage = getLastMessage(messages);
  console.log('Last message:', lastMessage);
  // Output: "I'll help you add a new employee. What is the employee's first name?"

  // Extract the last user message
  const lastUserMessage = getLastUserMessage(messages);
  console.log('Last user message:', lastUserMessage);
  // Output: "I need to add a new employee."

  // Extract the last assistant message
  const lastAssistantMessage = getLastAssistantMessage(messages);
  console.log('Last assistant message:', lastAssistantMessage);
  // Output: "I'll help you add a new employee. What is the employee's first name?"
}

// Run the example
extractLastMessageExample();

/**
 * How to use in your components:
 * 
 * 1. Import the utility functions:
 *    import { getLastMessage } from '../utils/chatUtils';
 * 
 * 2. Use in your component:
 *    const messages = [...]; // Your chat messages array
 *    const lastMessage = getLastMessage(messages);
 * 
 * 3. Use the extracted message as needed:
 *    console.log(lastMessage);
 *    // or process it further
 */

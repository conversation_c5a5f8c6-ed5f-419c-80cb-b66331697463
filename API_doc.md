# Rollfi API Documentation

This document outlines the routes and information gathered from the Rollfi API documentation.

## Part 1: Documentation Overview (from `https://developer.rollfi.xyz/docs`)

The following sections were identified on the main documentation page.

### Overview: Payroll API Documentation
The page is titled "Payroll API Documentation".

### Introduction
Welcome to the comprehensive documentation for our suite of Payroll APIs. This set of tools is designed to empower developers, payroll administrators, and business owners alike, by providing a seamless and efficient interface to manage all aspects of a company's payroll system. From the onboarding of companies and their employees to the streamlined handling of payroll calculations and reporting, this suite of APIs is purpose-built to be robust, secure, and easy to integrate.
*(User requested: Introduction)*

### 1. Company Onboarding
This section likely covers the initial setup and onboarding process for companies. (Detailed content was not fully captured from the overview page, expected in API reference).
*(User requested: Customer Onboarding that is company)*

### 2. Employee Onboarding
Here you will find comprehensive information on the APIs used to onboard new employees, including personal details and tax information.
*(User requested: Employee Onboarding)*

### 3. Employer Portal
This section outlines the functionality available to employers through the API. It includes features such as managing employee information, setting up company bank account, employee wage and pay schedules.

### 4. Employee Portal
This section likely outlines functionality available to employees through the API. (Detailed content was not fully captured from the overview page).

### 5. Payroll
This core section dives deep into the APIs responsible for the actual payroll process. It covers everything from initiate payroll, through to running payroll, making adjustments, and resolving any issues that may arise.

### 6. Webhooks
This section introduces our webhook system, which allows for real-time data transmission between our Payroll API and your application or service. Learn how to set up, manage, and troubleshoot your webhooks for optimal synchronization.

### Note on "Authentication"
The "Authentication" section, as requested by the user for `https://developer.rollfi.xyz/docs`, was not found as a distinct top-level heading on this page during the review on 2025-05-22. Information regarding authentication may be present within other sections or will be detailed in the API Reference.
*(User requested: Authentication)*

---

## Part 2: API Reference (from `https://developer.rollfi.xyz/api-reference/`)

*(User requested sections: Company Onboarding, User Onboarding, Admin Portal, User Portal, Payroll, Benefits, Reports, Webhooks)*

### User Onboarding

*(Navigated to `https://developer.rollfi.xyz/api-reference/useronboarding/` which landed on `addKycInformation`)*

#### `addKycInformation`
*   **Endpoint:** `POST /userOnboarding#addKycInformation` (Note: The URL in cURL example is `https://sandbox.rollfi.xyz/userOnboarding#addKycInformation`)
*   **Description:** Use the below endpoint to Add Kyc information if it satisfies the condition - The customer operates from the company's registered location.
*   **Request Body (application/json):**
    *   `method`: string, required. Example: `"addKycInformation"`
    *   `kycInformation`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable)
    *   `employeeRemoteLocation`: object. (Child attributes to be detailed if "Show child attributes" was clickable)
*   **cURL Example (Request):**
    ```bash
    curl --request POST \
      --url 'https://sandbox.rollfi.xyz/userOnboarding#addKycInformation' \
      --header 'Content-Type: application/json' \
      --data '{
        "method": "addKycInformation",
        # ... (rest of kycInformation and userRemoteLocation object details)
      }'
    ```
*   **Response Example (200 OK):**
    ```json
    {
      "kycInformation": {
        "userId": "5a2398e1-0f83-46bd-ae1c-81b38fe364e1",
        "status": "KYC Pending",
        "message": "The KYC Information for jacob king has been added successfully."
        // ... (potentially more fields)
      }
    }
    ```
    *(Other status codes like 400 also shown with example responses)*

### Admin Portal

*(Navigated to `https://developer.rollfi.xyz/api-reference/adminportal/` which landed on `addCompanyBankAccount`)*

#### `addCompanyBankAccount`
*   **Endpoint:** `POST /adminPortal#addCompanyBankAccount` (Note: The URL in cURL example is `https://sandbox.rollfi.xyz/adminPortal#addCompanyBankAccount`)
*   **Description:** This API is used to add the Company Bank Account Details.
*   **Request Body (application/json):**
    *   `method`: string, required. Example: (Not fully visible, assumed `"addCompanyBankAccount"`)
    *   `companyFundingSourceEntity`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable)
*   **cURL Example (Request):**
    ```bash
    curl --request POST \
      --url 'https://sandbox.rollfi.xyz/adminPortal#addCompanyBankAccount' \
      --header 'Content-Type: application/json' \
      --data '{
        "method": "addCompanyBankAccount",
        "companyFundingSourceEntity": {
          # ... (object details)
        }
      }'
    ```
*   **Response Example (200 OK):**
    ```json
    {
      "companyFundingSourceEntity": {
        "companyFundingSourceEntityId": "5E56316D-5226-47CF-BDC8-994EDC81CBB7",
        "status": "Ready",
        "message": "The Company's bank account has been added successfully."
      }
    }
    ```
    *(Other status codes like 400 also shown with example responses)*
*   **Response Body (application/json):**
    *   `companyFundingSourceEntity`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable)

*(Page also showed navigation links to `< updateW4Information` and `deactivateCompanyBankAccount >` suggesting other endpoints in this section.)*

### User Portal

*(Navigated to `https://developer.rollfi.xyz/api-reference/userportal/` which landed on `addUserBankAccount`)*

#### `addUserBankAccount`
*   **Endpoint:** `POST /userPortal#addUserBankAccount` (Note: The URL in cURL example is `https://sandbox.rollfi.xyz/userPortal#addUserBankAccount`)
*   **Description:** In order to save the Company Funding Source Entity this Endpoint is used It comprises of information related to bank to process payroll.
*   **Request Body (application/json):**
    *   `method`: string, required. Example: `"addUserBankAccount"`
    *   `userPayAccountEntity`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable)
*   **cURL Example (Request):**
    ```bash
    curl --request POST \
      --url 'https://sandbox.rollfi.xyz/userPortal#addUserBankAccount' \
      --header 'Content-Type: application/json' \
      --data '{
        "method": "addUserBankAccount",
        "userPayAccountEntity": {
          # ... (object details, e.g. bankName, accountType, accountName, payPercentage)
        }
      }'
    ```
*   **Response Example (200 OK):**
    ```json
    {
      "userPayAccountEntity": {
        "userPayAccountEntityId": "8BE7466C-3785-4437-8FEE-7D08582784BD",
        "status": "Ready",
        "message": "The User PayAccount Entity has been added successfully."
      }
    }
    ```
    *(Other status codes like 400 also shown with example responses)*
*   **Response Body (application/json):**
    *   `userPayAccountEntity`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable)

### Payroll

*(Navigated to `https://developer.rollfi.xyz/api-reference/payroll/` which landed on `addPaySchedule`)*

#### `addPaySchedule`
*   **Endpoint:** `POST /payroll#addPaySchedule` (Note: The URL in cURL example is not fully visible but likely `https://sandbox.rollfi.xyz/payroll#addPaySchedule`)
*   **Title:** Add Pay Schedule Endpoint
*   **Description:** Use this endpoint to create a new Pay Schedule for the company's employees. When CompensationFrequency is Monthly/Semi - Monthly please use this link to Update the PayBeginDate `https://docs.google.com/spreadsheets/d/12LWp9jWqiovMQf18XaWPPWv_oOFvbZAsLHKSP3Q74pQ/` (link from documentation).
*   **Request Body (application/json):**
    *   `method`: string, required. Example: `"addPaySchedule"`
    *   `paySchedule`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable, e.g. `payBeginDate`, `standardWorkingHours`, `workerType`, `compensationFrequency`, `option`, `paymentMode`)
*   **cURL Example (Request):**
    ```bash
    curl --request POST \
      # --url '(likely https://sandbox.rollfi.xyz/payroll#addPaySchedule)' \
      --header 'Content-Type: application/json' \
      --data '{
        "method": "addPaySchedule",
        "paySchedule": {
          "payBeginDate": "2023-12-09",
          "standardWorkingHours": 8,
          "workerType": "1099-NEC",
          "compensationFrequency": "BiWeekly",
          "option": 1,
          "paymentMode": "Automatic"
          # ... (potentially more fields)
        }
      }'
    ```
*   **Response Example (200 OK):**
    ```json
    {
      "paySchedule": {
        "payScheduleId": "6BCD7075-0E02-4C62-B50C-74267FB2E462",
        "status": "New",
        "message": "PayPeriod for Prism has been added successfully"
      }
    }
    ```
    *(Other status codes like 400 also shown with example responses)*
*   **Response Body (application/json):**
    *   `paySchedule`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable)

### Benefits

*(Navigated to `https://developer.rollfi.xyz/api-reference/benefits/` which landed on `addEmployeeBenefits`)*

#### `addEmployeeBenefits`
*   **Endpoint:** `POST /benefits#addEmployeeBenefits` (Note: The URL in cURL example is `https://sandbox.rollfi.xyz/benefits#addEmployeeBenefits`)
*   **Description:** This endpoint is used to add employee benefits.
*   **Request Body (application/json):**
    *   `method`: string, required. Example: `"addEmployeeBenefits"` (default was `addUserBankAccount` in screenshot, likely a copy-paste error in docs, corrected here based on endpoint name)
    *   `benefitEmployeeElection`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable)
*   **cURL Example (Request):**
    ```bash
    curl --request POST \
      --url 'https://sandbox.rollfi.xyz/benefits#addEmployeeBenefits' \
      --header 'Content-Type: application/json' \
      --data '{
        "method": "addEmployeeBenefits",
        "benefitEmployeeElection": {
          # ... (object details)
        }
      }'
    ```
*   **Response Example (200 OK):**
    ```json
    {
      "Benefits": {
        "status": "Ready",
        "message": "The Benefits for Medical has been saved successfully.",
        "benefitType": "Medical"
      }
    }
    ```
    *(Other status codes like 400 also shown with example responses)*
*   **Response Body (application/json):**
    *   `Benefits`: object, required. (Child attributes to be detailed if "Show child attributes" was clickable)

*(Page also showed navigation links to `< addSupplementalPayrollLineItem` and `updateEmployeeBenefits >` suggesting other endpoints in this section.)*

---
*(Further content from API Reference to be added below)*

import api from './apiConfig';
import { utf8ToBase64 } from '../utils/base64';
import { adminService, CreateBusinessPayload } from './adminService';
import { getBasicAuthHeader } from '../utils/auth';

// Re-export CreateBusinessPayload as CompanyData for backward compatibility
export type CompanyData = CreateBusinessPayload;

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface EmployerProfile {
  companyName: string;
  ein: string;
  address: string;
  phone: string;
  email: string;
}

export interface PayrollInfo {
  totalPayroll: number;
  nextPayrollDate: string;
  pendingPayments: number;
  completedPayments: number;
}

export interface BankBalance {
  balance: number;
  currency: string;
  lastUpdated: string;
}

export const employerService = {
  // Employee Management
  getAllEmployees: async () => {
    try {
      const selectedCompanyStr = localStorage.getItem('selectedCompany');
      if (!selectedCompanyStr) {
        console.error('Error fetching employees: No company selected');
        return { data: [] };
      }
      const selectedCompany = JSON.parse(selectedCompanyStr);
      const companyId = selectedCompany.companyID;
      if (!companyId) {
        console.error('Error fetching employees: Invalid company ID');
        return { data: [] };
      }
      // Use Basic Auth for /reports#getUsers
      const basicAuth = getBasicAuthHeader();
      const response = await fetch('https://sandbox.rollfi.xyz/reports#getUsers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': basicAuth
        },
        body: JSON.stringify({
          method: 'getUsers',
          companyId: companyId
        })
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      const result = await response.json();
      console.log('Employees fetched successfully:', result);
      return result;
    } catch (error) {
      console.error('Error fetching employees:', error);
      return { data: [] };
    }
  },
  

  
  async addEmployee(employeeData: any): Promise<any> {
    console.log('🔍 employerService.addEmployee called with data:', JSON.stringify(employeeData, null, 2));
    
    try {
      const selectedCompanyStr = localStorage.getItem('selectedCompany');
      if (!selectedCompanyStr) {
        throw new Error('No company selected');
      }
      const selectedCompany = JSON.parse(selectedCompanyStr);
      const companyId = selectedCompany.companyID;
      
      if (!companyId) {
        throw new Error('Invalid company ID');
      }
      
      // Helper to convert dd-mm-yyyy to YYYY-MM-DD
      const convertDateToYYYYMMDD = (dateStr: string | undefined): string => {
        if (!dateStr || typeof dateStr !== 'string') return "";
        const parts = dateStr.split('-');
        // Check if it's dd-mm-yyyy
        if (parts.length === 3 && parts[0].length === 2 && parts[1].length === 2 && parts[2].length === 4) {
          return `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
        // If already YYYY-MM-DD or other, return as is (or add more specific validation)
        return dateStr;
      };

      // Prepare Basic Auth credentials
      const credentials = `${import.meta.env.VITE_CLIENT_ID}:${import.meta.env.VITE_CLIENT_SECRET}`;
      const basicAuth = utf8ToBase64(credentials);

      // Construct userData payload mapping form fields to API fields
      const userData = {
        companyId: companyId,
        userReferenceId: employeeData.employeeId || employeeData.userReferenceId || "", // Mapped from form's "Employee ID" or if directly provided
        firstName: employeeData.firstName,
        middleName: employeeData.middleName || "",
        lastName: employeeData.lastName,
        email: employeeData.businessEmail || employeeData.email, // Mapped from form's "Business Email"
        phoneNumber: employeeData.phoneNumber?.replace(/\D/g, '') || '',
        dateOfJoin: convertDateToYYYYMMDD(employeeData.startDate || employeeData.dateOfJoin), // Mapped from "Start Date" & converted
        workerType: (typeof employeeData.workerType === 'object' && employeeData.workerType !== null && employeeData.workerType.workerType) ? employeeData.workerType.workerType : employeeData.workerType,
        jobTitle: employeeData.jobTitle, // Mapped from form's "Job Title"
        companyLocationCategory: (typeof employeeData.companyLocationCategory === 'object' && employeeData.companyLocationCategory !== null && employeeData.companyLocationCategory.companyLocationCategory) ? employeeData.companyLocationCategory.companyLocationCategory : (employeeData.workLocation || employeeData.companyLocationCategory),
        stateCode: employeeData.stateCode?.toUpperCase(), 
        companyLocationId: employeeData.companyLocationId || ""
      };
      
      console.log('📤 Sending API request to add employee with payload:', JSON.stringify({ method: 'addUser', user: userData }, null, 2));
      
      const response = await fetch('https://sandbox.rollfi.xyz/adminPortal#addUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${basicAuth}`
        },
        body: JSON.stringify({
          method: 'addUser',
          user: userData
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API error response text:', errorText);
        let apiErrorMessage = `API error: ${response.status} ${response.statusText}`;
        try {
          const errorJson = JSON.parse(errorText);
          // Append more specific error message if available in JSON response
          if (errorJson && errorJson.message) {
            apiErrorMessage += ` - ${errorJson.message}`;
          } else if (errorJson && errorJson.error && errorJson.error.message) {
            apiErrorMessage += ` - ${errorJson.error.message}`;
          } else {
            apiErrorMessage += ` - ${errorText}`; // Fallback to full error text if no specific message field
          }
        } catch (e) {
          // If errorText is not JSON, append it directly
          apiErrorMessage += ` - ${errorText}`;
        }
        throw new Error(apiErrorMessage);
      }
      
      const result = await response.json();
      console.log('✅ API success response:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('❌ Error in employerService.addEmployee:', error);
      throw error;
    }
  },
  
  updateEmployee: (employeeId: string, data: any) => {
    // Encode name fields to prevent btoa encoding issues
    if (data.firstName) {
      data.firstName = utf8ToBase64(data.firstName);
    }
    if (data.middleName) {
      data.middleName = utf8ToBase64(data.middleName);
    }
    if (data.lastName) {
      data.lastName = utf8ToBase64(data.lastName);
    }
    
    console.log('Encoding employee update data with utf8ToBase64 to prevent btoa issues');
    
    return api.put(`/employer/employees/${employeeId}`, data);
  },


  
  addContractor: (contractorData: any) => {
    // Encode name fields to prevent btoa encoding issues
    if (contractorData.firstName) {
      contractorData.firstName = utf8ToBase64(contractorData.firstName);
    }
    if (contractorData.middleName) {
      contractorData.middleName = utf8ToBase64(contractorData.middleName);
    }
    if (contractorData.lastName) {
      contractorData.lastName = utf8ToBase64(contractorData.lastName);
    }
    
    console.log('Encoding contractor data with utf8ToBase64 to prevent btoa issues');
    
    return api.post('/employer/contractors', contractorData);
  },

  // Payroll
  runPayroll: (payrollData: any) => 
    api.post('/employer/payroll/run', payrollData),
  
  getPayrollHistory: () => 
    api.get('/employer/payroll/history'),

  // Company Settings
  getCompanyProfile: () => 
    api.get<ApiResponse<EmployerProfile>>('/employer/company'),
    
  addCompany: async (companyData: CreateBusinessPayload) => {
    try {
      console.log('employerService.addCompany called with data:', JSON.stringify(companyData, null, 2));
      const result = await adminService.createBusiness(companyData);
      return result;
    } catch (error) {
      console.error('Error in employerService.addCompany:', error);
      throw error;
    }
  },
  
  updateCompanyProfile: (data: Partial<EmployerProfile>) => {
    // Encode company name to prevent btoa encoding issues
    if (data.companyName) {
      data.companyName = utf8ToBase64(data.companyName);
    }
    
    console.log('Encoding company profile data with utf8ToBase64 to prevent btoa issues');
    
    return api.put('/employer/company', data);
  },

  // Benefits Administration
  getBenefitsPlans: () => 
    api.get('/employer/benefits'),
  
  updateBenefitsPlan: (planId: string, data: any) => 
    api.put(`/employer/benefits/${planId}`, data),

  // Tax Documents
  getTaxDocuments: () => 
    api.get('/employer/tax-documents'),
  
  generateW2s: (year: number) => 
    api.post('/employer/tax-documents/w2', { year }),
  
  generate1099s: (year: number) => 
    api.post('/employer/tax-documents/1099', { year }),
    

    
  // Banking
  getUserDetails: async (userId: string, companyId: string) => {
    try {
      console.log(`Fetching user details for userId: ${userId}, companyId: ${companyId}`);
      const response = await api.post('/reports#getUser', {
        method: 'getUser',
        userId: userId.toUpperCase(),
        companyId: companyId.toUpperCase()
      });
      // The API returns user details in an array, even if it's a single user
      if (response.data && response.data.user && Array.isArray(response.data.user) && response.data.user.length > 0) {
        return response.data.user[0];
      }
      console.warn('User not found or unexpected response structure:', response.data);
      return null;
    } catch (error) {
      console.error('Error fetching user details:', error);
      return null;
    }
  },

  /**
   * Fetches employer tasks from Rollfi API for the given userId.
   * @param userId The employer's userId (UUID string)
   * @returns Promise resolving to the task array or API error
   */
  async getUserTask(userId: string): Promise<any> {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('Authentication token not found');
      }
      const response = await fetch('https://sandbox.rollfi.xyz/reports#getUserTask', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          method: 'getUserTask',
          userId: userId.toUpperCase()
        })
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
      }
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('❌ Error in employerService.getUserTask:', error);
      throw error;
    }
  },

};

export const getUserDetails = employerService.getUserDetails;

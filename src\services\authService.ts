import api from '../services/apiConfig';
import { createClient } from '@supabase/supabase-js';
import type { ApiResponse } from './types';
import { UserProfile } from '../types';

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

export const authService = {
  sendOtp: async (email: string) => {
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    });
    
    if (error) throw error;
  },

  verifyOtp: async (email: string, token: string) => {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'email'
    });

    if (error) throw error;

    if (data.session) {
      localStorage.setItem('token', data.session.access_token);
      localStorage.setItem('auth_token', data.session.access_token);
    }

    return data;
  },

  getUserProfile: async (email: string): Promise<UserProfile | null> => {
    const { data, error } = await supabase
      .from('temp_website2_users')
      .select('*')
      .eq('email', email)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }

    return data;
  },

  logout: async () => {
    console.log('authService.logout: Clearing localStorage...');
    localStorage.removeItem('token');
    localStorage.removeItem('auth_token');
    localStorage.removeItem('userProfile');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('currentRole');
    localStorage.removeItem('userRoles');
    localStorage.removeItem('selectedCompany');
    console.log('authService.logout: Calling supabase.auth.signOut()...');
    const result = await supabase.auth.signOut();
    console.log('authService.logout: supabase.auth.signOut() result:', result);
    return result;
  },

  refreshToken: async () => {
    const { data, error } = await supabase.auth.refreshSession();
    if (error) throw error;
    return data;
  },

  forgotPassword: (email: string) => 
    api.post('/auth/forgot-password', { email }),

  resetPassword: (token: string, newPassword: string) => 
    api.post('/auth/reset-password', { token, newPassword })
};









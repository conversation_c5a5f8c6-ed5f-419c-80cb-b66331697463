.addCompanyPageContainer {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pageTitle {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 600;
}

.formContainer {
  background-color: #fff;
}

.stepTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.errorText {
  color: #dc3545; /* Bootstrap's danger color */
  font-size: 0.875em;
  margin-top: 0.25rem;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.errorMessage {
  background-color: #ffebee; /* Light red */
  color: #c62828; /* Dark red */
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  border: 1px solid #ef9a9a; /* Lighter red border */
  text-align: center;
}

.reviewSection {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fdfdfd;
}

.reviewSectionTitle {
  font-size: 1.2rem;
  font-weight: 500;
  color: #007bff;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed #ccc;
}

.reviewSection p {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  color: #555;
}

.reviewSection p strong {
  color: #333;
  margin-right: 5px;
}


/* Basic styles for form elements that might be shared across steps */
.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.formGroup label sup {
  color: red;
  margin-left: 2px;
}

.formGroup input[type="text"],
.formGroup input[type="email"],
.formGroup input[type="tel"],
.formGroup input[type="number"],
.formGroup input[type="date"],
.formGroup select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1rem;
}

.formGroup input[type="text"]:focus,
.formGroup input[type="email"]:focus,
.formGroup input[type="tel"]:focus,
.formGroup input[type="number"]:focus,
.formGroup input[type="date"]:focus,
.formGroup select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.formGroup input[type="checkbox"] {
  margin-right: 0.5rem;
}

.buttonContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.buttonContainer button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease-in-out;
}

.buttonContainer button.primary {
  background-color: #007bff;
  color: white;
}

.buttonContainer button.primary:hover {
  background-color: #0056b3;
}

.buttonContainer button.secondary {
  background-color: #6c757d;
  color: white;
}

.buttonContainer button.secondary:hover {
  background-color: #545b62;
}

.buttonContainer button:disabled {
  background-color: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}
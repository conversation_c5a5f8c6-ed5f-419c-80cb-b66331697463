import { supabase } from '../lib/supabaseClient';

export const sendOtpToEmail = async (email: string) => {
  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      emailRedirectTo: `${window.location.origin}/auth/callback`,
    }
  });
  
  if (error) throw error;
  return true;
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
  return true;
};

export const getCurrentUser = async () => {
  const { data, error } = await supabase.auth.getUser();
  if (error) throw error;
  return data?.user || null;
};

export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('temp_website2_users')
    .select('*')
    .eq('user_id', userId)
    .maybeSingle();
  
  if (error) throw error;
  return data;
};

export const createUserProfile = async (userData: {
  user_id: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  phone: string;
  bussiness_email: string;
}) => {
  const { data, error } = await supabase
    .from('temp_website2_users')
    .insert([userData])
    .select();
  
  if (error) throw error;
  return data;
};
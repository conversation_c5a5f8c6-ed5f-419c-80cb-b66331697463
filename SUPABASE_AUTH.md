# Supabase Authentication Implementation

This document outlines how to use the Supabase authentication system implemented in this project.

## Setup

1. Create a `.env` file based on `.env.example` and add your Supabase credentials:
   ```
   VITE_SUPABASE_URL=your-supabase-url
   VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

2. Make sure your Supabase project has the following table structure:
   ```sql
   create table public.website2_users (
     id uuid not null default extensions.uuid_generate_v4 (),
     user_id uuid not null,
     first_name text not null,
     phone numeric not null,
     created_at timestamp with time zone null default now(),
     middle_name text null,
     last_name text not null,
     bussiness_email character varying not null,
     constraint website2_users_pkey primary key (id),
     constraint website2_users_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
   ) TABLESPACE pg_default;
   ```

3. Configure your Supabase authentication settings:
   - Enable Email OTP (Magic Link) authentication in your Supabase dashboard
   - Set up the redirect URL to `https://your-website.com/auth/callback`

## Authentication Flow

1. **Login**: Users enter their email on the login page (`/login`)
2. **Magic Link**: A magic link is sent to their email
3. **Callback**: When users click the link, they're redirected to `/auth/callback`
4. **User Check**: The system checks if the user exists in the `website2_users` table
   - If yes: Redirected to `/admin`
   - If no: Redirected to `/signup` to complete their profile
5. **Signup**: New users fill out the signup form with their details
6. **Registration**: After submitting the form, a magic link is sent to their email
7. **Completion**: When they click the link, their profile is created in the `website2_users` table

## Files Overview

- `lib/supabaseClient.ts` - Supabase client initialization
- `services/authService.ts` - Authentication service with Supabase methods
- `pages/LoginPage.tsx` - Login page with Magic Link
- `pages/SignupPage.tsx` - Signup page for new users
- `pages/AuthCallback.tsx` - Authentication callback handler

## Implementation Details

### Login Page
The login page has been updated to use Supabase authentication with magic links. When a user enters their email, a magic link is sent to their email address. After clicking the link, they are redirected to the callback page which checks if they have a profile and redirects accordingly.

### Signup Page
The signup page collects user information (first name, middle name, last name, email, phone) and sends a magic link to complete registration. The user data is temporarily stored in localStorage until the user clicks the magic link.

### Auth Callback
The auth callback page handles the authentication process after the user clicks the magic link. It checks if there's pending user data in localStorage and creates a profile in the `website2_users` table if needed. Then it redirects the user to the appropriate page.

## Usage

### Login Process
1. User navigates to `/login`
2. User enters their email
3. A magic link is sent to their email
4. User clicks the link in their email
5. System checks if user has a profile
   - If yes: Redirected to dashboard
   - If no: Redirected to signup

### Signup Process
1. User navigates to `/signup`
2. User fills out the form with their details
3. User submits the form
4. A magic link is sent to their email
5. User clicks the link in their email
6. System creates a profile for the user
7. User is redirected to dashboard

## Testing

To test the authentication flow:

1. Start the development server with `npm run dev`
2. Navigate to `/signup` to create a new account
3. Fill out the form and submit
4. Check your email for the magic link
5. Click the link to complete registration
6. You should be redirected to the admin page

For existing users:

1. Navigate to `/login`
2. Enter your email
3. Enter the OTP sent to your email
4. You should be redirected to the admin page
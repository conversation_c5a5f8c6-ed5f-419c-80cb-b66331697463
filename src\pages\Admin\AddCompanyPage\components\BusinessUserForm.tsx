import React, { useState, ChangeEvent, FormEvent } from 'react';
import styles from '../AddCompanyPage.module.css';

interface BusinessUserData {
  firstName: string;
  middleName?: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  address1: string;
  address2?: string;
  city: string;
  state: string; // State code e.g. CO
  zipcode: string;
  ssn: string; // Social Security Number
  dateOfBirth: string; // YYYY-MM-DD
  payrollAdmin: boolean;
  bookKeeper: boolean;
  beneficialOwner: boolean;
  ownershipPercentage: number | '';
}

interface BusinessUserFormProps {
  data: BusinessUserData;
  onNext: (data: { businessUser: BusinessUserData }) => void;
  onBack: () => void;
}

const BusinessUserForm: React.FC<BusinessUserFormProps> = ({ data, onNext, onBack }) => {
  const [formData, setFormData] = useState<BusinessUserData>(data);
  const [errors, setErrors] = useState<Partial<Record<keyof BusinessUserData, string>>>({});

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else if (name === 'ownershipPercentage') {
      setFormData((prev) => ({ ...prev, [name]: value === '' ? '' : Number(value) }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
    if (errors[name as keyof BusinessUserData]) {
      setErrors(prev => ({...prev, [name]: undefined}));
    }
  };

  const validate = (): boolean => {
    const newErrors: Partial<Record<keyof BusinessUserData, string>> = {};
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required.';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required.';
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required.';
    } else if (!/^\d{10}$/.test(formData.phoneNumber.replace(/\D/g, ''))) {
      newErrors.phoneNumber = 'Enter a valid 10-digit phone number.';
    }
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required.';
    } else if (!/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(formData.email)) {
      newErrors.email = 'Enter a valid email address.';
    }
    if (!formData.address1.trim()) newErrors.address1 = 'Address Line 1 is required.';
    if (!formData.city.trim()) newErrors.city = 'City is required.';
    if (!formData.state.trim()) {
      newErrors.state = 'State is required.';
    } else if (!/^[A-Z]{2}$/.test(formData.state.toUpperCase())) {
      newErrors.state = 'State must be a 2-letter code (e.g., CO).';
    }
    if (!formData.zipcode.trim()) {
      newErrors.zipcode = 'Zipcode is required.';
    } else if (!/^\d{5}(-\d{4})?$/.test(formData.zipcode)) {
      newErrors.zipcode = 'Enter a valid 5-digit or 9-digit zipcode.';
    }
    if (!formData.ssn.trim()) {
      newErrors.ssn = 'SSN is required.';
    } else if (!/^\d{9}$/.test(formData.ssn)) {
      newErrors.ssn = 'SSN must be a 9-digit number.';
    }
    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required.';
    } else {
      const datePattern = /^\d{4}-\d{2}-\d{2}$/;
      if (!datePattern.test(formData.dateOfBirth)) {
        newErrors.dateOfBirth = 'Date must be in YYYY-MM-DD format.';
      } else {
        const date = new Date(formData.dateOfBirth);
        const [year, month, day] = formData.dateOfBirth.split('-').map(Number);
        if (!(date.getFullYear() === year && date.getMonth() + 1 === month && date.getDate() === day)) {
          newErrors.dateOfBirth = 'Invalid date.';
        }
      }
    }
    if (formData.ownershipPercentage === '' || formData.ownershipPercentage < 0 || formData.ownershipPercentage > 100) {
      newErrors.ownershipPercentage = 'Ownership percentage must be between 0 and 100.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    const dataToSubmit = { ...formData, state: formData.state.toUpperCase() };
    if (validate()) {
      onNext({ businessUser: dataToSubmit });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <h2 className={styles.stepTitle}>Step 4: Business User Information</h2>

      <div className={styles.formGroup}>
        <label htmlFor="firstName">First Name <sup>*</sup></label>
        <input type="text" id="firstName" name="firstName" value={formData.firstName} onChange={handleChange} />
        {errors.firstName && <p className={styles.errorText}>{errors.firstName}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="middleName">Middle Name</label>
        <input type="text" id="middleName" name="middleName" value={formData.middleName || ''} onChange={handleChange} />
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="lastName">Last Name <sup>*</sup></label>
        <input type="text" id="lastName" name="lastName" value={formData.lastName} onChange={handleChange} />
        {errors.lastName && <p className={styles.errorText}>{errors.lastName}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="userPhoneNumber">Phone Number <sup>*</sup></label>
        <input type="tel" id="userPhoneNumber" name="phoneNumber" value={formData.phoneNumber} onChange={handleChange} placeholder="9874563210" />
        {errors.phoneNumber && <p className={styles.errorText}>{errors.phoneNumber}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="userEmail">Email <sup>*</sup></label>
        <input type="email" id="userEmail" name="email" value={formData.email} onChange={handleChange} placeholder="<EMAIL>" />
        {errors.email && <p className={styles.errorText}>{errors.email}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="userAddress1">Address Line 1 <sup>*</sup></label>
        <input type="text" id="userAddress1" name="address1" value={formData.address1} onChange={handleChange} />
        {errors.address1 && <p className={styles.errorText}>{errors.address1}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="userAddress2">Address Line 2</label>
        <input type="text" id="userAddress2" name="address2" value={formData.address2 || ''} onChange={handleChange} />
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="userCity">City <sup>*</sup></label>
        <input type="text" id="userCity" name="city" value={formData.city} onChange={handleChange} />
        {errors.city && <p className={styles.errorText}>{errors.city}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="userState">State (2-letter code) <sup>*</sup></label>
        <input type="text" id="userState" name="state" value={formData.state} onChange={handleChange} maxLength={2} placeholder="e.g., CO" />
        {errors.state && <p className={styles.errorText}>{errors.state}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="userZipcode">Zipcode <sup>*</sup></label>
        <input type="text" id="userZipcode" name="zipcode" value={formData.zipcode} onChange={handleChange} placeholder="e.g., 80540" />
        {errors.zipcode && <p className={styles.errorText}>{errors.zipcode}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="ssn">SSN (Social Security Number) <sup>*</sup></label>
        <input type="text" id="ssn" name="ssn" value={formData.ssn} onChange={handleChange} placeholder="XXXXXXXXX" />
        {errors.ssn && <p className={styles.errorText}>{errors.ssn}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="dateOfBirth">Date of Birth <sup>*</sup></label>
        <input type="date" id="dateOfBirth" name="dateOfBirth" value={formData.dateOfBirth} onChange={handleChange} />
        {errors.dateOfBirth && <p className={styles.errorText}>{errors.dateOfBirth}</p>}
      </div>
      
      <div className={styles.formGroup}>
        <label htmlFor="ownershipPercentage">Ownership Percentage (%)<sup>*</sup></label>
        <input type="number" id="ownershipPercentage" name="ownershipPercentage" value={formData.ownershipPercentage} onChange={handleChange} min="0" max="100" />
        {errors.ownershipPercentage && <p className={styles.errorText}>{errors.ownershipPercentage}</p>}
      </div>

      <div className={styles.formGroup}>
        <label><input type="checkbox" name="payrollAdmin" checked={formData.payrollAdmin} onChange={handleChange} /> Payroll Admin <sup>*</sup></label>
      </div>
      <div className={styles.formGroup}>
        <label><input type="checkbox" name="bookKeeper" checked={formData.bookKeeper} onChange={handleChange} /> Book Keeper <sup>*</sup></label>
      </div>
      <div className={styles.formGroup}>
        <label><input type="checkbox" name="beneficialOwner" checked={formData.beneficialOwner} onChange={handleChange} /> Beneficial Owner <sup>*</sup></label>
      </div>

      <div className={styles.buttonContainer}>
        <button type="button" onClick={onBack} className={styles.secondary}>Back</button>
        <button type="submit" className={styles.primary}>Next</button>
      </div>
    </form>
  );
};

export default BusinessUserForm;

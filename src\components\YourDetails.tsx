import React, { useState, useEffect } from 'react';
import { EmployeeDetails } from '../types';
import { useEmployeeViewStore } from '../store/employeeViewStore';

type DetailTab = 'personal' | 'direct-deposit' | 'w4' | 'address';

interface YourDetailsProps {
  employeeDetails: EmployeeDetails;
  // Add optional API employee details for when we have real data
  apiEmployeeDetails?: any;
}

// Helper function to format currency
const formatCurrency = (amount: number, basis: string): string => {
  return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}/${basis}`;
};

const YourDetails: React.FC<YourDetailsProps> = ({ employeeDetails, apiEmployeeDetails }) => {
  const [activeTab, setActiveTab] = useState<DetailTab>('personal');
  const { isAdminViewingAsEmployee } = useEmployeeViewStore();
  const [isAdmin, setIsAdmin] = useState(false);
  
  useEffect(() => {
    // Check if user is admin viewing as employee
    const storedRole = localStorage.getItem('currentRole');
    setIsAdmin(storedRole === 'admin-as-employee');
  }, []);

  // Get employee name from API data if available
  const employeeName = apiEmployeeDetails ? 
    `${apiEmployeeDetails.firstName || ''} ${apiEmployeeDetails.middleName || ''} ${apiEmployeeDetails.lastName || ''}`.trim() :
    employeeDetails.fullName;

  // Format date to MM/DD/YYYY
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' });
  };

  // Get wage information from API data
  const getWageInfo = () => {
    if (apiEmployeeDetails?.userWages && apiEmployeeDetails.userWages.length > 0) {
      const wage = apiEmployeeDetails.userWages[0];
      return {
        rate: wage.WageRate,
        basis: wage.WageBasis?.WageBasis || '',
        paymentMethod: wage.paymentMethod?.PaymentMethod || '',
        taxExempt: wage.employeeRefTaxExempt || '',
        employeeType: wage.employeeType || '',
        differentialPay: wage.differentialPay || '',
        employmentStatus: wage.employmentStatus || ''
      };
    }
    return null;
  };

  const wageInfo = getWageInfo();

  return (
    <div className="flex flex-col w-full">
      {/* Tab Navigation */}
      <div className="flex mb-6 flex-wrap">
        <button
          className={`py-3 px-6 rounded-t-md ${
            activeTab === 'personal' ? 'bg-teal-600 text-white' : 'bg-white text-gray-700'
          }`}
          onClick={() => setActiveTab('personal')}
        >
          Personal details
        </button>
        <button
          className={`py-3 px-6 ml-2 rounded-t-md ${
            activeTab === 'address' ? 'bg-teal-600 text-white' : 'bg-white text-gray-700'
          }`}
          onClick={() => setActiveTab('address')}
        >
          Address
        </button>
        <button
          className={`py-3 px-6 ml-2 rounded-t-md ${
            activeTab === 'direct-deposit' ? 'bg-teal-600 text-white' : 'bg-white text-gray-700'
          }`}
          onClick={() => setActiveTab('direct-deposit')}
        >
          Direct deposit
        </button>
        <button
          className={`py-3 px-6 ml-2 rounded-t-md ${
            activeTab === 'w4' ? 'bg-teal-600 text-white' : 'bg-white text-gray-700'
          }`}
          onClick={() => setActiveTab('w4')}
        >
          W4 information
        </button>
      </div>

      {/* Personal Details Content */}
      {activeTab === 'personal' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Job Details Section */}
          <div className="bg-white p-6 rounded-md shadow-sm">
            <h2 className="text-xl font-medium text-gray-700 mb-4">Employee Information</h2>
            
            <div className="space-y-4">
              {/* Basic Information */}
              <div>
                <h3 className="text-sm font-medium text-gray-500">Employee Name</h3>
                <p className="text-base text-gray-700">{employeeName}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Email</h3>
                <p className="text-base text-gray-700">{apiEmployeeDetails?.email || employeeDetails.workEmail || '--'}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Phone Number</h3>
                <p className="text-base text-gray-700">{apiEmployeeDetails?.phoneNumber || employeeDetails.businessPhone || '--'}</p>
              </div>
              
              {apiEmployeeDetails?.ssn && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">SSN</h3>
                  <p className="text-base text-gray-700">XXX-XX-{apiEmployeeDetails.ssn.slice(-4)}</p>
                </div>
              )}
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Status</h3>
                <p className="text-base text-gray-700">{apiEmployeeDetails?.status?.userStatus || '--'}</p>
              </div>
            </div>
          </div>

          {/* Job Details Section */}
          <div className="bg-white p-6 rounded-md shadow-sm">
            <h2 className="text-xl font-medium text-gray-700 mb-4">Job Details</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Worker Type</h3>
                <p className="text-base text-gray-700">{apiEmployeeDetails?.WorkerType?.WorkerType || '--'}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Job Title</h3>
                <p className="text-base text-gray-700">{apiEmployeeDetails?.jobTitle || employeeDetails.jobTitle || '--'}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Start Date</h3>
                <p className="text-base text-gray-700">{formatDate(apiEmployeeDetails?.dateOfJoin) || employeeDetails.startDate || '--'}</p>
              </div>
              
              {wageInfo && (
                <>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Compensation</h3>
                    <p className="text-base text-gray-700">
                      {formatCurrency(wageInfo.rate, wageInfo.basis)}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Employment Status</h3>
                    <p className="text-base text-gray-700">{wageInfo.employmentStatus || '--'}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Employee Type</h3>
                    <p className="text-base text-gray-700">{wageInfo.employeeType || '--'}</p>
                  </div>
                </>
              )}
            </div>
          </div>
          
          {/* Work Location Section */}
          <div className="bg-white p-6 rounded-md shadow-sm md:col-span-2">
            <h2 className="text-xl font-medium text-gray-700 mb-4">Work Location</h2>
            
            <div className="space-y-4">
              {apiEmployeeDetails?.companyLocation ? (
                <>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Location Name</h3>
                    <p className="text-base text-gray-700">{apiEmployeeDetails.companyLocation.companyLocation}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Location Address</h3>
                    <p className="text-base text-gray-700">{apiEmployeeDetails.companyLocation.address}</p>
                  </div>
                  
                  {apiEmployeeDetails.companyLocationCategory && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Location Type</h3>
                      <p className="text-base text-gray-700">{apiEmployeeDetails.companyLocationCategory.companyLocationCategory}</p>
                    </div>
                  )}
                </>
              ) : (
                <p className="text-sm text-gray-500">No work location specified</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Address Tab */}
      {activeTab === 'address' && (
        <div className="bg-white p-6 rounded-md shadow-sm">
          <h2 className="text-xl font-medium text-gray-700 mb-4">Address Information</h2>
          
          {apiEmployeeDetails?.userAddress ? (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Address Line 1</h3>
                <p className="text-base text-gray-700">{apiEmployeeDetails.userAddress.address1}</p>
              </div>
              
              {apiEmployeeDetails.userAddress.address2 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Address Line 2</h3>
                  <p className="text-base text-gray-700">{apiEmployeeDetails.userAddress.address2}</p>
                </div>
              )}
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">City</h3>
                  <p className="text-base text-gray-700">{apiEmployeeDetails.userAddress.city}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">State</h3>
                  <p className="text-base text-gray-700">{apiEmployeeDetails.userAddress.state}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Zip Code</h3>
                  <p className="text-base text-gray-700">{apiEmployeeDetails.userAddress.zipcode}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Country</h3>
                  <p className="text-base text-gray-700">{apiEmployeeDetails.userAddress.country}</p>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No address information available</p>
          )}
        </div>
      )}

      {/* Direct Deposit Content */}
      {activeTab === 'direct-deposit' && (
        <div className="bg-white p-6 rounded-md shadow-sm">
          <h2 className="text-xl font-medium text-gray-700 mb-4">Direct Deposit Information</h2>
          
          {wageInfo && wageInfo.paymentMethod ? (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Payment Method</h3>
                <p className="text-base text-gray-700">{wageInfo.paymentMethod}</p>
              </div>
              
              {wageInfo.paymentMethod === 'Direct Deposit' ? (
                <p className="text-gray-500">Bank account details are not displayed for security reasons.</p>
              ) : (
                <p className="text-gray-500">No direct deposit information available.</p>
              )}
            </div>
          ) : (
            <p className="text-gray-500">No payment method information available</p>
          )}
        </div>
      )}

      {/* W4 Information Content */}
      {activeTab === 'w4' && (
        <div className="bg-white p-6 rounded-md shadow-sm">
          <h2 className="text-xl font-medium text-gray-700 mb-4">W4 Information</h2>
          
          {wageInfo && wageInfo.taxExempt ? (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Tax Exempt Status</h3>
                <p className="text-base text-gray-700">{wageInfo.taxExempt}</p>
              </div>
              
              <p className="text-sm text-gray-500 mt-4">
                For detailed W4 information, please contact your payroll administrator.
              </p>
            </div>
          ) : (
            <p className="text-gray-500">No W4 information available</p>
          )}
        </div>
      )}
    </div>
  );
};

export default YourDetails;

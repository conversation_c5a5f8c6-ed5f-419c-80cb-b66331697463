import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface User {
  name: string;
  businessPhone: string;
  businessEmail: string;
  dateAdded: string;
  role: string;
}

interface NewUserForm {
  firstName: string;
  middleName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  roles: {
    beneficialOwner: boolean;
    bookkeeper: boolean;
    payrollAdmin: boolean;
  };
}

const EmployerSettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState<User[]>([
    {
      name: '<PERSON>',
      businessPhone: '(*************',
      businessEmail: '<EMAIL>',
      dateAdded: '05/22/2024',
      role: 'Beneficial Owner'
    }
  ]);

  const [activeTab, setActiveTab] = useState('Users');
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showBankAccountUI, setShowBankAccountUI] = useState(false);
  const [companyBankAccount, setCompanyBankAccount] = useState<any | null>(null);
  const [companyBankLoading, setCompanyBankLoading] = useState(false);
  const [companyBankError, setCompanyBankError] = useState<string | null>(null);
  const [newUserForm, setNewUserForm] = useState<NewUserForm>({
    firstName: '',
    middleName: '',
    lastName: '',
    phoneNumber: '',
    email: '',
    roles: {
      beneficialOwner: false,
      bookkeeper: false,
      payrollAdmin: false
    }
  });

  useEffect(() => {
    if (activeTab === 'Payment details') {
      const fetchBankAccount = async () => {
        setCompanyBankLoading(true);
        setCompanyBankError(null);
        try {
          const selectedCompanyStr = localStorage.getItem('selectedCompany');
          if (!selectedCompanyStr) throw new Error('No company selected');
          const selectedCompany = JSON.parse(selectedCompanyStr);
          const companyId = selectedCompany.companyID || selectedCompany.companyId || selectedCompany.id;
          if (!companyId) throw new Error('Invalid company ID');
          const credentials = `${import.meta.env.VITE_CLIENT_ID}:${import.meta.env.VITE_CLIENT_SECRET}`;
          const basicAuth = btoa(credentials);
          const response = await fetch('https://sandbox.rollfi.xyz/reports#getCompanyInfo', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Basic ${basicAuth}`,
            },
            body: JSON.stringify({ method: 'getCompanyInfo', companyId }),
          });
          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(errorText);
          }
          const result = await response.json();
          const bankAccounts = result.Company?.[0]?.BankAccounts || [];
          setCompanyBankAccount(bankAccounts.length > 0 ? bankAccounts[0] : null);
        } catch (err: any) {
          setCompanyBankError(err.message || 'Failed to fetch bank account.');
        } finally {
          setCompanyBankLoading(false);
        }
      };
      fetchBankAccount();
    }
  }, [activeTab, showBankAccountUI]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setNewUserForm(prev => ({
        ...prev,
        roles: {
          ...prev.roles,
          [name]: checked
        }
      }));
    } else {
      setNewUserForm(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleAddUser = () => {
    // Handle form submission here
    setShowAddUserModal(false);
    // Reset form
    setNewUserForm({
      firstName: '',
      middleName: '',
      lastName: '',
      phoneNumber: '',
      email: '',
      roles: {
        beneficialOwner: false,
        bookkeeper: false,
        payrollAdmin: false
      }
    });
  };

  const AddUserModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 w-[600px] max-h-[90vh] overflow-y-auto">
        <h2 className="text-2xl font-semibold mb-6">Add New User</h2>
        <form className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">First name</label>
            <input
              type="text"
              name="firstName"
              value={newUserForm.firstName}
              onChange={handleInputChange}
              placeholder="Enter first name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Middle name</label>
            <input
              type="text"
              name="middleName"
              value={newUserForm.middleName}
              onChange={handleInputChange}
              placeholder="Enter middle name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Last name</label>
            <input
              type="text"
              name="lastName"
              value={newUserForm.lastName}
              onChange={handleInputChange}
              placeholder="Enter last name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Phone number</label>
            <input
              type="tel"
              name="phoneNumber"
              value={newUserForm.phoneNumber}
              onChange={handleInputChange}
              placeholder="Enter phone number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              name="email"
              value={newUserForm.email}
              onChange={handleInputChange}
              placeholder="Enter email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">What is their role?</label>
            <p className="text-sm text-gray-500 mb-3">Select all that apply</p>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="beneficialOwner"
                  checked={newUserForm.roles.beneficialOwner}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-teal-600 border-gray-300 rounded"
                />
                <span className="ml-2">Beneficial owner (owns 25% of Entity)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="bookkeeper"
                  checked={newUserForm.roles.bookkeeper}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-teal-600 border-gray-300 rounded"
                />
                <span className="ml-2">Bookkeeper</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="payrollAdmin"
                  checked={newUserForm.roles.payrollAdmin}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-teal-600 border-gray-300 rounded"
                />
                <span className="ml-2">Payroll admin</span>
              </label>
            </div>
          </div>

          <button
            type="button"
            onClick={handleAddUser}
            className="w-full bg-teal-600 text-white py-3 rounded-md hover:bg-teal-700 transition-colors"
          >
            Done
          </button>
        </form>
      </div>
    </div>
  );

  return (
    <div className="p-8">
      {/* Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <div className="flex space-x-4">
          <button
            className={`py-4 px-6 font-semibold text-sm ${
              activeTab === 'Users'
                ? 'text-white bg-teal-600 rounded-t-lg'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('Users')}
          >
            Users
          </button>
          <button
            className={`py-4 px-6 font-semibold text-sm ${
              activeTab === 'Payment details'
                ? 'text-white bg-teal-600 rounded-t-lg'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('Payment details')}
          >
            Payment details
          </button>
        </div>
      </div>

      {/* Users Content */}
      {activeTab === 'Users' && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-2xl font-semibold">Users</h1>
            <button
              className="px-4 py-2 text-sm font-medium text-teal-600 border border-teal-600 rounded-md hover:bg-teal-50"
              onClick={() => setShowAddUserModal(true)}
            >
              Add new user
            </button>
          </div>

          <p className="text-gray-600 mb-8">
            You can add beneficial owners, payroll admins, and accountant / bookkeepers here.
          </p>

          {/* Users Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Name</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Business phone number</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Business email</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Date added</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Role</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900"></th>
                </tr>
              </thead>
              <tbody>
                {users.map((user, index) => (
                  <tr key={index} className="border-b border-gray-200">
                    <td className="py-4 px-4">{user.name}</td>
                    <td className="py-4 px-4">{user.businessPhone}</td>
                    <td className="py-4 px-4">{user.businessEmail}</td>
                    <td className="py-4 px-4">{user.dateAdded}</td>
                    <td className="py-4 px-4">{user.role}</td>
                    <td className="py-4 px-4">
                      <button
                        className="text-gray-400 hover:text-gray-600"
                        onClick={() => {/* Handle more options */}}
                      >
                        ⋮
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4 text-sm text-gray-600">
            <span>1-1 of 1</span>
            <div className="flex space-x-2">
              <button className="p-1" disabled>←</button>
              <button className="p-1" disabled>→</button>
            </div>
          </div>
        </div>
      )}

      {/* Add User Modal */}
      {showAddUserModal && <AddUserModal />}

      {/* Payment details Content */}
      {activeTab === 'Payment details' && (
        <div>
          <h1 className="text-2xl font-semibold mb-6">Payment details</h1>
          {companyBankLoading ? (
            <div className="text-center text-gray-500">Loading bank account...</div>
          ) : companyBankAccount ? (
            <div className="flex justify-center">
              <div className="bg-white border border-teal-600 rounded-lg shadow-md p-8 flex flex-col items-center" style={{ maxWidth: 400 }}>
                <div className="text-lg font-semibold mb-2">Connected Bank Account</div>
                <div className="mb-1"><span className="font-medium">Bank Name:</span> {companyBankAccount.bankName}</div>
                <div className="mb-1"><span className="font-medium">Account Name:</span> {companyBankAccount.accountName}</div>
                <div className="mb-1"><span className="font-medium">Account Number:</span> {companyBankAccount.accountNumber}</div>
                <div className="mb-1"><span className="font-medium">Account Type:</span> {companyBankAccount.accountType}</div>
              </div>
            </div>
          ) : !showBankAccountUI ? (
            <div className="flex justify-center">
              <div
                className="cursor-pointer bg-white border border-teal-600 rounded-lg shadow-md p-8 flex flex-col items-center hover:bg-teal-50 transition"
                style={{ maxWidth: 400 }}
                onClick={() => setShowBankAccountUI(true)}
              >
                <div className="text-4xl mb-2 text-teal-600">+</div>
                <div className="text-lg font-semibold mb-1">Add Bank Account</div>
                <div className="text-gray-500 text-sm">Connect your company bank account to enable payroll payments.</div>
              </div>
            </div>
          ) : (
            <BankAccountConnection />
          )}
          {companyBankError && <div className="text-center text-red-600 mt-4">{companyBankError}</div>}
        </div>
      )}
    </div>
  );
};

export default EmployerSettingsPage;

// --- BankAccountConnection Component ---
const BankAccountConnection: React.FC = () => {
  const [selectedMethod, setSelectedMethod] = useState<'plaid' | 'manual'>('plaid');
  const [showManualForm, setShowManualForm] = useState(false);
  const [form, setForm] = useState({
    bankName: '',
    accountHolder: '',
    routingNumber: '',
    accountNumber: '',
    confirmAccountNumber: '',
    accountType: '',
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleMethodChange = (method: 'plaid' | 'manual') => {
    setSelectedMethod(method);
    setShowManualForm(method === 'manual');
    setSuccess(null);
    setError(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleManualSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccess(null);
    setError(null);
    try {
      const selectedCompanyStr = localStorage.getItem('selectedCompany');
      if (!selectedCompanyStr) throw new Error('No company selected');
      const selectedCompany = JSON.parse(selectedCompanyStr);
      const companyId = selectedCompany.companyID || selectedCompany.companyId || selectedCompany.id;
      if (!companyId) throw new Error('Invalid company ID');
      // API call as per instructions
      const payload = {
        method: 'addCompanyBankAccount',
        companyFundingSourceEntity: {
          companyId,
          accountNumber: form.accountNumber,
          routingNumber: form.routingNumber,
          bankName: form.bankName,
          accountType: form.accountType,
          accountName: 'default',
        },
      };
      // Use fetch directly for now, or move to employerService if needed
      const credentials = `${import.meta.env.VITE_CLIENT_ID}:${import.meta.env.VITE_CLIENT_SECRET}`;
      const basicAuth = btoa(credentials);
      const response = await fetch('https://sandbox.rollfi.xyz/adminPortal#addCompanyBankAccount', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${basicAuth}`,
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }
      const result = await response.json();
      setSuccess(result.companyFundingSourceEntity?.message || 'Bank account added successfully.');
      setForm({ bankName: '', accountHolder: '', routingNumber: '', accountNumber: '', confirmAccountNumber: '', accountType: '' });
    } catch (err: any) {
      setError(err.message || 'Failed to add bank account.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto">
      <div className="mb-8">
        <div
          className={`border rounded-lg p-6 mb-4 flex items-center justify-between cursor-pointer ${selectedMethod === 'plaid' ? 'border-teal-600 bg-gray-50' : 'border-gray-200'}`}
          onClick={() => handleMethodChange('plaid')}
        >
          <div>
            <div className="font-semibold text-lg flex items-center">
              <span className="mr-2">🏦</span> Plaid connection
            </div>
            <div className="text-sm text-gray-600">2 day ACH<br />Instant verification</div>
          </div>
          <input type="radio" checked={selectedMethod === 'plaid'} readOnly />
        </div>
        <div
          className={`border rounded-lg p-6 flex items-center justify-between cursor-pointer ${selectedMethod === 'manual' ? 'border-teal-600 bg-gray-50' : 'border-gray-200'}`}
          onClick={() => handleMethodChange('manual')}
        >
          <div>
            <div className="font-semibold text-lg flex items-center">
              <span className="mr-2">🔗</span> Manual account linking
            </div>
            <div className="text-sm text-gray-600">4 day ACH<br />Micro deposit account verification needed</div>
          </div>
          <input type="radio" checked={selectedMethod === 'manual'} readOnly />
        </div>
      </div>
      {showManualForm && (
        <form className="bg-white rounded-lg p-8 shadow-md" onSubmit={handleManualSubmit}>
          <h2 className="text-xl font-semibold mb-2">Connect your bank account manually</h2>
          <p className="text-gray-500 text-sm mb-6">Connecting your account manually will result in a 4 day ACH window and a micro deposit to verify the account is active. It will take 4 to 5 business days to verify the account is active. Once the account is verified and active then you are able to run payroll.</p>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Bank Name</label>
            <input type="text" name="bankName" value={form.bankName} onChange={handleInputChange} placeholder="Enter bank name" className="w-full px-3 py-2 border border-gray-300 rounded-md" required />
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Account holder name</label>
            <input type="text" name="accountHolder" value={form.accountHolder} onChange={handleInputChange} placeholder="Enter account holder name" className="w-full px-3 py-2 border border-gray-300 rounded-md" required />
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">9 Digit routing number</label>
            <input type="text" name="routingNumber" value={form.routingNumber} onChange={handleInputChange} placeholder="Enter 9 digit routing number" className="w-full px-3 py-2 border border-gray-300 rounded-md" required maxLength={9} pattern="\\d{9}" />
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Account number</label>
            <input type="text" name="accountNumber" value={form.accountNumber} onChange={handleInputChange} placeholder="Enter account number" className="w-full px-3 py-2 border border-gray-300 rounded-md" required />
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Confirm account number</label>
            <input type="text" name="confirmAccountNumber" value={form.confirmAccountNumber} onChange={handleInputChange} placeholder="Enter confirm account number" className="w-full px-3 py-2 border border-gray-300 rounded-md" required />
          </div>
          <div className="mb-6">
            <label className="block text-sm font-medium mb-1">Account type</label>
            <select name="accountType" value={form.accountType} onChange={handleInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-md" required>
              <option value="">Select Account Type</option>
              <option value="checking">Checking</option>
              <option value="savings">Savings</option>
            </select>
          </div>
          <div className="flex justify-between mt-6">
            <button type="button" className="px-6 py-2 border border-gray-300 rounded-md text-gray-700" onClick={() => setShowManualForm(false)}>Back</button>
            <button type="submit" className="px-6 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700" disabled={loading}>{loading ? 'Saving...' : 'Save'}</button>
          </div>
          {success && <div className="mt-4 text-green-600 font-medium">{success}</div>}
          {error && <div className="mt-4 text-red-600 font-medium">{error}</div>}
        </form>
      )}
      {!showManualForm && (
        <div className="flex justify-end mt-8">
          <button className="px-6 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700" onClick={() => selectedMethod === 'manual' && setShowManualForm(true)}>Continue</button>
        </div>
      )}
    </div>
  );
};

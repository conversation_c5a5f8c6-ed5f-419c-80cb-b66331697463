import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import CopilotProvider from './components/CopilotProvider';
import { patchBtoa } from './utils/btoaPatch';
import AdminDashboard from './pages/AdminDashboard';
import AddCompanyPage from './pages/Admin/AddCompanyPage/AddCompanyPage';
import EmployeeDashboard from './pages/EmployeeDashboard';
import EmployeeDetailsPage from './pages/EmployeeDetailsPage';
import DocumentsPage from './pages/DocumentsPage';
import TeamDocuments from './pages/TeamDocuments';
import PersonalDocuments from './pages/PersonalDocuments';
import PaystubsPage from './pages/PaystubsPage';
import UsersPage from './pages/UsersPage';
import EmployerLayout from './layouts/EmployerLayout';
import EmployeeLayout from './layouts/EmployeeLayout';
import EmployerDashboard from './pages/EmployerDashboard';
import PayrollPage from './pages/PayrollPage';
import HiringPage from './pages/HiringPage';
import TeamPage from './pages/TeamPage';
import CompanyPage from './pages/CompanyPage';
import EmployerDocumentsPage from './pages/EmployerDocumentsPage';
import BenefitsPage from './pages/BenefitsPage';
import TaxesPage from './pages/TaxesPage';
import './index.css';
import AddEmployeePage from './pages/AddEmployeePage';
import AddIndependentContractorPage from './pages/AddIndependentContractorPage';
import AddBusinessContractorPage from './pages/AddBusinessContractorPage';
import BusinessDocuments from './pages/BusinessDocuments';
import EmployerSettingsPage from './pages/EmployerSettingsPage';
import EmployerHelpPage from './pages/EmployerHelpPage';
import LoginPage from './pages/LoginPage';
import SignupPage from './pages/SignupPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import ProtectedRoute from './components/ProtectedRoute';
import { AppProvider } from './context/AppContext';
import AuthProvider from './context/AuthProvider';
import ReportsPage from './pages/ReportsPage';
import CopilotActionsProvider from './components/CopilotActionsProvider';
import { BankAccountForm, FederalW4Form, StateW4Form } from './pages/EmployeeDashboard';

// Apply the btoa patch to handle non-Latin1 characters
// This is applied at module load time to ensure it's available globally
patchBtoa();

// Create a fallback component for unauthorized access
const UnauthorizedAccess = () => (
  <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
    <div className="p-8 bg-white rounded-lg shadow-md max-w-md w-full">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
      <p className="text-gray-700 mb-6">
        You don't have permission to access this page. Please contact your administrator if you believe this is an error.
      </p>
      <div className="flex justify-center">
        <button 
          onClick={() => window.history.back()} 
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Go Back
        </button>
      </div>
    </div>
  </div>
);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <AppProvider>
          <CopilotProvider>
            <CopilotActionsProvider />
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<Navigate to="/login" />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/signup" element={<SignupPage />} />
              <Route path="/forgot-password" element={<ForgotPasswordPage />} />
              
              {/* Admin routes - only accessible by admin */}
              <Route 
                path="/admin" 
                element={
                  <ProtectedRoute>
                    <AdminDashboard />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/company" 
                element={
                  <ProtectedRoute>
                    <AddCompanyPage />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/users" 
                element={
                  <ProtectedRoute>
                    <UsersPage />
                  </ProtectedRoute>
                } 
              />
              
              {/* Employee routes with shared layout */}
              <Route 
                path="/employee" 
                element={
                  <ProtectedRoute>
                    <EmployeeLayout />
                  </ProtectedRoute>
                }
              >
                <Route path="dashboard" element={<EmployeeDashboard />}>
                  <Route path="bankaccount" element={<BankAccountForm />} />
                  <Route path="w4info" element={<FederalW4Form />} />
                  <Route path="statew4" element={<StateW4Form />} />
                </Route>
                <Route path="details" element={<EmployeeDetailsPage />} />
                <Route path="documents" element={<DocumentsPage />}>
                  <Route path="business" element={<BusinessDocuments />} />
                  <Route path="team" element={<TeamDocuments />} />
                  <Route path="personal" element={<PersonalDocuments />} />
                </Route>
                <Route path="paystubs" element={<PaystubsPage />} />
              </Route>
              
              {/* Employer routes with shared layout - only accessible by admin and business users */}
              <Route 
                path="/employer" 
                element={
                  <ProtectedRoute>
                    <EmployerLayout />
                  </ProtectedRoute>
                }
              >
                <Route path="dashboard" element={<EmployerDashboard />} />
                <Route path="payroll" element={<PayrollPage />} />
                <Route path="hiring" element={<HiringPage />} />
                <Route path="hiring/add-employee" element={<AddEmployeePage />} />
                <Route path="hiring/add-independent-contractor" element={<AddIndependentContractorPage />} />
                <Route path="hiring/add-business-contractor" element={<AddBusinessContractorPage />} />
                <Route path="team" element={<TeamPage />} />
                <Route path="company" element={<CompanyPage />} />
                <Route path="documents" element={<EmployerDocumentsPage />} />
                <Route path="benefits" element={<BenefitsPage />} />
                <Route path="taxes" element={<TaxesPage />} />
                <Route path="settings" element={<EmployerSettingsPage />} />
                <Route path="help" element={<EmployerHelpPage />} />
                <Route path="reports" element={<ReportsPage />} />
              </Route>
              
              {/* Unauthorized access fallback */}
              <Route path="/unauthorized" element={<UnauthorizedAccess />} />
              
              {/* Catch-all route for any undefined routes */}
              <Route path="*" element={<Navigate to="/login" replace />} />
            </Routes>
          </CopilotProvider>
        </AppProvider>
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>
);

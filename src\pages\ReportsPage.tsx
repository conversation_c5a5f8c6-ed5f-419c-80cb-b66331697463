import React, { useState } from 'react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const getYearOptions = () => {
  const currentYear = new Date().getFullYear();
  return [currentYear - 1, currentYear, currentYear + 1];
};

const getMonthOptions = () => [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const getQuarterOptions = () => [
  { value: 'Q1', label: 'Q1 (Jan-Mar)' },
  { value: 'Q2', label: 'Q2 (Apr-Jun)' },
  { value: 'Q3', label: 'Q3 (Jul-Sep)' },
  { value: 'Q4', label: 'Q4 (Oct-Dec)' },
];
 // Use the companyId from your example
const clientId = import.meta.env.VITE_CLIENT_ID;
const clientSecret = import.meta.env.VITE_CLIENT_SECRET;
const BASIC_AUTH = 'Basic ' + btoa(`${clientId}:${clientSecret}`);
const API_URL_PAY_PERIOD = 'https://sandbox.rollfi.xyz/reports#getPayPeriod';
const API_URL_PAYROLL_JOURNAL = 'https://sandbox.rollfi.xyz/reports#getPayrollJournalReport';

const WORKER_TYPES = [
  { label: 'W2', value: 'W2', id: 'BFFBD3A7-965C-48CB-98FE-F638C02BD23C' },
  { label: '1099-NEC', value: '1099-NEC', id: '5BEEAC60-0533-47DE-BBBE-E39CF68BDAD1' },
];

const ReportsPage: React.FC = () => {
  const [showJournalModal, setShowJournalModal] = useState(false);
  const [dateRangeType, setDateRangeType] = useState('');
  const [selectedYear, setSelectedYear] = useState('');
  const [selectedMonth, setSelectedMonth] = useState('');
  const [selectedQuarter, setSelectedQuarter] = useState('');
  const [customStart, setCustomStart] = useState('');
  const [customEnd, setCustomEnd] = useState('');
  const [showPayrollGeneralModal, setShowPayrollGeneralModal] = useState(false);
  const [payPeriods, setPayPeriods] = useState<any[]>([]);
  const [selectedPayPeriod, setSelectedPayPeriod] = useState('');
  const [payrollJournalData, setPayrollJournalData] = useState<any>(null);
  const [loadingPeriods, setLoadingPeriods] = useState(false);
  const [loadingReport, setLoadingReport] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingJournal, setLoadingJournal] = useState(false);
  const [journalError, setJournalError] = useState<string | null>(null);
  const [expandedEmployee, setExpandedEmployee] = useState<string | null>(null);
  const [selectedWorkerType, setSelectedWorkerType] = useState(WORKER_TYPES[0].value);
  const [selectedWorkerTypeId, setSelectedWorkerTypeId] = useState(WORKER_TYPES[0].id);
  // Add state for Payroll Journal worker type
  const [journalWorkerType, setJournalWorkerType] = useState(WORKER_TYPES[0].value);
  const [journalWorkerTypeId, setJournalWorkerTypeId] = useState(WORKER_TYPES[0].id);
  const [journalPayPeriods, setJournalPayPeriods] = useState<any[]>([]);
  const [selectedJournalPayPeriod, setSelectedJournalPayPeriod] = useState('');

  // 1. Add state for Paystubs modal and data
  const [showPaystubModal, setShowPaystubModal] = useState(false);
  const [paystubUsers, setPaystubUsers] = useState<any[]>([]);
  const [selectedUserId, setSelectedUserId] = useState('');
  const [paystubPeriods, setPaystubPeriods] = useState<any[]>([]);
  const [paystubPeriodId, setPaystubPeriodId] = useState('');
  const [paystubData, setPaystubData] = useState<any>(null);
  const [loadingPaystubUsers, setLoadingPaystubUsers] = useState(false);
  const [loadingPaystubPeriods, setLoadingPaystubPeriods] = useState(false);
  const [loadingPaystub, setLoadingPaystub] = useState(false);
  const [paystubError, setPaystubError] = useState<string | null>(null);

  // --- Bank Transactions State ---
  const [showBankTransactionsModal, setShowBankTransactionsModal] = useState(false);
  const [bankWorkerType, setBankWorkerType] = useState('');
  const [bankWorkerTypeId, setBankWorkerTypeId] = useState('');
  const [bankPayPeriods, setBankPayPeriods] = useState<any[]>([]);
  const [selectedBankPayPeriod, setSelectedBankPayPeriod] = useState('');
  const [bankTransactions, setBankTransactions] = useState<any[]>([]);
  const [loadingBankPeriods, setLoadingBankPeriods] = useState(false);
  const [loadingBankTransactions, setLoadingBankTransactions] = useState(false);
  const [bankError, setBankError] = useState<string | null>(null);

  // Helper to get companyId from localStorage
  const getCompanyId = () => {
    const stored = localStorage.getItem('selectedCompany');
    if (stored) {
      try {
        const obj = JSON.parse(stored);
        return obj.companyID || obj.companyId || obj.company_id;
      } catch {
        return null;
      }
    }
    return null;
  };

  // Fetch pay periods for the company
  const fetchPayPeriods = async (workerType = 'W2') => {
    setLoadingPeriods(true);
    setError(null);
    setPayPeriods([]);
    setPayrollJournalData(null);
    const companyId = getCompanyId();
    console.log('Fetching pay periods (General) with:', { companyId, workerType });
    if (!companyId) {
      setError('No company selected. Please select a company first.');
      setLoadingPeriods(false);
      return;
    }
    try {
      const res = await fetch(API_URL_PAY_PERIOD, {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method: 'getPayPeriod',
          companyId,
          workerType,
        }),
      });
      const data = await res.json();
      console.log('API response (General):', data);
      if (data.PayPeriod && Array.isArray(data.PayPeriod)) {
        setPayPeriods(data.PayPeriod);
      } else {
        setError('No pay periods found.');
      }
    } catch (e) {
      setError('Failed to fetch pay periods.');
    } finally {
      setLoadingPeriods(false);
    }
  };

  // Fetch payroll journal report for selected pay period
  const fetchPayrollJournal = async (payPeriodId: string, workerTypeId: string) => {
    setLoadingReport(true);
    setError(null);
    setPayrollJournalData(null);
    const companyId = getCompanyId();
    if (!companyId) {
      setError('No company selected. Please select a company first.');
      setLoadingReport(false);
      return;
    }
    try {
      const res = await fetch(API_URL_PAYROLL_JOURNAL, {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method: 'getPayrollJournalReport',
          payPeriodId,
          companyId,
          WorkerTypeID: workerTypeId,
        }),
      });
      const data = await res.json();
      setPayrollJournalData(data);
    } catch (e) {
      setError('Failed to fetch payroll journal report.');
    } finally {
      setLoadingReport(false);
    }
  };

  // Update handlePayrollJournalClick to only reset state, not fetch anything
  const handlePayrollJournalClick = () => {
    setShowJournalModal(true);
    setLoadingJournal(false);
    setJournalError(null);
    setPayrollJournalData(null);
    setJournalWorkerType(''); // No default selection
    setJournalWorkerTypeId('');
    setSelectedJournalPayPeriod('');
    setJournalPayPeriods([]);
  };

  // Fetch pay periods for Payroll Journal (with worker type)
  const fetchJournalPayPeriods = async (workerType = 'W2', workerTypeId = '') => {
    setLoadingJournal(true);
    setJournalError(null);
    setJournalPayPeriods([]);
    setPayrollJournalData(null);
    const companyId = getCompanyId();
    console.log('Fetching pay periods (Journal) with:', { companyId, workerType });
    if (!companyId) {
      setJournalError('No company selected. Please select a company first.');
      setLoadingJournal(false);
      return;
    }
    try {
      const res = await fetch(API_URL_PAY_PERIOD, {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method: 'getPayPeriod',
          companyId,
          workerType,
        }),
      });
      const data = await res.json();
      console.log('API response (Journal):', data);
      // Handle array or single object
      if (Array.isArray(data.PayPeriod) && data.PayPeriod.length === 1) {
        const payPeriodId = data.PayPeriod[0].PayPeriodID || data.PayPeriod[0].payPeriodId;
        setJournalPayPeriods(data.PayPeriod);
        setSelectedJournalPayPeriod(payPeriodId);
        fetchPayrollJournalWithWorkerType(payPeriodId, workerTypeId);
      } else if (Array.isArray(data.PayPeriod) && data.PayPeriod.length > 1) {
        setJournalPayPeriods(data.PayPeriod);
        setSelectedJournalPayPeriod('');
      } else if (data.payPeriodId) {
        // API returned a single pay period object
        setJournalPayPeriods([data]);
        setSelectedJournalPayPeriod(data.payPeriodId);
        fetchPayrollJournalWithWorkerType(data.payPeriodId, workerTypeId);
      } else {
        setJournalError('No pay periods found.');
      }
    } catch (e) {
      setJournalError('Failed to fetch pay periods.');
    } finally {
      setLoadingJournal(false);
    }
  };

  // Fetch Payroll Journal report (with workerTypeId)
  const fetchPayrollJournalWithWorkerType = async (payPeriodId: string, workerTypeId: string) => {
    setLoadingJournal(true);
    setJournalError(null);
    setPayrollJournalData(null);
    const companyId = getCompanyId();
    if (!companyId) {
      setJournalError('No company selected. Please select a company first.');
      setLoadingJournal(false);
      return;
    }
    if (!workerTypeId) {
      setJournalError('WorkerType ID is mandatory. Please select a worker type.');
      setLoadingJournal(false);
      return;
    }
    try {
      const res = await fetch(API_URL_PAYROLL_JOURNAL, {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method: 'getPayrollJournalReport',
          payPeriodId,
          companyId,
          workerTypeId,
        }),
      });
      const data = await res.json();
      if (data.error) {
        setJournalError(data.error.message || 'Failed to fetch payroll journal report.');
      } else {
        setPayrollJournalData(data);
      }
    } catch (e) {
      setJournalError('Failed to fetch payroll journal report.');
    } finally {
      setLoadingJournal(false);
    }
  };

  // Handle worker type change for Payroll Journal
  const handleJournalWorkerTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const type = e.target.value;
    const found = WORKER_TYPES.find(w => w.value === type);
    setJournalWorkerType(type);
    setJournalWorkerTypeId(found ? found.id : '');
    setSelectedJournalPayPeriod('');
    setPayrollJournalData(null);
    fetchJournalPayPeriods(type, found ? found.id : '');
  };

  const handleWorkerTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const type = e.target.value;
    const found = WORKER_TYPES.find(w => w.value === type);
    setSelectedWorkerType(type);
    setSelectedWorkerTypeId(found ? found.id : '');
    setSelectedPayPeriod('');
    setPayrollJournalData(null);
    fetchPayPeriods(type);
  };

  const handleDownloadPDF = () => {
    const doc = new jsPDF();
    doc.setFontSize(14);
    doc.text('Payroll General Report', 10, 10);
    doc.setFontSize(10);
    doc.text(`Worker Type: ${selectedWorkerType}`, 10, 18);
    doc.text(`Pay Period: ${selectedPayPeriod}`, 10, 26);
    doc.text('Data:', 10, 34);
    let y = 42;
    const json = JSON.stringify(payrollJournalData, null, 2);
    const lines: string[] = doc.splitTextToSize(json, 180) as string[];
    lines.forEach((line) => {
      if (y > 280) {
        doc.addPage();
        y = 10;
      }
      doc.text(line, 10, y, { maxWidth: 180 });
      y += 6;
    });
    doc.save('payroll-general-report.pdf');
  };

  const renderEmployeeCards = (data: any) => {
    if (!data || !Array.isArray(data.Employee)) return <div>No employee data found.</div>;
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
        {data.Employee.map((emp: any, idx: number) => {
          const details = emp.report?.employeereportdetails?.[0] || {};
          const isExpanded = expandedEmployee === emp.employeeID;
          return (
            <div
              key={emp.employeeID || idx}
              style={{
                background: '#faf7fd',
                borderRadius: 12,
                boxShadow: isExpanded ? '0 4px 16px #c49a5a33' : '0 2px 8px #0001',
                padding: 20,
                cursor: 'pointer',
                border: isExpanded ? '2px solid #c49a5a' : '1px solid #eee',
                transition: 'box-shadow 0.2s, border 0.2s',
                position: 'relative',
              }}
              onClick={() => setExpandedEmployee(isExpanded ? null : emp.employeeID)}
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  <div style={{ fontWeight: 600, fontSize: 18 }}>{details.employeeName || 'N/A'}</div>
                  <div style={{ color: '#888', fontSize: 14 }}>{details.jobTitle || 'N/A'}</div>
                  <div style={{ color: '#555', fontSize: 15 }}>Phone: {details.phoneNumber || 'N/A'}</div>
                </div>
                <div style={{ fontSize: 22, color: '#1976d2', userSelect: 'none' }}>
                  {isExpanded ? '▲' : '▼'}
                </div>
              </div>
              {isExpanded && (
                <div style={{ marginTop: 16 }}>
                  {/* Address */}
                  <div style={{ marginBottom: 8 }}>
                    <b>Address:</b> {details.employeeAddress?.address || 'N/A'}
                  </div>
                  {/* Wages */}
                  <div style={{ marginBottom: 8 }}>
                    <b>Wage Rate:</b> {details.employeeWages?.[0]?.wageRate ?? 'N/A'} {details.employeeWages?.[0]?.wageBasis?.wageBasis || ''}
                  </div>
                  {/* W4 Status */}
                  <div style={{ marginBottom: 8 }}>
                    <b>W4 Status:</b> {details.w4Informations?.[0]?.w4FilingStatus?.w4FilingStatus || 'N/A'}
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <b>Dependents:</b> {details.w4Informations?.[0]?.dependents ?? 'N/A'}
                  </div>
                  {/* Entity Tax */}
                  <div style={{ marginBottom: 8 }}>
                    <b>Entity Tax:</b> {emp.report?.EntityTax?.map((tax: any, i: number) => (
                      <span key={i}>{tax.taxID}{i < emp.report.EntityTax.length - 1 ? ', ' : ''}</span>
                    )) || 'N/A'}
                  </div>
                  {/* Payroll Line Items */}
                  <div style={{ marginBottom: 8 }}>
                    <b>Payroll Line Items:</b> {emp.report?.PayrollLineItem?.length ? (
                      <ul style={{ margin: 0, paddingLeft: 18 }}>
                        {emp.report.PayrollLineItem.map((pli: any, i: number) => (
                          <li key={i}>{JSON.stringify(pli)}</li>
                        ))}
                      </ul>
                    ) : 'N/A'}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Responsive modal styles
  const modalStyle = {
    background: '#fff',
    borderRadius: '16px',
    padding: '2.5rem 1rem',
    minWidth: 600,
    width: '100%',
    maxWidth: 900,
    position: 'relative',
    boxShadow: '0 4px 32px #0002',
    margin: '2rem',
    overflow: 'auto',
    maxHeight: '90vh',
  };

  // Helper to get report year from pay period
  const getReportYear = (payPeriod: any): string => {
    if (!payPeriod) return '';
    // Try to extract year from payPeriod string (e.g., '09/16/2024 - 09/30/2024')
    const match = /\d{4}/.exec(payPeriod);
    return match ? match[0] : '';
  };

  // Helper to download PDF for an employee
  const handleDownloadEmployeePDF = (
    company: any,
    address: any,
    year: any,
    payPeriod: any,
    workerType: any,
    emp: any
  ): void => {
    const doc = new jsPDF();
    doc.setFontSize(14);
    doc.text('Payroll Journal Report', 10, 10);
    doc.setFontSize(10);
    doc.text(`Company: ${company}`, 10, 18);
    doc.text(`Address: ${address}`, 10, 24);
    doc.text(`Report Year: ${year}`, 10, 30);
    doc.text(`Pay Period: ${payPeriod}`, 10, 36);
    doc.text(`Worker Type: ${workerType}`, 10, 42);
    doc.text('----------------------------------------', 10, 48);
    // Employee details as table
    autoTable(doc, {
      startY: 54,
      head: [[
        'Name',
        'Job Title',
        'Phone',
        'Address',
        'Wage Rate',
        'W4 Status',
        'Dependents',
      ]],
      body: [[
        emp.employeeName || 'N/A',
        emp.jobTitle || 'N/A',
        emp.phoneNumber || 'N/A',
        emp.employeeAddress?.address || 'N/A',
        (emp.employeeWages?.[0]?.wageRate ?? 'N/A') + ' ' + (emp.employeeWages?.[0]?.wageBasis?.wageBasis || ''),
        emp.w4Informations?.[0]?.w4FilingStatus?.w4FilingStatus || 'N/A',
        emp.w4Informations?.[0]?.dependents ?? 'N/A',
      ]],
      styles: { fontSize: 10 },
      headStyles: { fillColor: [25, 118, 210] },
      margin: { left: 10, right: 10 },
      theme: 'grid',
    });
    doc.save(`${emp.employeeName || 'employee'}-payroll-report.pdf`);
  };

  // Helper to download all employees as a single PDF
  const handleDownloadAllEmployeesPDF = (
    company: any,
    address: any,
    year: any,
    payPeriod: any,
    workerType: any,
    employees: any[]
  ): void => {
    const doc = new jsPDF();
    doc.setFontSize(14);
    doc.text('Payroll Journal Report', 10, 10);
    doc.setFontSize(10);
    doc.text(`Company: ${company}`, 10, 18);
    doc.text(`Address: ${address}`, 10, 24);
    doc.text(`Report Year: ${year}`, 10, 30);
    doc.text(`Pay Period: ${payPeriod}`, 10, 36);
    doc.text(`Worker Type: ${workerType}`, 10, 42);
    doc.text('----------------------------------------', 10, 48);
    // All employees as table
    const body = employees.map(emp => [
      emp.report?.employeereportdetails?.[0]?.employeeName || 'N/A',
      emp.report?.employeereportdetails?.[0]?.jobTitle || 'N/A',
      emp.report?.employeereportdetails?.[0]?.phoneNumber || 'N/A',
      emp.report?.employeereportdetails?.[0]?.employeeAddress?.address || 'N/A',
      (emp.report?.employeereportdetails?.[0]?.employeeWages?.[0]?.wageRate ?? 'N/A') + ' ' + (emp.report?.employeereportdetails?.[0]?.employeeWages?.[0]?.wageBasis?.wageBasis || ''),
      emp.report?.employeereportdetails?.[0]?.w4Informations?.[0]?.w4FilingStatus?.w4FilingStatus || 'N/A',
      emp.report?.employeereportdetails?.[0]?.w4Informations?.[0]?.dependents ?? 'N/A',
    ]);
    autoTable(doc, {
      startY: 54,
      head: [[
        'Name',
        'Job Title',
        'Phone',
        'Address',
        'Wage Rate',
        'W4 Status',
        'Dependents',
      ]],
      body,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [25, 118, 210] },
      margin: { left: 10, right: 10 },
      theme: 'grid',
    });
    doc.save(`${company || 'all-employees'}-payroll-report.pdf`);
  };

  // Render employee table with download option
  const renderEmployeeTable = (
    data: any,
    company: any,
    address: any,
    year: any,
    payPeriod: any,
    workerType: any
  ): JSX.Element => {
    if (!data || !Array.isArray(data.Employee)) return <div>No employee data found.</div>;
    return (
      <div style={{ overflowX: 'auto' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: 14, minWidth: 600 }}>
          <thead>
            <tr style={{ background: '#f3f3f3' }}>
              <th style={{ padding: 8, border: '1px solid #eee' }}>Name</th>
              <th style={{ padding: 8, border: '1px solid #eee' }}>Job Title</th>
              <th style={{ padding: 8, border: '1px solid #eee' }}>Phone</th>
              <th style={{ padding: 8, border: '1px solid #eee' }}>Address</th>
              <th style={{ padding: 8, border: '1px solid #eee' }}>Wage Rate</th>
              <th style={{ padding: 8, border: '1px solid #eee' }}>W4 Status</th>
              <th style={{ padding: 8, border: '1px solid #eee' }}>Dependents</th>
              <th style={{ padding: 8, border: '1px solid #eee' }}>Download</th>
            </tr>
          </thead>
          <tbody>
            {data.Employee.map((emp: any, idx: number) => {
              const details = emp.report?.employeereportdetails?.[0] || {};
              return (
                <tr key={emp.employeeID || idx}>
                  <td style={{ padding: 8, border: '1px solid #eee' }}>{details.employeeName || 'N/A'}</td>
                  <td style={{ padding: 8, border: '1px solid #eee' }}>{details.jobTitle || 'N/A'}</td>
                  <td style={{ padding: 8, border: '1px solid #eee' }}>{details.phoneNumber || 'N/A'}</td>
                  <td style={{ padding: 8, border: '1px solid #eee' }}>{details.employeeAddress?.address || 'N/A'}</td>
                  <td style={{ padding: 8, border: '1px solid #eee' }}>{details.employeeWages?.[0]?.wageRate ?? 'N/A'} {details.employeeWages?.[0]?.wageBasis?.wageBasis || ''}</td>
                  <td style={{ padding: 8, border: '1px solid #eee' }}>{details.w4Informations?.[0]?.w4FilingStatus?.w4FilingStatus || 'N/A'}</td>
                  <td style={{ padding: 8, border: '1px solid #eee' }}>{details.w4Informations?.[0]?.dependents ?? 'N/A'}</td>
                  <td style={{ padding: 8, border: '1px solid #eee' }}>
                    <button
                      style={{ background: '#1976d2', color: '#fff', border: 'none', borderRadius: 4, padding: '0.25rem 1rem', fontWeight: 500, cursor: 'pointer', fontSize: 13 }}
                      onClick={() => handleDownloadEmployeePDF(company, address, year, payPeriod, workerType, details)}
                    >Download PDF</button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };

  // 2. Fetch users for paystub modal
  const fetchPaystubUsers = async () => {
    setLoadingPaystubUsers(true);
    setPaystubError(null);
    setPaystubUsers([]);
    setSelectedUserId('');
    setPaystubPeriods([]);
    setPaystubPeriodId('');
    setPaystubData(null);
    try {
      const selectedCompanyStr = localStorage.getItem('selectedCompany');
      if (!selectedCompanyStr) throw new Error('No company selected');
      const selectedCompany = JSON.parse(selectedCompanyStr);
      const companyId = selectedCompany.companyID || selectedCompany.companyId || selectedCompany.company_id;
      const res = await fetch('https://sandbox.rollfi.xyz/reports#getUsers', {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ method: 'getUsers', companyId }),
      });
      const data = await res.json();
      // Handle both 'users' and 'user' keys
      let userList = [];
      if (data.users && Array.isArray(data.users)) {
        userList = data.users;
      } else if (data.user && Array.isArray(data.user)) {
        userList = data.user;
      } else if (Array.isArray(data)) {
        userList = data;
      }
      console.log('Fetched user list:', userList);
      if (userList.length > 0) {
        setPaystubUsers(userList);
      } else {
        setPaystubError('No users found.');
      }
    } catch (e) {
      setPaystubError('Failed to fetch users.');
    } finally {
      setLoadingPaystubUsers(false);
    }
  };

  // Refactor fetchPaystubPeriods to accept userId as a parameter
  const fetchPaystubPeriods = (userIdParam: string) => {
    setLoadingPaystubPeriods(true);
    setPaystubError(null);
    setPaystubPeriods([]);
    setPaystubPeriodId('');
    setPaystubData(null);
    try {
      const selectedCompanyStr = localStorage.getItem('selectedCompany');
      if (!selectedCompanyStr) throw new Error('No company selected');
      const selectedCompany = JSON.parse(selectedCompanyStr);
      const companyId = selectedCompany.companyID || selectedCompany.companyId || selectedCompany.company_id;
      console.log('Looking for userId:', userIdParam, 'in', paystubUsers);
      const selectedUser = paystubUsers.find((u: any) => u.userId === userIdParam || u.userID === userIdParam);
      const workerType = selectedUser && selectedUser.WorkerType && (selectedUser.WorkerType.WorkerType || selectedUser.WorkerType.workerType) ? (selectedUser.WorkerType.WorkerType || selectedUser.WorkerType.workerType) : 'W2';
      console.log('Fetching pay periods with:', { companyId, workerType, selectedUser });
      fetch('https://sandbox.rollfi.xyz/reports#getPayPeriod', {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ method: 'getPayPeriod', companyId, workerType }),
      })
        .then(res => res.json())
        .then(data => {
          console.log('Pay period API response:', data);
          if (data.PayPeriod && Array.isArray(data.PayPeriod)) {
            setPaystubPeriods(data.PayPeriod);
          } else if (data.PayPeriod && typeof data.PayPeriod === 'object') {
            setPaystubPeriods([data.PayPeriod]);
          } else if (data.payPeriodId) {
            setPaystubPeriods([data]);
          } else {
            setPaystubError('No pay periods found.');
          }
        })
        .catch(() => setPaystubError('Failed to fetch pay periods.'))
        .finally(() => setLoadingPaystubPeriods(false));
    } catch (e) {
      setPaystubError('Failed to fetch pay periods.');
      setLoadingPaystubPeriods(false);
    }
  };

  // 4. Fetch paystub for selected user and period
  const fetchPaystub = async () => {
    setLoadingPaystub(true);
    setPaystubError(null);
    setPaystubData(null);
    try {
      const res = await fetch('https://sandbox.rollfi.xyz/reports#getPayStub', {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ method: 'getPayStub', payPeriodId: paystubPeriodId, userId: selectedUserId }),
      });
      const data = await res.json();
      if (data && data.PayStub) {
        setPaystubData(data.PayStub);
      } else {
        setPaystubError('No paystub found for this user and period.');
      }
    } catch (e) {
      setPaystubError('Failed to fetch paystub.');
    } finally {
      setLoadingPaystub(false);
    }
  };

  // 5. Download paystub as PDF
  const handleDownloadPaystubPDF = () => {
    if (!paystubData) return;
    const doc = new jsPDF();
    doc.setFontSize(16);
    doc.text('Paystub', 10, 10);
    doc.setFontSize(12);
    let y = 20;
    Object.entries(paystubData).forEach(([key, value]) => {
      doc.text(`${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`, 10, y);
      y += 8;
      if (y > 280) { doc.addPage(); y = 10; }
    });
    doc.save('paystub.pdf');
  };

  // Fetch pay periods for Bank Transactions (with worker type)
  const fetchBankPayPeriods = async (workerType = 'W2') => {
    setLoadingBankPeriods(true);
    setBankError(null);
    setBankPayPeriods([]);
    setSelectedBankPayPeriod('');
    setBankTransactions([]);
    const companyId = getCompanyId();
    console.log('[Bank] Fetching pay periods with:', { companyId, workerType });
    if (!companyId) {
      setBankError('No company selected. Please select a company first.');
      setLoadingBankPeriods(false);
      return;
    }
    try {
      const requestBody = {
        method: 'getPayPeriod',
        companyId,
        workerType,
      };
      console.log('[Bank] Request body:', requestBody);
      const res = await fetch(API_URL_PAY_PERIOD, {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      const data = await res.json();
      console.log('[Bank] API response:', data);
      if (data.PayPeriod && Array.isArray(data.PayPeriod)) {
        setBankPayPeriods(data.PayPeriod);
      } else if (data.PayPeriod && typeof data.PayPeriod === 'object') {
        setBankPayPeriods([data.PayPeriod]);
      } else if (data.payPeriodId && data.payPeriod) {
        setBankPayPeriods([{ ...data }]);
      } else {
        setBankError('No pay periods found.');
      }
    } catch (e) {
      console.error('[Bank] Error fetching pay periods:', e);
      setBankError('Failed to fetch pay periods.');
    } finally {
      setLoadingBankPeriods(false);
    }
  };

  // Fetch bank transactions for selected pay period
  const fetchBankTransactions = async (payPeriodId: string) => {
    setLoadingBankTransactions(true);
    setBankError(null);
    setBankTransactions([]);
    try {
      const res = await fetch('https://sandbox.rollfi.xyz/reports#getTransactions', {
        method: 'POST',
        headers: {
          'Authorization': BASIC_AUTH,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ method: 'getTransactions', payPeriodId }),
      });
      const data = await res.json();
      if (data.payrollTransaction && Array.isArray(data.payrollTransaction)) {
        setBankTransactions(data.payrollTransaction);
      } else {
        setBankError('No transactions found for this pay period.');
      }
    } catch (e) {
      setBankError('Failed to fetch transactions.');
    } finally {
      setLoadingBankTransactions(false);
    }
  };

  // Download transactions as PDF
  const handleDownloadBankTransactionsPDF = () => {
    if (!bankTransactions.length) return;
    const doc = new jsPDF();
    doc.setFontSize(16);
    doc.text('Bank Transactions', 10, 10);
    doc.setFontSize(12);
    const selectedPeriodObj = bankPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedBankPayPeriod);
    const payPeriodLabel = selectedPeriodObj?.PayPeriod || selectedPeriodObj?.payPeriod || selectedPeriodObj?.payBeginDate && selectedPeriodObj?.payEndDate ? `${selectedPeriodObj.payBeginDate} - ${selectedPeriodObj.payEndDate}` : selectedBankPayPeriod;
    doc.text(`Pay Period: ${payPeriodLabel}`, 10, 18);
    autoTable(doc, {
      startY: 26,
      head: [[
        'Reference ID',
        'Source',
        'Source Account',
        'Destination',
        'Destination Account',
        'Transaction Name',
        'Amount',
        'Status',
      ]],
      body: bankTransactions.map(tx => [
        tx.requestReferenceId,
        tx.source,
        tx.sourceAccount,
        tx.destination,
        tx.destinationAccount,
        tx.transactionName,
        tx.transferAmount,
        tx.status
      ]),
      styles: { fontSize: 10 },
      headStyles: { fillColor: [25, 118, 210] },
      margin: { left: 10, right: 10 },
      theme: 'grid',
    });
    doc.save('bank-transactions.pdf');
  };

  const handleGenerateClick = (reportName: string) => {
    switch (reportName) {
      case "Bank Transactions":
        setShowBankTransactionsModal(true);
        setBankWorkerType('');
        setBankWorkerTypeId('');
        setBankPayPeriods([]);
        setSelectedBankPayPeriod('');
        setBankTransactions([]);
        window.scrollTo({ top: 0, behavior: "smooth" });
        break;
      case "Payroll journal (legacy)":
        setShowJournalModal(true);
        break;
      case "Paystubs":
        setShowPaystubModal(true);
        fetchPaystubUsers();
        window.scrollTo({ top: 0, behavior: "smooth" });
        break;
      case "Payroll General report":
        setShowPayrollGeneralModal(true);
        fetchPayPeriods();
        break;
      default:
        alert(`Generate action for "${reportName}" is not implemented yet.`);
    }
  };

  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div style={{ padding: '2rem' }}>
      <h1 style={{ fontWeight: 600, fontSize: '2rem', marginBottom: '2rem' }}>Reports</h1>
      <div style={{ display: 'flex', gap: '2rem', marginBottom: '2rem', flexWrap: 'wrap' }}>
        {/* Payroll Journal Card */}
        <div
          style={{ flex: 1, background: '#faf7fd', borderRadius: '16px', boxShadow: '0 2px 8px #0001', padding: '2rem', minWidth: 280, cursor: 'pointer', transition: 'box-shadow 0.2s, transform 0.2s' }}
          onClick={handlePayrollJournalClick}
          onMouseEnter={e => (e.currentTarget.style.boxShadow = '0 4px 16px #c49a5a33')}
          onMouseLeave={e => (e.currentTarget.style.boxShadow = '0 2px 8px #0001')}
        >
          <div style={{ fontSize: '2.5rem', color: '#c49a5a', marginBottom: '1rem' }}>▶️</div>
          <div style={{ fontWeight: 600, fontSize: '1.25rem' }}>Payroll Journal</div>
          <div style={{ color: '#555', marginBottom: '1.5rem' }}>Generate journal entry report for each paycheck</div>
          <div style={{ background: '#1976d2', color: '#fff', borderRadius: '8px', padding: '0.5rem 1.5rem', fontWeight: 500, display: 'inline-block', marginTop: 8 }}>Generate</div>
        </div>
        {/* Cash Requirements Card */}
        <div style={{ flex: 1, background: '#f7faf7', borderRadius: '16px', boxShadow: '0 2px 8px #0001', padding: '2rem', minWidth: 280 }}>
          <div style={{ fontSize: '2.5rem', color: '#5ac46b', marginBottom: '1rem' }}>💲</div>
          <div style={{ fontWeight: 600, fontSize: '1.25rem' }}>Cash Requirements</div>
          <div style={{ color: '#555', marginBottom: '1.5rem' }}>View your cash requirements here</div>
          <button style={{ background: '#fff', color: '#1976d2', border: '1px solid #1976d2', borderRadius: '8px', padding: '0.5rem 1.5rem', fontWeight: 500, cursor: 'pointer' }}>Generate</button>
        </div>
        {/* Custom Report Card */}
        <div style={{ flex: 1, background: '#f7fafd', borderRadius: '16px', boxShadow: '0 2px 8px #0001', padding: '2rem', minWidth: 280 }}>
          <div style={{ fontSize: '2.5rem', color: '#5ac4e1', marginBottom: '1rem' }}>💡</div>
          <div style={{ fontWeight: 600, fontSize: '1.25rem' }}>Custom Report</div>
          <div style={{ color: '#555', marginBottom: '1.5rem' }}>Generate a custom report</div>
          <div style={{ color: '#888', fontSize: '0.95rem' }}>Development in Progress</div>
        </div>
      </div>
      {/* Custom Reports List (Gusto-style) */}
      <div style={{ background: '#fff', borderRadius: '16px', boxShadow: '0 2px 8px #0001', padding: '2rem', marginBottom: '2rem', maxWidth: 900, marginLeft: 'auto', marginRight: 'auto' }}>
        <h2 style={{ fontWeight: 600, fontSize: '1.25rem', marginBottom: '1.5rem' }}>Available Reports</h2>
        {/* Search Bar */}
        <div style={{ marginBottom: 24 }}>
          <input
            type="text"
            placeholder="Search"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '0.75rem 1rem',
              borderRadius: 8,
              border: '1px solid #ddd',
              fontSize: 16,
              outline: 'none'
            }}
          />
        </div>
        <div>
          {[
            {
              name: "Bank Transactions",
              desc: "View and download your company's bank transactions.",
              category: "Finance"
            },
            {
              name: "Employee Summary",
              desc: "Summary of all employees and their details.",
              category: "HR"
            },
            {
              name: "Federal Tax Docs",
              desc: "Access federal tax documents for your company.",
              category: "Taxes and Compliance"
            },
            {
              name: "General Ledger Report",
              desc: "General ledger report for payroll and compliance.",
              category: "Payroll, Compliance"
            },
            {
              name: "Payroll Data Reports",
              desc: "Detailed payroll data reports for analysis.",
              category: "Payroll"
            },
            {
              name: "Payroll General Report",
              desc: "General payroll report for the selected period.",
              category: "Payroll"
            },
            {
              name: "Payroll Trends",
              desc: "Identify and monitor payroll trends over a specified time period.",
              category: "Payroll, Trends"
            },
            {
              name: "State Tax Documents",
              desc: "View your state tax filings.",
              category: "Taxes and Compliance"
            },
            {
              name: "Tax and Compliance Forms",
              desc: "View all quarterly and annual tax and compliance forms.",
              category: "Taxes and Compliance"
            },
            {
              name: "W-2 and W-3 Tax Forms",
              desc: "View your employees’ W-2s and W-3s forms.",
              category: "Taxes and Compliance"
            },
            {
              name: "Paystubs",
              desc: "View and download employee paystubs.",
              category: "Payroll"
            }
          ]
            .filter(report => {
              const term = searchTerm.trim().toLowerCase();
              if (!term) return true;
              return (
                report.name.toLowerCase().includes(term) ||
                report.desc.toLowerCase().includes(term) ||
                report.category.toLowerCase().includes(term)
              );
            })
            .map((report, idx, arr) => (
              <div
                key={report.name}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '1.25rem 0',
                  borderBottom: idx !== arr.length - 1 ? '1px solid #f3f3f3' : 'none',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                }}
                onMouseEnter={e => (e.currentTarget.style.background = '#f7fafd')}
                onMouseLeave={e => (e.currentTarget.style.background = 'transparent')}
              >
                <div>
                  <div style={{ fontWeight: 600, fontSize: 16, color: '#1976d2', marginBottom: 2 }}>
                    {report.name}
                  </div>
                  <div style={{ color: '#555', fontSize: 14, marginBottom: 2 }}>
                    {report.desc}
                  </div>
                  <div style={{ color: '#888', fontSize: 13 }}>
                    {report.category}
                  </div>
                </div>
                <button
                  style={{
                    background: '#1976d2',
                    color: '#fff',
                    border: 'none',
                    borderRadius: 8,
                    padding: '0.5rem 1.5rem',
                    fontWeight: 500,
                    cursor: 'pointer',
                    opacity: 1,
                    marginLeft: 16
                  }}
                  onClick={() => handleGenerateClick(report.name)}
                >
                  Generate
                </button>
              </div>
            ))}
        </div>
      </div>
      {/* Table Section */}
      <div style={{ background: '#fff', borderRadius: '16px', boxShadow: '0 2px 8px #0001', padding: '2rem', marginTop: '2rem' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ textAlign: 'left', borderBottom: '1px solid #eee' }}>
              <th style={{ padding: '0.75rem 0.5rem' }}>Report Title</th>
              <th style={{ padding: '0.75rem 0.5rem' }}>Type</th>
              <th style={{ padding: '0.75rem 0.5rem' }}>Generation Date</th>
              <th style={{ padding: '0.75rem 0.5rem' }}></th>
            </tr>
          </thead>
          <tbody>
            {/* Empty for now */}
          </tbody>
        </table>
        <div style={{ textAlign: 'right', color: '#888', marginTop: '1rem' }}>1–0 of 0</div>
      </div>

      {/* Payroll Journal Modal */}
      {showJournalModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: 'rgba(0,0,0,0.15)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <div style={modalStyle as React.CSSProperties}>
            <button
              onClick={() => setShowJournalModal(false)}
              style={{
                position: 'absolute',
                top: 18,
                right: 18,
                background: 'none',
                border: 'none',
                fontSize: 28,
                cursor: 'pointer',
                color: '#444',
              }}
              aria-label="Close"
            >×</button>
            <h2 style={{ fontWeight: 700, fontSize: '2rem', textAlign: 'center', marginBottom: '2rem' }}>
              Payroll Journal Report
            </h2>
            <div style={{ marginBottom: 16 }}>
              <label style={{ fontWeight: 500, marginRight: 8 }}>Select Worker Type:</label>
              <select
                value={journalWorkerType}
                onChange={e => {
                  const type = e.target.value;
                  const found = WORKER_TYPES.find(w => w.value === type);
                  setJournalWorkerType(type);
                  setJournalWorkerTypeId(found ? found.id : '');
                  setSelectedJournalPayPeriod('');
                  setPayrollJournalData(null);
                  setJournalPayPeriods([]);
                  if (type && found && found.id) {
                    fetchJournalPayPeriods(type, found.id);
                  }
                }}
                style={{ padding: '0.5rem', borderRadius: 6, border: '1px solid #ccc', fontSize: 16, marginRight: 12 }}
              >
                <option value="">Select Worker Type</option>
                {WORKER_TYPES.map(w => (
                  <option key={w.value} value={w.value}>{w.label}</option>
                ))}
              </select>
            </div>
            {/* Download All button after worker type selection and before table */}
            {journalWorkerType && payrollJournalData && payrollJournalData.Employee && payrollJournalData.Employee.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <button
                  style={{ background: '#1976d2', color: '#fff', border: 'none', borderRadius: 6, padding: '0.5rem 1.5rem', fontWeight: 500, cursor: 'pointer', fontSize: 15 }}
                  onClick={() => handleDownloadAllEmployeesPDF(
                    payrollJournalData.Company?.[0]?.company || '',
                    payrollJournalData.CompanyLocation?.[0]?.address || '',
                    getReportYear(journalPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedJournalPayPeriod)?.PayPeriod || payrollJournalData.PayPeriod?.[0]?.PayPeriod),
                    journalPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedJournalPayPeriod)?.PayPeriod || payrollJournalData.PayPeriod?.[0]?.PayPeriod,
                    journalWorkerType,
                    payrollJournalData.Employee
                  )}
                >Download All as PDF</button>
              </div>
            )}
            {/* Only show the rest if a worker type is selected */}
            {journalWorkerType && (
              loadingJournal ? (
                <div style={{ textAlign: 'center', margin: '2rem 0' }}>Loading...</div>
              ) : journalError ? (
                <div style={{ color: 'red', marginBottom: 16 }}>{journalError}</div>
              ) : (journalPayPeriods.length > 1 ? (
                <>
                  <div style={{ marginBottom: 16 }}>
                    <label style={{ fontWeight: 500, marginRight: 8 }}>Select Pay Period:</label>
                    <select
                      value={selectedJournalPayPeriod}
                      onChange={e => {
                        setSelectedJournalPayPeriod(e.target.value);
                        fetchPayrollJournalWithWorkerType(e.target.value, journalWorkerTypeId);
                      }}
                      style={{ padding: '0.5rem', borderRadius: 6, border: '1px solid #ccc', fontSize: 16 }}
                    >
                      <option value="">Select Pay Period</option>
                      {journalPayPeriods.map((p, idx) => (
                        <option key={idx} value={p.PayPeriodID || p.payPeriodId || p.PayPeriod}>
                          {p.PayPeriod || p.payPeriod || p.PayDate || p.PayBeginDate + ' - ' + p.PayEndDate}
                        </option>
                      ))}
                    </select>
                  </div>
                </>
              ) : null)
            )}
            {/* Only show employee cards if a pay period is selected and payrollJournalData is available */}
            {selectedJournalPayPeriod && payrollJournalData && (
              <div style={{ maxHeight: 600, overflow: 'auto', marginTop: 16 }}>
                {/* Header section */}
                <div style={{ marginBottom: 16, padding: 12, background: '#f7f7fa', borderRadius: 8 }}>
                  <div style={{ fontWeight: 600, fontSize: 18 }}>{payrollJournalData.Company?.[0]?.company || 'Company Name'}</div>
                  <div style={{ color: '#555', fontSize: 15 }}>{payrollJournalData.CompanyLocation?.[0]?.address || 'Company Address'}</div>
                  <div style={{ color: '#888', fontSize: 14 }}>Report Year: {getReportYear(journalPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedJournalPayPeriod)?.PayPeriod || payrollJournalData.PayPeriod?.[0]?.PayPeriod)}</div>
                  <div style={{ color: '#888', fontSize: 14 }}>Pay Period: {journalPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedJournalPayPeriod)?.PayPeriod || payrollJournalData.PayPeriod?.[0]?.PayPeriod}</div>
                  <div style={{ color: '#888', fontSize: 14 }}>Pay Start: {journalPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedJournalPayPeriod)?.payBeginDate || payrollJournalData.PayPeriod?.[0]?.payBeginDate || 'N/A'}</div>
                  <div style={{ color: '#888', fontSize: 14 }}>Pay End: {journalPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedJournalPayPeriod)?.payEndDate || payrollJournalData.PayPeriod?.[0]?.payEndDate || 'N/A'}</div>
                  <div style={{ color: '#888', fontSize: 14 }}>Worker Type: {journalWorkerType}</div>
                </div>
                {/* Employee table */}
                {renderEmployeeTable(
                  payrollJournalData,
                  payrollJournalData.Company?.[0]?.company || '',
                  payrollJournalData.CompanyLocation?.[0]?.address || '',
                  getReportYear(journalPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedJournalPayPeriod)?.PayPeriod || payrollJournalData.PayPeriod?.[0]?.PayPeriod),
                  journalPayPeriods.find(p => (p.PayPeriodID || p.payPeriodId || p.PayPeriod) === selectedJournalPayPeriod)?.PayPeriod || payrollJournalData.PayPeriod?.[0]?.PayPeriod,
                  journalWorkerType
                )}
              </div>
            )}
          </div>
        </div>
      )}
      {/* Payroll General Modal */}
      {showPayrollGeneralModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: 'rgba(0,0,0,0.15)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <div style={{
            background: '#fff',
            borderRadius: '16px',
            padding: '2.5rem 3rem',
            minWidth: 480,
            maxWidth: '90vw',
            position: 'relative',
            boxShadow: '0 4px 32px #0002',
            margin: '2rem',
          }}>
            <button
              onClick={() => setShowPayrollGeneralModal(false)}
              style={{
                position: 'absolute',
                top: 18,
                right: 18,
                background: 'none',
                border: 'none',
                fontSize: 28,
                cursor: 'pointer',
                color: '#444',
              }}
              aria-label="Close"
            >×</button>
            <h2 style={{ fontWeight: 700, fontSize: '2rem', textAlign: 'center', marginBottom: '2rem' }}>
              Generate Payroll General Report
            </h2>
            <div style={{ marginBottom: 16 }}>
              <label style={{ fontWeight: 500, marginRight: 8 }}>Select Worker Type:</label>
              <select
                value={selectedWorkerType}
                onChange={handleWorkerTypeChange}
                style={{ padding: '0.5rem', borderRadius: 6, border: '1px solid #ccc', fontSize: 16, marginRight: 12 }}
              >
                {WORKER_TYPES.map(w => (
                  <option key={w.value} value={w.value}>{w.label}</option>
                ))}
              </select>
            </div>
            {loadingPeriods ? (
              <div style={{ textAlign: 'center', margin: '2rem 0' }}>Loading pay periods...</div>
            ) : error ? (
              <div style={{ color: 'red', marginBottom: 16 }}>{error}</div>
            ) : payPeriods.length === 0 ? (
              <div style={{ color: '#888', marginBottom: 16 }}>No pay periods found.</div>
            ) : (
              <>
                <div style={{ marginBottom: 16 }}>
                  <label style={{ fontWeight: 500, marginRight: 8 }}>Select Pay Period:</label>
                  <select
                    value={selectedPayPeriod}
                    onChange={e => setSelectedPayPeriod(e.target.value)}
                    style={{ padding: '0.5rem', borderRadius: 6, border: '1px solid #ccc', fontSize: 16 }}
                  >
                    <option value="">Select Pay Period</option>
                    {payPeriods.map((p, idx) => (
                      <option key={idx} value={p.PayPeriodID || p.PayPeriodId || p.PayPeriod}>
                        {p.PayPeriod || p.PayDate || p.PayBeginDate + ' - ' + p.PayEndDate}
                      </option>
                    ))}
                  </select>
                  <button
                    style={{ marginLeft: 12, background: '#1976d2', color: '#fff', border: 'none', borderRadius: 6, padding: '0.5rem 1.5rem', fontWeight: 500, cursor: 'pointer' }}
                    disabled={!selectedPayPeriod || loadingReport}
                    onClick={() => fetchPayrollJournal(selectedPayPeriod, selectedWorkerTypeId)}
                  >{loadingReport ? 'Generating...' : 'Generate Report'}</button>
                </div>
                {payrollJournalData && (
                  <>
                    <div style={{ maxHeight: 400, overflow: 'auto', background: '#f9f9f9', borderRadius: 8, padding: 16, marginTop: 16 }}>
                      <pre style={{ fontSize: 13, whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>{JSON.stringify(payrollJournalData, null, 2)}</pre>
                    </div>
                    <button
                      style={{ marginTop: 16, background: '#1976d2', color: '#fff', border: 'none', borderRadius: 6, padding: '0.5rem 1.5rem', fontWeight: 500, cursor: 'pointer' }}
                      onClick={handleDownloadPDF}
                    >Download as PDF</button>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      )}
      {/* Paystub Modal */}
      {showPaystubModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: 'rgba(0,0,0,0.15)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <div style={{
            background: '#fff',
            borderRadius: '16px',
            padding: '2.5rem 3rem',
            minWidth: 480,
            maxWidth: '90vw',
            position: 'relative',
            boxShadow: '0 4px 32px #0002',
            margin: '2rem',
          }}>
            <button
              onClick={() => setShowPaystubModal(false)}
              style={{
                position: 'absolute',
                top: 18,
                right: 18,
                background: 'none',
                border: 'none',
                fontSize: 28,
                cursor: 'pointer',
                color: '#444',
              }}
              aria-label="Close"
            >×</button>
            <h2 style={{ fontWeight: 700, fontSize: '2rem', textAlign: 'center', marginBottom: '2rem' }}>
              Generate Paystub
            </h2>
            {/* User selection */}
            <div style={{ marginBottom: 16 }}>
              <label style={{ fontWeight: 500, marginRight: 8 }}>Select Employee:</label>
              {loadingPaystubUsers ? (
                <span>Loading users...</span>
              ) : paystubError ? (
                <span style={{ color: 'red' }}>{paystubError}</span>
              ) : (
                <select
                  value={selectedUserId}
                  onChange={e => {
                    const val = e.target.value;
                    setSelectedUserId(val);
                    setPaystubPeriodId('');
                    setPaystubData(null);
                    if (val) fetchPaystubPeriods(val); // Only call if a user is selected
                  }}
                  style={{ padding: '0.5rem', borderRadius: 6, border: '1px solid #ccc', fontSize: 16 }}
                >
                  <option value="">Select Employee</option>
                  {paystubUsers.map((u: any) => (
                    <option key={u.userId || u.userID} value={u.userId || u.userID}>{u.firstName} {u.lastName} ({u.email})</option>
                  ))}
                </select>
              )}
            </div>
            {/* Pay period selection */}
            {selectedUserId && (
              <div style={{ marginBottom: 16 }}>
                <label style={{ fontWeight: 500, marginRight: 8 }}>Select Pay Period:</label>
                {loadingPaystubPeriods ? (
                  <span>Loading pay periods...</span>
                ) : paystubError ? (
                  <span style={{ color: 'red' }}>{paystubError}</span>
                ) : (
                  <select
                    value={paystubPeriodId}
                    onChange={e => { setPaystubPeriodId(e.target.value); setPaystubData(null); }}
                    style={{ padding: '0.5rem', borderRadius: 6, border: '1px solid #ccc', fontSize: 16 }}
                  >
                    <option value="">Select Pay Period</option>
                    {paystubPeriods.map((p: any, idx: number) => (
                      <option key={idx} value={p.PayPeriodID || p.payPeriodId || p.PayPeriod}>{p.PayPeriod || p.PayDate || p.PayBeginDate + ' - ' + p.PayEndDate}</option>
                    ))}
                  </select>
                )}
              </div>
            )}
            {/* Generate button */}
            {selectedUserId && paystubPeriodId && (
              <button
                style={{ background: '#1976d2', color: '#fff', border: 'none', borderRadius: 6, padding: '0.5rem 1.5rem', fontWeight: 500, cursor: 'pointer', marginBottom: 16 }}
                onClick={fetchPaystub}
                disabled={loadingPaystub}
              >{loadingPaystub ? 'Generating...' : 'Generate Paystub'}</button>
            )}
            {/* Paystub data and download */}
            {paystubData && (
              <div style={{ marginTop: 24 }}>
                <pre style={{ fontSize: 13, whiteSpace: 'pre-wrap', wordBreak: 'break-all', background: '#f9f9f9', borderRadius: 8, padding: 16 }}>{JSON.stringify(paystubData, null, 2)}</pre>
                <button
                  style={{ marginTop: 16, background: '#1976d2', color: '#fff', border: 'none', borderRadius: 6, padding: '0.5rem 1.5rem', fontWeight: 500, cursor: 'pointer' }}
                  onClick={handleDownloadPaystubPDF}
                >Download as PDF</button>
              </div>
            )}
          </div>
        </div>
      )}
      {/* Bank Transactions Modal */}
      {showBankTransactionsModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: 'rgba(0,0,0,0.15)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <div style={{
            background: '#fff',
            borderRadius: '16px',
            padding: '2.5rem 3rem',
            minWidth: 480,
            maxWidth: '90vw',
            position: 'relative',
            boxShadow: '0 4px 32px #0002',
            margin: '2rem',
            maxHeight: '90vh',
            overflow: 'auto',
          }}>
            <button
              onClick={() => setShowBankTransactionsModal(false)}
              style={{
                position: 'absolute',
                top: 18,
                right: 18,
                background: 'none',
                border: 'none',
                fontSize: 28,
                cursor: 'pointer',
                color: '#444',
              }}
              aria-label="Close"
            >×</button>
            <h2 style={{ fontWeight: 700, fontSize: '2rem', textAlign: 'center', marginBottom: '2rem' }}>
              Bank Transactions
            </h2>
            {/* Worker Type Selection */}
            <div style={{ marginBottom: 16 }}>
              <label style={{ fontWeight: 500, marginRight: 8 }}>Select Worker Type:</label>
              <select
                value={bankWorkerType}
                onChange={e => {
                  const type = e.target.value;
                  const found = WORKER_TYPES.find(w => w.value === type);
                  setBankWorkerType(type);
                  setBankWorkerTypeId(found ? found.id : '');
                  setBankPayPeriods([]);
                  setSelectedBankPayPeriod('');
                  setBankTransactions([]);
                  if (type) fetchBankPayPeriods(type);
                }}
                style={{ padding: '0.5rem', borderRadius: 6, border: '1px solid #ccc', fontSize: 16, marginRight: 12 }}
              >
                <option value="">Select Worker Type</option>
                {WORKER_TYPES.map(w => (
                  <option key={w.value} value={w.value}>{w.label}</option>
                ))}
              </select>
            </div>
            {/* Pay Period Selection */}
            <div style={{ marginBottom: 16 }}>
              <label style={{ fontWeight: 500, marginRight: 8 }}>Select Pay Period:</label>
              {loadingBankPeriods ? (
                <span>Loading pay periods...</span>
              ) : bankError ? (
                <span style={{ color: 'red' }}>{bankError}</span>
              ) : (
                <select
                  value={selectedBankPayPeriod}
                  onChange={e => {
                    setSelectedBankPayPeriod(e.target.value);
                    setBankTransactions([]);
                    if (e.target.value) fetchBankTransactions(e.target.value);
                  }}
                  style={{ padding: '0.5rem', borderRadius: 6, border: '1px solid #ccc', fontSize: 16 }}
                  disabled={!bankWorkerType}
                >
                  <option value="">Select Pay Period</option>
                  {bankPayPeriods.map((p, idx) => (
                    <option key={idx} value={p.PayPeriodID || p.payPeriodId || p.PayPeriod}>
                      {p.PayPeriod || p.payPeriod || p.payPeriodId || p.payDate || p.payBeginDate + ' - ' + p.payEndDate}
                    </option>
                  ))}
                </select>
              )}
            </div>
            {/* Transactions Table */}
            {selectedBankPayPeriod && (
              loadingBankTransactions ? (
                <div style={{ textAlign: 'center', margin: '2rem 0' }}>Loading transactions...</div>
              ) : bankError ? (
                <div style={{ color: 'red', marginBottom: 16 }}>{bankError}</div>
              ) : bankTransactions.length === 0 ? (
                <div style={{ color: '#888', marginBottom: 16 }}>No transactions found for this pay period.</div>
              ) : (
                <>
                  <div style={{ overflowX: 'auto', marginBottom: 16 }}>
                    <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: 14, minWidth: 900 }}>
                      <thead>
                        <tr style={{ background: '#f3f3f3' }}>
                          <th style={{ padding: 8, border: '1px solid #eee' }}>Reference ID</th>
                          <th style={{ padding: 8, border: '1px solid #eee' }}>Source</th>
                          <th style={{ padding: 8, border: '1px solid #eee' }}>Source Account</th>
                          <th style={{ padding: 8, border: '1px solid #eee' }}>Destination</th>
                          <th style={{ padding: 8, border: '1px solid #eee' }}>Destination Account</th>
                          <th style={{ padding: 8, border: '1px solid #eee' }}>Transaction Name</th>
                          <th style={{ padding: 8, border: '1px solid #eee' }}>Amount</th>
                          <th style={{ padding: 8, border: '1px solid #eee' }}>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {bankTransactions.map((tx, idx) => (
                          <tr key={tx.requestReferenceId || idx}>
                            <td style={{ padding: 8, border: '1px solid #eee' }}>{tx.requestReferenceId}</td>
                            <td style={{ padding: 8, border: '1px solid #eee' }}>{tx.source}</td>
                            <td style={{ padding: 8, border: '1px solid #eee' }}>{tx.sourceAccount}</td>
                            <td style={{ padding: 8, border: '1px solid #eee' }}>{tx.destination}</td>
                            <td style={{ padding: 8, border: '1px solid #eee' }}>{tx.destinationAccount}</td>
                            <td style={{ padding: 8, border: '1px solid #eee' }}>{tx.transactionName}</td>
                            <td style={{ padding: 8, border: '1px solid #eee' }}>{tx.transferAmount}</td>
                            <td style={{ padding: 8, border: '1px solid #eee' }}>{tx.status}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <button
                    style={{ marginTop: 8, background: '#1976d2', color: '#fff', border: 'none', borderRadius: 6, padding: '0.5rem 1.5rem', fontWeight: 500, cursor: 'pointer' }}
                    onClick={handleDownloadBankTransactionsPDF}
                  >Download as PDF</button>
                </>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportsPage; 
import { create } from 'zustand';

interface EmployeeViewState {
  selectedEmployeeId: string | null;
  isAdminViewingAsEmployee: boolean;
  setSelectedEmployee: (employeeId: string | null) => void;
  setIsAdminViewingAsEmployee: (isAdminViewing: boolean) => void;
  clearSelectedEmployee: () => void;
}

export const useEmployeeViewStore = create<EmployeeViewState>((set) => ({
  // Initialize state from localStorage if available
  selectedEmployeeId: localStorage.getItem('viewingEmployeeId') || null,
  isAdminViewingAsEmployee: localStorage.getItem('currentRole') === 'admin-as-employee',
  
  // Update state and localStorage when setting selected employee
  setSelectedEmployee: (employeeId) => {
    if (employeeId) {
      localStorage.setItem('viewingEmployeeId', employeeId);
    } else {
      localStorage.removeItem('viewingEmployeeId');
    }
    set({ selectedEmployeeId: employeeId });
  },
  
  // Update state and localStorage when setting admin viewing status
  setIsAdminViewingAsEmployee: (isAdminViewing) => {
    if (isAdminViewing) {
      localStorage.setItem('currentRole', 'admin-as-employee');
    } else {
      // Only remove if it's admin-as-employee, don't affect other roles
      if (localStorage.getItem('currentRole') === 'admin-as-employee') {
        localStorage.removeItem('currentRole');
      }
    }
    set({ isAdminViewingAsEmployee: isAdminViewing });
  },
  
  // Clear all employee view state and localStorage
  clearSelectedEmployee: () => {
    localStorage.removeItem('viewingEmployeeId');
    // Only remove if it's admin-as-employee, don't affect other roles
    if (localStorage.getItem('currentRole') === 'admin-as-employee') {
      localStorage.removeItem('currentRole');
    }
    set({ selectedEmployeeId: null, isAdminViewingAsEmployee: false });
  },
}));
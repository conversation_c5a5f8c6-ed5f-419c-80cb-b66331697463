# MCP Server Architecture

## Overview

The Message Control Protocol (MCP) Server is a centralized middleware layer that handles all API communications for the US Payroll application. It provides a consistent interface for making API requests, handles authentication, retries, and error handling.

## Components

### MCPServer

The core server class that provides the foundation for all API communications. It:
- Manages authentication tokens
- <PERSON><PERSON> request formatting
- Implements retry logic
- Provides consistent error handling
- Offers helper methods for different HTTP methods (GET, POST, PUT, DELETE)

### Service Modules

The MCP server is organized into domain-specific service modules:

1. **CompanyService**: Handles company-related API operations
   - Company registration
   - Company updates
   - KYB information
   - Company locations

2. **UserService**: Handles user-related API operations
   - User management
   - User onboarding
   - KYC information
   - User bank accounts

3. **PayrollService**: Handles payroll-related API operations
   - Pay schedules
   - Payroll processing
   - Benefits management
   - Supplemental payroll items

4. **ReportService**: Handles report-related API operations
   - Payroll reports
   - Tax reports
   - Employee reports
   - Company reports

5. **WebhookService**: Handles webhook-related API operations
   - Webhook registration
   - Webhook updates
   - Webhook testing

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                        UI Components                         │
└───────────────────────────────┬─────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────┐
│                         MCP Services                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │CompanyService│  │UserService  │  │PayrollService│         │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│  ┌─────────────┐  ┌─────────────┐                           │
│  │ReportService │  │WebhookService│                          │
│  └─────────────┘  └─────────────┘                           │
└───────────────────────────────┬─────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────┐
│                         MCP Server                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │Authentication│  │Retry Logic  │  │Error Handling│         │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└───────────────────────────────┬─────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────┐
│                         Axios Client                         │
└───────────────────────────────┬─────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────┐
│                         Rollfi API                           │
└─────────────────────────────────────────────────────────────┘
```

## Benefits

1. **Centralized API Logic**: All API communication is handled through a single interface, making it easier to maintain and update.

2. **Consistent Error Handling**: Errors are handled consistently across all API calls.

3. **Automatic Retries**: Transient errors are automatically retried, improving reliability.

4. **Authentication Management**: Authentication tokens are automatically managed and refreshed as needed.

5. **Domain-Specific Services**: API operations are organized into domain-specific services, making the code more modular and easier to understand.

6. **Simplified Component Code**: UI components can use the service modules without worrying about the details of API communication.

## Usage

Components should import and use the service modules rather than making direct API calls. This ensures that all API communication goes through the MCP server and benefits from its features.

Example:
```typescript
import { companyService } from '../mcp';

// Use the service
const result = await companyService.registerCompany({
  company: "Example Company",
  businessWebsite: "www.example.com",
  nacisSubCategoryCode: 6233,
  isEsign: true,
  isTermsAccepted: true
});
```
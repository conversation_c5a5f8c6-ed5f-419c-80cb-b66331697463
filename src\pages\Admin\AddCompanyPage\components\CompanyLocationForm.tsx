import React, { useState, ChangeEvent, FormEvent } from 'react';
import styles from '../AddCompanyPage.module.css';

interface CompanyLocationData {
  companyLocation: string; // Suggested Values : M
  address1: string;
  address2?: string;
  city: string;
  state: string; // State code e.g. CO
  zipcode: string;
  phoneNumber: string;
  isWorkLocation: boolean;
  isMailingAddress: boolean;
  isFilingAddress: boolean;
}

interface CompanyLocationFormProps {
  data: CompanyLocationData;
  onNext: (data: { companyLocation: CompanyLocationData }) => void;
  onBack: () => void;
}

const CompanyLocationForm: React.FC<CompanyLocationFormProps> = ({ data, onNext, onBack }) => {
  const [formData, setFormData] = useState<CompanyLocationData>(data);
  const [errors, setErrors] = useState<Partial<Record<keyof CompanyLocationData, string>>>({});

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
    if (errors[name as keyof CompanyLocationData]) {
      setErrors(prev => ({...prev, [name]: undefined}));
    }
  };

  const validate = (): boolean => {
    const newErrors: Partial<Record<keyof CompanyLocationData, string>> = {};
    if (!formData.companyLocation.trim()) newErrors.companyLocation = 'Company location type is required.';
    if (!formData.address1.trim()) newErrors.address1 = 'Address Line 1 is required.';
    if (!formData.city.trim()) newErrors.city = 'City is required.';
    if (!formData.state.trim()) {
      newErrors.state = 'State is required.';
    } else if (!/^[A-Z]{2}$/.test(formData.state.toUpperCase())) {
      newErrors.state = 'State must be a 2-letter code (e.g., CO).';
    }
    if (!formData.zipcode.trim()) {
      newErrors.zipcode = 'Zipcode is required.';
    } else if (!/^\d{5}(-\d{4})?$/.test(formData.zipcode)) {
      newErrors.zipcode = 'Enter a valid 5-digit or 9-digit zipcode (e.g., 80540 or 80540-1234).';
    }
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required.';
    } else if (!/^\d{10}$/.test(formData.phoneNumber.replace(/\D/g, ''))) {
      newErrors.phoneNumber = 'Enter a valid 10-digit phone number.';
    }
    // No specific validation for checkboxes as they default to true, but they are required fields in the payload.

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    // Ensure state is uppercase before submitting
    const dataToSubmit = { ...formData, state: formData.state.toUpperCase() };
    if (validate()) { // Re-validate with potentially transformed data if needed, though here it's simple
      onNext({ companyLocation: dataToSubmit });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <h2 className={styles.stepTitle}>Step 3: Company Location</h2>

      <div className={styles.formGroup}>
        <label htmlFor="companyLocation">Company Location Type <sup>*</sup></label>
        <input
          type="text"
          id="companyLocation"
          name="companyLocation"
          value={formData.companyLocation}
          onChange={handleChange}
          placeholder="Suggested: M (Main Office)"
        />
        {errors.companyLocation && <p className={styles.errorText}>{errors.companyLocation}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="address1">Address Line 1 <sup>*</sup></label>
        <input
          type="text"
          id="address1"
          name="address1"
          value={formData.address1}
          onChange={handleChange}
        />
        {errors.address1 && <p className={styles.errorText}>{errors.address1}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="address2">Address Line 2</label>
        <input
          type="text"
          id="address2"
          name="address2"
          value={formData.address2 || ''}
          onChange={handleChange}
        />
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="city">City <sup>*</sup></label>
        <input
          type="text"
          id="city"
          name="city"
          value={formData.city}
          onChange={handleChange}
        />
        {errors.city && <p className={styles.errorText}>{errors.city}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="state">State (2-letter code) <sup>*</sup></label>
        <input
          type="text"
          id="state"
          name="state"
          value={formData.state}
          onChange={handleChange}
          maxLength={2}
          placeholder="e.g., CO"
        />
        {errors.state && <p className={styles.errorText}>{errors.state}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="zipcode">Zipcode <sup>*</sup></label>
        <input
          type="text"
          id="zipcode"
          name="zipcode"
          value={formData.zipcode}
          onChange={handleChange}
          placeholder="e.g., 80540 or 80540-1234"
        />
        {errors.zipcode && <p className={styles.errorText}>{errors.zipcode}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="phoneNumber">Phone Number <sup>*</sup></label>
        <input
          type="tel"
          id="phoneNumber"
          name="phoneNumber"
          value={formData.phoneNumber}
          onChange={handleChange}
          placeholder="9874563210"
        />
        {errors.phoneNumber && <p className={styles.errorText}>{errors.phoneNumber}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="isWorkLocation">
          <input
            type="checkbox"
            id="isWorkLocation"
            name="isWorkLocation"
            checked={formData.isWorkLocation}
            onChange={handleChange}
          />
          Is this a work location? <sup>*</sup>
        </label>
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="isMailingAddress">
          <input
            type="checkbox"
            id="isMailingAddress"
            name="isMailingAddress"
            checked={formData.isMailingAddress}
            onChange={handleChange}
          />
          Is this a mailing address? <sup>*</sup>
        </label>
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="isFilingAddress">
          <input
            type="checkbox"
            id="isFilingAddress"
            name="isFilingAddress"
            checked={formData.isFilingAddress}
            onChange={handleChange}
          />
          Is this a filing address? <sup>*</sup>
        </label>
      </div>

      <div className={styles.buttonContainer}>
        <button type="button" onClick={onBack} className={styles.secondary}>Back</button>
        <button type="submit" className={styles.primary}>Next</button>
      </div>
    </form>
  );
};

export default CompanyLocationForm;
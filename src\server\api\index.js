import express from 'express';
import cors from 'cors';
import { Configuration, PlaidApi, PlaidEnvironments } from 'plaid';
import dotenv from 'dotenv';
dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());

const configuration = new Configuration({
  basePath: PlaidEnvironments.sandbox,
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': process.env.PLAID_CLIENT_ID,
      'PLAID-SECRET': process.env.PLAID_SECRET,
    },
  },
});
const client = new PlaidApi(configuration);

app.post('/exchange_public_token', async (req, res) => {
  const { public_token } = req.body;
  const tokenResponse = await client.itemPublicTokenExchange({ public_token });
  res.json(tokenResponse.data);
});

app.post('/transactions', async (req, res) => {
  const { access_token } = req.body;
  const response = await client.transactionsGet({
    access_token,
    start_date: '2024-01-01',
    end_date: '2025-12-31',
  });
  res.json(response.data.transactions);
});

app.post('/create_link_token', async (req, res) => {
  try {
    const response = await client.linkTokenCreate({
      user: { client_user_id: 'test-user-123' },
      client_name: 'Your App Name',
      products: ['transactions'],
      country_codes: ['US'],
      language: 'en',
      webhook: 'https://webhook.site/da2cdf69-ec45-4682-bdd8-a3b1cb11aff4' // <-- Updated webhook.site URL
    });
    res.json({ link_token: response.data.link_token });
  } catch (err) {
    console.error('Plaid error:', err.response?.data || err.message || err);
    res.status(500).json({ error: err.message });
  }
});

app.post('/simulate_payroll', async (req, res) => {
  const { access_token, net_pays } = req.body;
  try {
    // If net_pays is provided and is an array, simulate a webhook for each net pay
    if (Array.isArray(net_pays) && net_pays.length > 0) {
      const results = [];
      for (const amount of net_pays) {
        // Optionally, you can log or use the amount for more advanced simulation
        const response = await client.sandboxItemFireWebhook({
          access_token,
          webhook_code: 'DEFAULT_UPDATE',
          webhook_type: 'TRANSACTIONS',
        });
        results.push(response.data);
      }
      res.json({ success: true, results });
    } else {
      // Fallback: single simulation
      const response = await client.sandboxItemFireWebhook({
        access_token,
        webhook_code: 'DEFAULT_UPDATE',
        webhook_type: 'TRANSACTIONS',
      });
      res.json(response.data);
    }
  } catch (err) {
    console.error('Plaid simulate_payroll error:', err.response?.data || err.message || err);
    res.status(500).json({ error: err.message });
  }
});

app.listen(8000, () => console.log('Plaid test server running on port 8000'));

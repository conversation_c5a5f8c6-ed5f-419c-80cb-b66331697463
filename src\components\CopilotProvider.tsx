import React, { useEffect, useState } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotSidebar } from '@copilotkit/react-ui';
import { PromptManager } from '../services/promptManager';
import { copilotService } from '../services/copilotService';
import '@copilotkit/react-ui/styles.css';
import { useAuth } from '../context/AuthProvider';

interface CopilotProviderProps {
  children: React.ReactNode;
}

const CopilotProvider: React.FC<CopilotProviderProps> = ({ children }) => {
  const [dynamicPrompt, setDynamicPrompt] = useState('');
  const { userProfile } = useAuth();

  useEffect(() => {
    const updatePrompt = async () => {
      try {
        const context = await copilotService.getFullContext();
        const userData = context.userData || { note: 'No user logged in' };
        const companyData = context.company;
        // Always provide all actions, regardless of role
        const availableActions = [
          'View dashboard',
          'Manage users',
          'Process payroll',
          'View reports',
          'Update my details',
          'View documents',
          'View paystubs',
          'Add team member',
          'Add company',
        ];
        const prompt = PromptManager.generatePrompt({
          currentPage: window.location.pathname,
          userRole: userData?.role || 'user',
          availableActions,
          userData,
          companyData
        });
        setDynamicPrompt(prompt);
      } catch (error) {
        console.error('Error updating prompt:', error);
        // Set a default prompt if there's an error
        const defaultPrompt = PromptManager.generatePrompt({
          currentPage: window.location.pathname,
          userRole: 'user',
          availableActions: [
            'View dashboard',
            'Get help',
            'Manage users',
            'Process payroll',
            'View reports',
            'Update my details',
            'View documents',
            'View paystubs',
            'Add team member',
            'Add company',
          ],
          userData: { note: 'No user logged in' },
          companyData: null
        });
        setDynamicPrompt(defaultPrompt);
      }
    };
    updatePrompt();
    const handleRouteChange = () => {
      updatePrompt();
    };
    window.addEventListener('popstate', handleRouteChange);
    return () => window.removeEventListener('popstate', handleRouteChange);
  }, [userProfile]);

  const fullPrompt = `${dynamicPrompt}`;

  return (
    <CopilotKit 
      publicApiKey={import.meta.env.VITE_COPILOT_PUBLIC_API_KEY}
    >
      {children}
      <CopilotSidebar
        labels={{
          title: "Payroll AI Agent",
          placeholder: "Ask me anything about your account...",
          initial: "Need help with anything?"
        }}
        instructions={fullPrompt}
        displayMode="popup"
        popupPosition="center"
        className="custom-copilot-popup"
      />
    </CopilotKit>
  );
};

export default CopilotProvider;

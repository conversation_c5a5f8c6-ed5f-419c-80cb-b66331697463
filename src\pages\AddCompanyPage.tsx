import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { employerService, CompanyData } from '../services/employerService';

// Step 1: Registration Information
interface RegistrationFormData {
  company: string;
  businessWebsite: string;
  nacisSubCategoryCode: number | string;
  isEsign: boolean;
  isTermsAccepted: boolean;
}

// Step 2: KYB Information
interface KybFormData {
  ein: string;
  entityType: string;
  dateOfIncorporation: string;
  irsAssisgnedFederalFilingForm: string;
}

// Step 3: Company Location
interface LocationFormData {
  companyLocation: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zipcode: string;
  phoneNumber: string;
  isWorkLocation: boolean;
  isMailingAddress: boolean;
  isFilingAddress: boolean;
}

// Step 4: Business User
interface BusinessUserFormData {
  firstName: string;
  middleName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zipcode: string;
  ssn: string;
  dateOfBirth: string;
  payrollAdmin: boolean;
  bookKeeper: boolean;
  beneficialOwner: boolean;
  ownershipPercentage: number | string;
}

const AddCompanyPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Form data for each step
  const [registrationData, setRegistrationData] = useState<RegistrationFormData>({
    company: '',
    businessWebsite: '',
    nacisSubCategoryCode: '',
    isEsign: false,
    isTermsAccepted: false
  });
  
  const [kybData, setKybData] = useState<KybFormData>({
    ein: '',
    entityType: '',
    dateOfIncorporation: '',
    irsAssisgnedFederalFilingForm: ''
  });
  
  const [locationData, setLocationData] = useState<LocationFormData>({
    companyLocation: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    zipcode: '',
    phoneNumber: '',
    isWorkLocation: false,
    isMailingAddress: false,
    isFilingAddress: false
  });
  
  const [businessUserData, setBusinessUserData] = useState<BusinessUserFormData>({
    firstName: '',
    middleName: '',
    lastName: '',
    phoneNumber: '',
    email: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    zipcode: '',
    ssn: '',
    dateOfBirth: '',
    payrollAdmin: false,
    bookKeeper: false,
    beneficialOwner: false,
    ownershipPercentage: ''
  });

  // Handle input changes for each step
  const handleRegistrationChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setRegistrationData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  const handleKybChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Special handling for EIN to ensure proper format
    if (name === 'ein') {
      // Remove any non-digit characters to ensure plain 9-digit number
      let cleanValue = value.replace(/\D/g, '');
      
      // Limit to 9 digits
      if (cleanValue.length > 9) {
        cleanValue = cleanValue.substring(0, 9);
      }
      
      setKybData(prev => ({
        ...prev,
        [name]: cleanValue
      }));
    } else {
      setKybData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  const handleLocationChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setLocationData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  const handleBusinessUserChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    if (name === 'ssn') {
      // Remove any non-digit characters for SSN
      const cleanValue = value.replace(/\D/g, '').substring(0, 9);
      
      setBusinessUserData(prev => ({
        ...prev,
        [name]: cleanValue
      }));
    } else {
      setBusinessUserData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  // Navigation between steps
  const nextStep = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    }
  };
  
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Validate form data before submission
  const validateForm = (): { isValid: boolean; errorMessage?: string } => {
    // Clean the EIN to ensure it's only digits
    const cleanEIN = kybData.ein.replace(/\D/g, '');
    
    // Validate EIN length (must be exactly 9 digits)
    if (cleanEIN.length !== 9) {
      return { 
        isValid: false, 
        errorMessage: 'EIN must be 9 digits' 
      };
    }
    
    // If the EIN has any non-digit characters, update it to be clean
    if (kybData.ein !== cleanEIN) {
      setKybData(prev => ({
        ...prev,
        ein: cleanEIN
      }));
    }
    
    return { isValid: true };
  };

  // Submit the form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSuccess(null);
    setError(null); // Clear previous errors
    
    // Validate form before submission
    const validation = validateForm();
    if (!validation.isValid) {
      setError(validation.errorMessage);
      
      // If EIN is invalid, go to KYB step
      if (validation.errorMessage?.includes('EIN')) {
        setCurrentStep(2);
      }
      
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }
    
    try {
      // Clean the EIN and SSN to ensure they're only digits
      const cleanEIN = kybData.ein.replace(/\D/g, '');
      const cleanSSN = businessUserData.ssn.replace(/\D/g, '');
      
      // Final validation check for EIN and SSN
      if (cleanEIN.length !== 9) {
        setError('EIN must be 9 digits');
        setCurrentStep(2);
        window.scrollTo({ top: 0, behavior: 'smooth' });
        return;
      }
      
      if (cleanSSN.length !== 9) {
        setError('SSN must be 9 digits');
        setCurrentStep(4);
        window.scrollTo({ top: 0, behavior: 'smooth' });
        return;
      }
      
      // Prepare the data for API submission
      const companyData: CompanyData = {
        registration: {
          ...registrationData,
          nacisSubCategoryCode: Number(registrationData.nacisSubCategoryCode)
        },
        kybInformation: {
          ...kybData,
          ein: cleanEIN // Ensure clean EIN
        },
        companyLocation: locationData,
        businessUser: {
          ...businessUserData,
          ssn: cleanSSN, // Ensure clean SSN
          ownershipPercentage: Number(businessUserData.ownershipPercentage)
        }
      };
      
      console.log('Submitting company data:', companyData);
      const result = await employerService.addCompany(companyData);
      
      // Check if the result contains an error object (additional check)
      if (result && result.error) {
        throw new Error(result.error.message || 'An error occurred during company creation');
      }
      
      console.log('Company created successfully:', result);
      setSuccess('Company added successfully!');
      
      // Navigate back to company page after a delay only on success
      setTimeout(() => navigate('/employer/company'), 2000);
    } catch (err: any) {
      console.error('Failed to create company:', err);
      
      // Format the error message to be more user-friendly
      let displayErrorMessage: string;
      const errMessage = (err as any)?.message ?? (typeof err === 'string' ? err : 'An unknown error occurred.');

      if (errMessage.includes('Authentication token not found')) {
        displayErrorMessage = 'Authentication error: Please log out and log back in to refresh your session.';
      } else if (errMessage.includes('EIN')) {
        // Special handling for EIN errors - direct user to the KYB step
        displayErrorMessage = `Error: ${errMessage}`;
        setCurrentStep(2); // Go directly to KYB step where EIN is entered
      } else {
        displayErrorMessage = `Error: ${errMessage}`;
      }
      
      setError(displayErrorMessage as string | null);
      
      // Show an alert for immediate attention
      alert(`Error: ${displayErrorMessage}`);
      
      // Scroll to the top to make sure the error is visible
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return renderRegistrationStep();
      case 2:
        return renderKybStep();
      case 3:
        return renderLocationStep();
      case 4:
        return renderBusinessUserStep();
      case 5:
        return renderReviewStep();
      default:
        return renderRegistrationStep();
    }
  };

  // Step 1: Registration Information
  const renderRegistrationStep = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Step 1: Registration Information</h2>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Company Name <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          name="company"
          value={registrationData.company}
          onChange={handleRegistrationChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Business Website <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          name="businessWebsite"
          value={registrationData.businessWebsite}
          onChange={handleRegistrationChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          NACIS Sub-Category Code <span className="text-red-500">*</span>
        </label>
        <input
          type="number"
          name="nacisSubCategoryCode"
          value={registrationData.nacisSubCategoryCode}
          onChange={handleRegistrationChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>
      
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="isEsign"
          name="isEsign"
          checked={registrationData.isEsign}
          onChange={handleRegistrationChange}
          className="h-4 w-4 text-teal-600 border-gray-300 rounded"
          required
        />
        <label htmlFor="isEsign" className="text-sm font-medium text-gray-700">
          E-Sign Agreement <span className="text-red-500">*</span>
        </label>
      </div>
      
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="isTermsAccepted"
          name="isTermsAccepted"
          checked={registrationData.isTermsAccepted}
          onChange={handleRegistrationChange}
          className="h-4 w-4 text-teal-600 border-gray-300 rounded"
          required
        />
        <label htmlFor="isTermsAccepted" className="text-sm font-medium text-gray-700">
          Accept Terms and Conditions <span className="text-red-500">*</span>
        </label>
      </div>
      
      <div className="flex justify-end space-x-4 pt-4">
        <button
          type="button"
          onClick={() => navigate('/employer/company')}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={nextStep}
          className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
        >
          Next
        </button>
      </div>
    </div>
  );

  // Step 2: KYB Information
  const renderKybStep = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Step 2: KYB Information</h2>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          EIN <span className="text-red-500">*</span>
        </label>
        <div>
          <input
            type="text"
            name="ein"
            value={kybData.ein}
            onChange={handleKybChange}
            className={`w-full px-3 py-2 border ${error && error.includes('EIN') ? 'border-red-500' : 'border-gray-300'} rounded-md`}
            placeholder="123456789"
            pattern="[0-9]{9}"
            title="EIN must be 9 digits"
            required
            maxLength={9}
          />
          {error && error.includes('EIN') ? (
            <p className="text-red-500 text-sm mt-1">
              EIN must be 9 digits
            </p>
          ) : (
            <p className="text-gray-500 text-xs mt-1">
              Enter 9 digits (Example: 123456789)
            </p>
          )}
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Entity Type <span className="text-red-500">*</span>
        </label>
        <select
          name="entityType"
          value={kybData.entityType}
          onChange={handleKybChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        >
          <option value="">Select Entity Type</option>
          <option value="LLC">LLC</option>
          <option value="LLP">LLP</option>
          <option value="Corporation">Corporation</option>
          <option value="S-Corporation">S-Corporation</option>
          <option value="Partnership">Partnership</option>
          <option value="Sole Proprietorship">Sole Proprietorship</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Date of Incorporation <span className="text-red-500">*</span>
        </label>
        <input
          type="date"
          name="dateOfIncorporation"
          value={kybData.dateOfIncorporation}
          onChange={handleKybChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          IRS Assigned Federal Filing Form <span className="text-red-500">*</span>
        </label>
        <select
          name="irsAssisgnedFederalFilingForm"
          value={kybData.irsAssisgnedFederalFilingForm}
          onChange={handleKybChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        >
          <option value="">Select Form</option>
          <option value="941">941</option>
          <option value="944">944</option>
          <option value="940">940</option>
          <option value="943">943</option>
          <option value="945">945</option>
        </select>
      </div>
      
      <div className="flex justify-between space-x-4 pt-4">
        <button
          type="button"
          onClick={prevStep}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Previous
        </button>
        <button
          type="button"
          onClick={nextStep}
          className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
        >
          Next
        </button>
      </div>
    </div>
  );

  // Step 3: Company Location
  const renderLocationStep = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Step 3: Company Location</h2>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Company Location <span className="text-red-500">*</span>
        </label>
        <select
          name="companyLocation"
          value={locationData.companyLocation}
          onChange={handleLocationChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        >
          <option value="">Select Location Type</option>
          <option value="M">Main</option>
          <option value="B">Branch</option>
          <option value="H">Home Office</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Address Line 1 <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          name="address1"
          value={locationData.address1}
          onChange={handleLocationChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Address Line 2
        </label>
        <input
          type="text"
          name="address2"
          value={locationData.address2}
          onChange={handleLocationChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
        />
      </div>
      
      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            City <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="city"
            value={locationData.city}
            onChange={handleLocationChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            State <span className="text-red-500">*</span>
          </label>
          <select
            name="state"
            value={locationData.state}
            onChange={handleLocationChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          >
            <option value="">Select State</option>
            {['TX', 'AK', 'ND', 'IL', 'MA', 'KS', 'AS', 'AL', 'UT', 'SD', 'MI', 'DC', 'GA', 'SC', 'TN', 'OK', 'CO', 'CA', 'ME', 'MO', 'WY', 'MD', 'VT', 'NM', 'CT', 'AZ', 'WI', 'VA', 'WV', 'NJ', 'DE', 'NC', 'PR', 'IN', 'MT', 'NH', 'HI', 'IA', 'KY', 'WA', 'OH', 'FL', 'OR', 'MP', 'GU', 'MN', 'MS', 'PA', 'NE', 'RI', 'ID', 'LA', 'NY', 'NV', 'AR'].map(state => (
              <option key={state} value={state}>{state}</option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Zipcode <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="zipcode"
            value={locationData.zipcode}
            onChange={handleLocationChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number <span className="text-red-500">*</span>
          </label>
          <input
            type="tel"
            name="phoneNumber"
            value={locationData.phoneNumber}
            onChange={handleLocationChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="isWorkLocation"
            name="isWorkLocation"
            checked={locationData.isWorkLocation}
            onChange={handleLocationChange}
            className="h-4 w-4 text-teal-600 border-gray-300 rounded"
          />
          <label htmlFor="isWorkLocation" className="text-sm font-medium text-gray-700">
            Work Location <span className="text-red-500">*</span>
          </label>
        </div>
        
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="isMailingAddress"
            name="isMailingAddress"
            checked={locationData.isMailingAddress}
            onChange={handleLocationChange}
            className="h-4 w-4 text-teal-600 border-gray-300 rounded"
          />
          <label htmlFor="isMailingAddress" className="text-sm font-medium text-gray-700">
            Mailing Address <span className="text-red-500">*</span>
          </label>
        </div>
        
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="isFilingAddress"
            name="isFilingAddress"
            checked={locationData.isFilingAddress}
            onChange={handleLocationChange}
            className="h-4 w-4 text-teal-600 border-gray-300 rounded"
          />
          <label htmlFor="isFilingAddress" className="text-sm font-medium text-gray-700">
            Filing Address <span className="text-red-500">*</span>
          </label>
        </div>
      </div>
      
      <div className="flex justify-between space-x-4 pt-4">
        <button
          type="button"
          onClick={prevStep}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Previous
        </button>
        <button
          type="button"
          onClick={nextStep}
          className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
        >
          Next
        </button>
      </div>
    </div>
  );

  // Step 4: Business User
  const renderBusinessUserStep = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Step 4: Business User</h2>
      
      <div className="grid grid-cols-3 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            First Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="firstName"
            value={businessUserData.firstName}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Middle Name
          </label>
          <input
            type="text"
            name="middleName"
            value={businessUserData.middleName}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Last Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="lastName"
            value={businessUserData.lastName}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number <span className="text-red-500">*</span>
          </label>
          <input
            type="tel"
            name="phoneNumber"
            value={businessUserData.phoneNumber}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            name="email"
            value={businessUserData.email}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Address Line 1 <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          name="address1"
          value={businessUserData.address1}
          onChange={handleBusinessUserChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Address Line 2
        </label>
        <input
          type="text"
          name="address2"
          value={businessUserData.address2}
          onChange={handleBusinessUserChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
        />
      </div>
      
      <div className="grid grid-cols-3 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            City <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="city"
            value={businessUserData.city}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            State <span className="text-red-500">*</span>
          </label>
          <select
            name="state"
            value={businessUserData.state}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          >
            <option value="">Select State</option>
            {['TX', 'AK', 'ND', 'IL', 'MA', 'KS', 'AS', 'AL', 'UT', 'SD', 'MI', 'DC', 'GA', 'SC', 'TN', 'OK', 'CO', 'CA', 'ME', 'MO', 'WY', 'MD', 'VT', 'NM', 'CT', 'AZ', 'WI', 'VA', 'WV', 'NJ', 'DE', 'NC', 'PR', 'IN', 'MT', 'NH', 'HI', 'IA', 'KY', 'WA', 'OH', 'FL', 'OR', 'MP', 'GU', 'MN', 'MS', 'PA', 'NE', 'RI', 'ID', 'LA', 'NY', 'NV', 'AR'].map(state => (
              <option key={state} value={state}>{state}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Zipcode <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="zipcode"
            value={businessUserData.zipcode}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            SSN <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="ssn"
            value={businessUserData.ssn}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="123456789"
            pattern="[0-9]{9}"
            title="SSN must be 9 digits"
            maxLength={9}
            required
          />
          <p className="text-gray-500 text-xs mt-1">
            Enter 9 digits (Example: 123456789)
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date of Birth <span className="text-red-500">*</span>
          </label>
          <input
            type="date"
            name="dateOfBirth"
            value={businessUserData.dateOfBirth}
            onChange={handleBusinessUserChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="payrollAdmin"
            name="payrollAdmin"
            checked={businessUserData.payrollAdmin}
            onChange={handleBusinessUserChange}
            className="h-4 w-4 text-teal-600 border-gray-300 rounded"
          />
          <label htmlFor="payrollAdmin" className="text-sm font-medium text-gray-700">
            Payroll Admin <span className="text-red-500">*</span>
          </label>
        </div>
        
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="bookKeeper"
            name="bookKeeper"
            checked={businessUserData.bookKeeper}
            onChange={handleBusinessUserChange}
            className="h-4 w-4 text-teal-600 border-gray-300 rounded"
          />
          <label htmlFor="bookKeeper" className="text-sm font-medium text-gray-700">
            Book Keeper <span className="text-red-500">*</span>
          </label>
        </div>
        
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="beneficialOwner"
            name="beneficialOwner"
            checked={businessUserData.beneficialOwner}
            onChange={handleBusinessUserChange}
            className="h-4 w-4 text-teal-600 border-gray-300 rounded"
          />
          <label htmlFor="beneficialOwner" className="text-sm font-medium text-gray-700">
            Beneficial Owner <span className="text-red-500">*</span>
          </label>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Ownership Percentage <span className="text-red-500">*</span>
        </label>
        <input
          type="number"
          name="ownershipPercentage"
          value={businessUserData.ownershipPercentage}
          onChange={handleBusinessUserChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          min="0"
          max="100"
          required
        />
      </div>
      
      <div className="flex justify-between space-x-4 pt-4">
        <button
          type="button"
          onClick={prevStep}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Previous
        </button>
        <button
          type="button"
          onClick={nextStep}
          className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
        >
          Review
        </button>
      </div>
    </div>
  );

  // Step 5: Review
  const renderReviewStep = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Step 5: Review and Submit</h2>
      
      <div className="bg-gray-50 p-4 rounded-md">
        <h3 className="font-medium text-lg mb-2">Registration Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Company Name</p>
            <p>{registrationData.company}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Business Website</p>
            <p>{registrationData.businessWebsite}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">NACIS Sub-Category Code</p>
            <p>{registrationData.nacisSubCategoryCode}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Agreements</p>
            <p>
              {registrationData.isEsign ? '✓ E-Sign Agreement' : '✗ E-Sign Agreement'}<br />
              {registrationData.isTermsAccepted ? '✓ Terms Accepted' : '✗ Terms Accepted'}
            </p>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-50 p-4 rounded-md">
        <h3 className="font-medium text-lg mb-2">KYB Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">EIN</p>
            <p>{kybData.ein}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Entity Type</p>
            <p>{kybData.entityType}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Date of Incorporation</p>
            <p>{kybData.dateOfIncorporation}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">IRS Filing Form</p>
            <p>{kybData.irsAssisgnedFederalFilingForm}</p>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-50 p-4 rounded-md">
        <h3 className="font-medium text-lg mb-2">Company Location</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Location Type</p>
            <p>{locationData.companyLocation}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Phone Number</p>
            <p>{locationData.phoneNumber}</p>
          </div>
          <div className="col-span-2">
            <p className="text-sm text-gray-500">Address</p>
            <p>
              {locationData.address1}<br />
              {locationData.address2 && <>{locationData.address2}<br /></>}
              {locationData.city}, {locationData.state} {locationData.zipcode}
            </p>
          </div>
          <div className="col-span-2">
            <p className="text-sm text-gray-500">Address Type</p>
            <p>
              {locationData.isWorkLocation ? '✓ Work Location' : '✗ Work Location'}<br />
              {locationData.isMailingAddress ? '✓ Mailing Address' : '✗ Mailing Address'}<br />
              {locationData.isFilingAddress ? '✓ Filing Address' : '✗ Filing Address'}
            </p>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-50 p-4 rounded-md">
        <h3 className="font-medium text-lg mb-2">Business User</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Name</p>
            <p>{businessUserData.firstName} {businessUserData.middleName} {businessUserData.lastName}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Contact</p>
            <p>{businessUserData.email}<br />{businessUserData.phoneNumber}</p>
          </div>
          <div className="col-span-2">
            <p className="text-sm text-gray-500">Address</p>
            <p>
              {businessUserData.address1}<br />
              {businessUserData.address2 && <>{businessUserData.address2}<br /></>}
              {businessUserData.city}, {businessUserData.state} {businessUserData.zipcode}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Personal Information</p>
            <p>
              SSN: {businessUserData.ssn}<br />
              DOB: {businessUserData.dateOfBirth}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Roles</p>
            <p>
              {businessUserData.payrollAdmin ? '✓ Payroll Admin' : '✗ Payroll Admin'}<br />
              {businessUserData.bookKeeper ? '✓ Book Keeper' : '✗ Book Keeper'}<br />
              {businessUserData.beneficialOwner ? '✓ Beneficial Owner' : '✗ Beneficial Owner'}<br />
              Ownership: {businessUserData.ownershipPercentage}%
            </p>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between space-x-4 pt-4">
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => navigate('/admin')}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Cancel & Return to Admin
          </button>
          <button
            type="button"
            onClick={prevStep}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Previous
          </button>
        </div>
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => navigate('/admin')}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={() => setCurrentStep(1)}
            className="px-4 py-2 text-teal-600 border border-teal-600 rounded-md hover:bg-teal-50"
          >
            Edit All
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
          >
            Submit
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex-1 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-semibold mb-6">Add New Company</h1>
        
        {error && (
          <div className="mb-4 p-4 bg-red-100 border-l-4 border-red-500 text-red-700 rounded shadow-md">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <svg className="h-6 w-6 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span className="font-medium">Error: {error}</span>
              </div>
              <div className="flex space-x-2">
                <button 
                  onClick={() => navigate('/admin')} 
                  className="px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
                >
                  Cancel
                </button>
                {error.includes('EIN') && (
                  <button 
                    onClick={() => setCurrentStep(2)} 
                    className="px-3 py-1 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 text-sm"
                  >
                    Fix EIN
                  </button>
                )}
                <button 
                  onClick={() => setCurrentStep(1)} 
                  className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
                >
                  Edit All Information
                </button>
              </div>
            </div>
            <p className="mt-2">Please correct the information and try again, or contact support if the issue persists.</p>
          </div>
        )}
        {success && <div className="mb-4 p-3 bg-green-100 text-green-700 rounded shadow-md">{success}</div>}
        
        <div className="mb-8">
          <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((step) => (
              <React.Fragment key={step}>
                <div 
                  className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    currentStep >= step ? 'bg-teal-600 text-white' : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {step}
                </div>
                {step < 5 && (
                  <div 
                    className={`flex-1 h-1 mx-2 ${
                      currentStep > step ? 'bg-teal-600' : 'bg-gray-200'
                    }`}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
          <div className="flex justify-between mt-2">
            <span className="text-xs">Registration</span>
            <span className="text-xs">KYB Info</span>
            <span className="text-xs">Location</span>
            <span className="text-xs">User</span>
            <span className="text-xs">Review</span>
          </div>
        </div>
        
        <form className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          {renderStep()}
        </form>
      </div>
    </div>
  );
};

export default AddCompanyPage;

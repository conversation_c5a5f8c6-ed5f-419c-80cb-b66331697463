import React, { useState, ChangeEvent, FormEvent } from 'react';
import styles from '../AddCompanyPage.module.css'; // Using shared styles for now

interface RegistrationData {
  company: string;
  businessWebsite: string;
  nacisSubCategoryCode: number | ''; // Allow empty string for initial state
  isEsign: boolean;
  isTermsAccepted: boolean;
}

interface RegistrationFormProps {
  data: RegistrationData;
  onNext: (data: { registration: RegistrationData }) => void;
}

const RegistrationForm: React.FC<RegistrationFormProps> = ({ data, onNext }) => {
  const [formData, setFormData] = useState<RegistrationData>(data);
  const [errors, setErrors] = useState<Partial<Record<keyof RegistrationData, string>>>({});

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else if (name === 'nacisSubCategoryCode') {
        setFormData((prev) => ({ ...prev, [name]: value === '' ? '' : Number(value) }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
    // Clear error for this field on change
    if (errors[name as keyof RegistrationData]) {
      setErrors(prev => ({...prev, [name]: undefined}));
    }
  };

  const validate = (): boolean => {
    const newErrors: Partial<Record<keyof RegistrationData, string>> = {};
    if (!formData.company.trim()) newErrors.company = 'Company name is required.';
    if (!formData.businessWebsite.trim()) {
      newErrors.businessWebsite = 'Business website is required.';
    } else if (!/^www\.[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/.test(formData.businessWebsite) && !/^https?:\/\/(www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(\/.*)?$/.test(formData.businessWebsite)) {
      newErrors.businessWebsite = 'Enter a valid website URL (e.g., www.example.com or http(s)://example.com).';
    }
    if (formData.nacisSubCategoryCode === '' || formData.nacisSubCategoryCode <= 0) {
        newErrors.nacisSubCategoryCode = 'NAICS Sub-Category Code is required and must be a positive number.';
    }
    if (!formData.isTermsAccepted) newErrors.isTermsAccepted = 'You must accept the terms and conditions.';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (validate()) {
      onNext({ registration: formData });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <h2 className={styles.stepTitle}>Step 1: Registration Information</h2>
      
      <div className={styles.formGroup}>
        <label htmlFor="company">Company Name <sup>*</sup></label>
        <input
          type="text"
          id="company"
          name="company"
          value={formData.company}
          onChange={handleChange}
        />
        {errors.company && <p className={styles.errorText}>{errors.company}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="businessWebsite">Business Website <sup>*</sup></label>
        <input
          type="text"
          id="businessWebsite"
          name="businessWebsite"
          value={formData.businessWebsite}
          onChange={handleChange}
          placeholder="www.example.com or https://example.com"
        />
        {errors.businessWebsite && <p className={styles.errorText}>{errors.businessWebsite}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="nacisSubCategoryCode">NAICS Sub-Category Code <sup>*</sup></label>
        <input
          type="number"
          id="nacisSubCategoryCode"
          name="nacisSubCategoryCode"
          value={formData.nacisSubCategoryCode}
          onChange={handleChange}
        />
        {errors.nacisSubCategoryCode && <p className={styles.errorText}>{errors.nacisSubCategoryCode}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="isEsign">
          <input
            type="checkbox"
            id="isEsign"
            name="isEsign"
            checked={formData.isEsign}
            onChange={handleChange}
          />
          Enable E-Sign <sup>*</sup>
        </label>
        {/* No explicit error message for checkbox, but it's part of the payload */}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="isTermsAccepted">
          <input
            type="checkbox"
            id="isTermsAccepted"
            name="isTermsAccepted"
            checked={formData.isTermsAccepted}
            onChange={handleChange}
          />
          I accept the terms and conditions <sup>*</sup>
        </label>
        {errors.isTermsAccepted && <p className={styles.errorText}>{errors.isTermsAccepted}</p>}
      </div>

      <div className={styles.buttonContainer}>
        {/* No Back button on the first step */}
        <div></div> 
        <button type="submit" className={styles.primary}>Next</button>
      </div>
    </form>
  );
};

export default RegistrationForm;